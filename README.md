# Solana Trading Bot

A sophisticated Rust-based trading bot for Solana that implements a copy-trading strategy with MEV protection. The bot monitors specified wallets, analyzes their trading patterns, and executes trades using Raydium pools with Jito MEV protection.

## Features

- **Copy Trading**: Monitors and copies trades from specified wallet addresses
- **MEV Protection**: Uses Jito MEV for protected transaction execution
- **Advanced Sell Strategy**:
  - Multiple configurable price targets with different sell percentages
  - Stop-loss protection
  - Timeout-based selling
  - Target wallet following (sells when target sells)
  - Trailing stop functionality
- **Real-time Monitoring**:
  - Tracks wallet balances
  - Analyzes recent transactions (within 10 seconds)
  - Monitors price movements
- **Error Handling**: Robust error handling and retry mechanisms
- **Logging**: Detailed logging with timestamps for monitoring and debugging

## Prerequisites

- Rust and Cargo (latest stable version)
- Solana CLI tools
- Access to a Solana RPC endpoint
- Jito MEV subscription (for MEV protection)

## Setup

1. Clone the repository:

```bash
git clone https://github.com/yourusername/solana-bot.git
cd solana-bot
```

2. Create a `.env` file with the following configuration:

```env
RPC_URL=your_solana_rpc_endpoint
```

3. Configure tracked wallets and sell parameters in `config.rs`

4. Build the project:

```bash
cargo build --release
```

5. Run the bot:

```bash
cargo run --release --bin track_wallet
```

## Configuration

The bot's behavior can be customized through several configuration options:

### Wallet Tracking

- Configure wallets to track in `TRACKED_WALLETS`
- Bot monitors SOL balance changes > 2 SOL for potential trades

### Sell Strategy Configuration

Configure `SELL_CONFIGS` with parameters for each sell target:

- `target_multiplier`: Price target multiplier
- `sell_percentage`: Percentage to sell at target
- `previous_target`: Previous target for stop loss
- `stop_loss_multiplier`: Stop loss trigger multiplier
- `timeout_minutes`: Maximum hold time after hitting target

## Safety Features

- Transaction protection via Jito MEV
- Configurable stop-loss levels
- Balance checks before trading
- Timeout protection for stale positions

## Monitoring

The bot provides detailed logging including:

- Trade entries and exits
- Price movements and multipliers
- Balance changes
- Error conditions

## Disclaimer

Trading cryptocurrencies involves significant risk. This bot is provided as-is with no guarantees. Always test thoroughly with small amounts before deploying with significant capital.

## License

[Add your license information here]

## Binaries

### get_associated_addresses

This binary helps identify associated EOA (Externally Owned Account) wallet addresses that have bidirectional transfers with a given wallet for a specific token, while filtering out DEX and program-controlled accounts.

```
Usage: get_associated_addresses [OPTIONS] --wallet <WALLET> --token <TOKEN>

Options:
  -w, --wallet <WALLET>              Wallet address to analyze
  -t, --token <TOKEN>                Token address to analyze
  -p, --max-pages <MAX_PAGES>        Number of pages to request [default: 10]
  -m, --min-transfers <MIN_TRANSFERS> Minimum number of transfers (both in and out) to consider an address as associated [default: 1]
  -o, --include-one-way              Include wallets with one-way transfers (either in or out)
  --min-one-way-transfers <MIN_ONE_WAY_TRANSFERS>
                                     Minimum number of transfers for one-way relationships [default: 3]
  --include-token-creators           Include token creators (don't exclude them in initial filtering)
  --log-level <LOG_LEVEL>            Set log level (trace, debug, info, warn, error) [default: info]
  -h, --help                         Print help

```

Example:
```bash
# Find bidirectional transfers only
cargo run --bin get_associated_addresses -- --wallet 8xLBuzgvbtbWxTXRvZg9jQ9mwnwfnEHKoA3GZqXCsfRx --token 2t7SanEtAjf35FERcfzzSTo8rajgV6YZWZYVVoRZpump --max-pages 5 --min-transfers 1

# Include one-way transfers
cargo run --bin get_associated_addresses -- --wallet 8xLBuzgvbtbWxTXRvZg9jQ9mwnwfnEHKoA3GZqXCsfRx --token 2t7SanEtAjf35FERcfzzSTo8rajgV6YZWZYVVoRZpump --include-one-way --min-one-way-transfers 1

# Include token creators (for token launches)
cargo run --bin get_associated_addresses -- --wallet B9JrUwERBRU2Pi2eLU15q6fT8SSUHffqqfcogFomY944 --token 2t7SanEtAjf35FERcfzzSTo8rajgV6YZWZYVVoRZpump --include-one-way --min-one-way-transfers 1 --include-token-creators
```

The tool performs the following steps:
1. Gets the associated token account for the wallet and token
2. Requests SPL transfer data from Solscan while excluding DEX-related addresses
3. Identifies EOA wallets that have both inflows and outflows with the target wallet
4. Optionally identifies EOA wallets with only in-transfers or only out-transfers of the target token (ignoring SOL/USDC)
5. Filters out program-controlled accounts like Jupiter Aggregator wallets
6. Outputs a table of associated addresses with transfer counts, grouped by transfer type (bidirectional or one-way)

This helps identify human-controlled wallets that might be related to the target wallet by analyzing token flow patterns.

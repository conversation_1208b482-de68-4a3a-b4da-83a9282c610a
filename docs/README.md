# Solana Trading Bot Documentation

## Overview

This project implements a Solana-based trading bot that interacts with Raydium liquidity pools to monitor and calculate token prices. The bot is specifically designed to work with the ELON/SOL trading pair but can be adapted for other tokens.

## Project Structure

```
solana-bot/
├── src/
│   ├── main.rs       # Entry point and main application logic
│   ├── price.rs      # SOL price fetching from CoinGecko
│   ├── raydium.rs    # Raydium pool interaction and price calculation
│   ├── token_metadata.rs # Token metadata retrieval
│   └── wallet.rs     # Wallet management and authentication
```

## Core Components

### Main Application (`main.rs`)

- Initializes the Solana RPC client connection
- Sets up wallet context from environment variables
- Coordinates price fetching from multiple sources
- Calculates final token prices in both SOL and USD

### Price Module (`price.rs`)

- Fetches SOL/USD price from CoinGecko API
- Implements rate limiting and error handling
- Uses custom user agent to avoid API restrictions

### Raydium Integration (`raydium.rs`)

- Implements Raydium pool state deserialization using Borsh
- Calculates token prices based on pool liquidity
- Handles decimal adjustments for accurate price representation
- Accounts for swap fees in price calculations

### Token Metadata (`token_metadata.rs`)

- Retrieves token metadata from Solana's Token Metadata Program
- Provides token symbol and decimal information
- Implements error handling for metadata deserialization

### Wallet Management (`wallet.rs`)

- Manages Solana wallet keypair
- Loads private key from environment variables
- Provides signing capabilities for transactions

## Key Features

### Price Calculation

The bot calculates token prices using multiple data points:

1. Raydium pool liquidity data
2. Current SOL/USD price from CoinGecko
3. Pool fees and decimal adjustments

### Error Handling

- Comprehensive error handling using Rust's Result type
- Custom error messages for better debugging
- Graceful handling of API failures and network issues

### Logging

- Multiple log levels (debug, info, error)
- Detailed logging for debugging and monitoring
- Performance-sensitive logging with debug level for detailed information

## Configuration

### Environment Variables

- `PRIVATE_KEY`: Base58-encoded private key for the wallet
- Loaded using the `dotenv` crate for development

### Constants

```rust
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
const RAYDIUM_POOL: &str = "5c4znipkjwGe9PFJay3EswDr3kxAmg2FX1CJsggybEjh";
const ELON_MINT: &str = "BftUiGB2iDkNDa8AKdhtLuJHHXiUfqLvTNmiXaSopump";
```

## Technical Details

### Raydium Pool State

The bot interacts with Raydium pools by deserializing the following state structure:

```rust
#[derive(BorshDeserialize, BorshSerialize, Debug)]
#[repr(C)]
pub struct RaydiumPoolState {
    pub status: u64,
    pub nonce: u64,
    pub order_num: u64,
    // ... (other fields)
}
```

### Price Calculation Formula

```
price = (base_balance / base_decimals) / (quote_balance / quote_decimals) * fee_multiplier
```

where:

- `fee_multiplier = 1.0 - (fee_numerator / fee_denominator)`
- `base_balance` and `quote_balance` are the current pool liquidity
- `base_decimals` and `quote_decimals` are token-specific decimal places

## Usage

1. Set up environment:

   ```bash
   cp .env.example .env
   # Edit .env and add your private key
   ```

2. Run the bot:

   ```bash
   cargo run
   ```

## Dependencies

Major dependencies include:

- `solana-sdk`: Solana blockchain interaction
- `solana-client`: RPC client functionality
- `borsh`: Binary serialization
- `spl-token`: Solana Program Library token functionality
- `mpl-token-metadata`: Metaplex token metadata program

## Security Considerations

1. Private Key Management
   - Private keys are loaded from environment variables
   - Never commit .env files to version control
   - Use secure key storage in production

2. API Security
   - Custom user agent for API requests
   - Rate limiting consideration
   - Error handling for API failures

## Future Improvements

1. Trading Strategy Implementation
   - Add buy/sell order execution
   - Implement price movement tracking
   - Add support for multiple trading pairs

2. Enhanced Monitoring
   - WebSocket price updates
   - Historical price tracking
   - Performance metrics

3. Configuration
   - Make more parameters configurable
   - Support for multiple RPC endpoints
   - Dynamic fee calculation

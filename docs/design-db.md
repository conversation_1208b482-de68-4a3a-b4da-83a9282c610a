# Solana Data Infra DB Design

# Technical Design Document: Solana Token Transfer Ingestion Service

|  |  |
| --- | --- |
| **Title** | Solana Token Transfer Ingestion Service |
| **Author** | Gemini |
| **Status** | Final |
| **Date** | June 13, 2025 |
| **Scope** | This document details the design of the back-end data writer component and the PostgreSQL database setup for ingesting and storing Solana token transfer events at high throughput. |

## 1. Overview

This system is designed to reliably capture all SPL token transfer events from the Solana blockchain and persist them into a PostgreSQL database. The primary goal is to create a scalable and durable data pipeline capable of handling a continuous, high-volume stream of events (estimated at 700+ records/second) while supporting efficient queries for transaction history.

The architecture emphasizes resilience, fault tolerance, and the ability to perform system upgrades (e.g., updating parsing logic) with zero downtime for data ingestion.

## 2. Goals & Requirements

### 2.1. Functional Requirements

- **FR1:** Ingest all SPL token transfer events from a Solana Geyser plugin stream.
- **FR2:** Store event data including transaction signature, slot, wallet addresses, token mint, and amount.
- **FR3:** Ensure data integrity and prevent duplicate records, even if source events are delivered more than once.
- **FR4:** Support efficient queries to retrieve the recent transaction history for a given wallet and/or token, with pagination.

### 2.2. Non-Functional Requirements

- **NFR1:** **High Throughput:** Sustain an ingestion rate of at least 700 records per second.
- **NFR2:** **Scalability:** The system must be able to scale to handle future increases in transaction volume.
- **NFR3:** **Durability & Resilience:** No data loss during transient network issues, component restarts, or deployments.
- **NFR4:** **Idempotency:** The data writing process must be idempotent.
- **NFR5:** **Maintainability:** The database schema must be manageable at a multi-terabyte scale, allowing for efficient data archival.

## 3. System Architecture

The system employs a decoupled, asynchronous architecture to maximize resilience and scalability.

```
graph TD
    A[Solana Validator w/ Geyser Plugin] -->|gRPC Stream| B(Stream Processor);
    B -->|Parsed Events| C{Google Cloud Pub/Sub Topic};
    C -->|Messages| D[Data Writer Service (Golang on K8s)];
    D -->|Batched Writes (COPY)| E[(PostgreSQL on Cloud SQL)];

    subgraph "Data Source"
        A
    end

    subgraph "Real-time Processing"
        B
    end

    subgraph "Buffering / Decoupling"
        C
    end

    subgraph "Data Persistence"
        D
        E
    end
```

**Component Responsibilities:**

1. **Geyser Plugin:** Streams raw transaction metadata from a Solana validator.
2. **Stream Processor:** A lightweight service that subscribes to the Geyser gRPC stream. Its sole responsibility is to parse the raw metadata, identify token transfers, derive a unique ix_index from the ordered transaction logs, and publish a structured event to a Pub/Sub topic.
3. **Cloud Pub/Sub:** Acts as a durable, scalable buffer. It decouples the data source from the database, absorbing spikes in volume and allowing the Data Writer to be taken offline for upgrades without losing data.
4. **Data Writer (Golang on K8s):** The core component of this design. A scalable set of consumers that read batches of events from Pub/Sub and write them to PostgreSQL using an efficient, idempotent pattern.
5. **PostgreSQL (Cloud SQL):** The authoritative, long-term data store, optimized for time-series data and large-scale queries.

## 4. Database Design & Setup (PostgreSQL on Cloud SQL)

### 4.1. Schema Definition

The schema is designed for storage efficiency by using binary data types and a composite primary key for uniqueness.

```
CREATE TABLE token_transfers (
    -- Core Event Data
    signature      BYTEA NOT NULL,
    ix_index.      INT NOT NULL,
    slot           BIGINT NOT NULL,
    token_mint     BYTEA NOT NULL,
    from_wallet    BYTEA NOT NULL,
    to_wallet      BYTEA NOT NULL,
    amount         BIGINT NOT NULL,

    -- Composite primary key for guaranteed uniqueness and idempotency
    PRIMARY KEY (signature, ix_index)
) PARTITION BY RANGE (slot);
```

**Key Decisions & Rationale:**

- **Data Types:** `BYTEA` for addresses/signatures and `BIGINT` for amounts are used instead of `TEXT`.
  - **Reason:** This significantly reduces storage requirements (~20% savings) and improves indexing efficiency compared to variable-length strings.
- **Primary Key `(signature,`** ix_index**`)`:** A composite key is used.
  - **Reason:** A transaction signature alone is not unique, as a single transaction can contain multiple transfers. The ix_index, derived from the ordered position in the transaction's logs, is required to uniquely identify each event. This is critical for preventing data loss and ensuring correct idempotency.

### 4.2. Partitioning Strategy

The `token_transfers` table will be partitioned by **RANGE** on the `slot` column.

- **Decision:** Partitions will be created on a **weekly basis**.
- **Reason:**
  - **Query Performance:** Partitioning is the single most important optimization for this use case. It allows the database to perform "partition pruning," where queries for recent data only scan the latest, smallest partitions instead of the entire multi-terabyte table.
  - **Maintenance:** Archiving old data is trivial and instantaneous (`DETACH PARTITION`) instead of a slow, table-locking `DELETE` command.
  - **Sizing:** A weekly partition size (estimated at ~85 GB / ~1.2-1.5 million slots) is the ideal balance. It's large enough to avoid the query planning overhead of having too many small partitions, yet small enough for its indexes to fit comfortably in memory on a well-provisioned server.

### 4.3. Indexing Strategy

In addition to the primary key index, the following indexes are required.

```
-- 1. To efficiently query for transfers SENT FROM a wallet for a specific token
CREATE INDEX idx_from_wallet_token_mint_slot ON token_transfers (from_wallet, token_mint, slot DESC);

-- 2. To efficiently query for transfers SENT TO a wallet for a specific token
CREATE INDEX idx_to_wallet_token_mint_slot ON token_transfers (to_wallet, token_mint, slot DESC);
```

**Key Decisions & Rationale:**

- **Composite Indexes:** The indexes are composite and ordered to match the most critical query pattern (`WHERE wallet = ? AND token = ? ORDER BY slot DESC`).
- **Lean Indexing:** We intentionally omit separate indexes on `(wallet, slot DESC)`. A multicolumn index `(A, B, C)` can be used for queries on `(A, B)`. While less efficient than a dedicated index, it is often sufficient.
  - **Reason:** Every additional index slows down write performance and consumes significant disk space. The recommendation is to start with these two essential indexes and only add more if `EXPLAIN ANALYZE` on real-world queries proves they are a bottleneck.

### 4.4. Cloud SQL Instance Configuration

- **Edition:** **Cloud SQL Enterprise Plus**
  - **Reason:** Provides superior write performance, lower replication lag, and near-zero downtime maintenance, which are mandatory for this mission-critical, high-throughput workload.
- **Machine Type:** Start with **`db-n2-highmem-16`** (16 vCPU, 128 GB RAM).
  - **Reason:** High write throughput and large-scale indexing are both CPU and memory intensive. This provides a robust starting point.
- **Storage:** **4 TB SSD** with auto-growth enabled.
  - **Reason:** IOPS performance scales with disk size in Cloud SQL. Provisioning a large disk ensures high IOPS to handle the write and indexing load.
- **High Availability (HA):** Disabled.
  - **Reason:** Too expensive. May enable in the future if server availability becomes more critical
- **Key Flags:**
  - `shared_buffers`: `32GB` (25% of RAM)
  - `maintenance_work_mem`: `2GB` (Speeds up index creation/vacuuming)
  - `checkpoint_completion_target`: `0.9` (Smooths out write I/O)
  - `autovacuum_vacuum_scale_factor`: `0.05` (Ensures timely cleanup on a huge, active table)

## 5. Data Writer Component Design (Golang on Kubernetes)

### 5.1. Core Logic & Idempotency

The writer will use a **`Temp Table -> COPY -> INSERT ON CONFLICT`** pattern for each batch of messages.

1. `BEGIN` a transaction.
2. `CREATE TEMPORARY TABLE temp_batch (LIKE token_transfers) ON COMMIT DROP;`
3. Use the `pgx.CopyFrom` interface to efficiently stream the batch of decoded events into `temp_batch`.
4. Execute `INSERT INTO token_transfers SELECT * FROM temp_batch ON CONFLICT (signature, event_index) DO NOTHING;`
5. `COMMIT` the transaction.

**Reason:** This pattern is the most performant way to achieve idempotent bulk-writes in PostgreSQL. It leverages the raw speed of the `COPY` protocol while using the atomic conflict-handling capabilities of `INSERT`, avoiding the performance penalty of row-by-row `INSERT` statements.

### 5.2. Concurrency & Connection Management

- **Driver:** Use the **`pgx/v5`** library.
  - **Reason:** It is the modern, high-performance standard for Go, with native support for the `COPY` protocol and advanced PostgreSQL features.
- **Connection Pooling:** Use **`pgxpool`**.
  - **Reason:** A connection pool is mandatory for managing concurrent database access from scaled K8s pods. It prevents overwhelming the database with connection requests and provides a controllable gate (`MaxConns`) on total database concurrency.

### 5.3. Scaling & Backpressure

The system is designed with multiple layers of backpressure to prevent overwhelming the database during backfills or traffic spikes.

1. **Pub/Sub as a Buffer:** The primary shock absorber. If writers slow down, messages queue safely in the subscription.
2. **Client-Side Flow Control:** The Go Pub/Sub client library's `MaxOutstandingMessages` setting will be configured.
    - **Reason:** This provides automatic, graceful backpressure. The client will stop pulling new messages if the application cannot `Ack()` them quickly enough (because the database is slow), preventing the writer pods from running out of memory.
3. **K8s Horizontal Pod Autoscaler (HPA):** The writer deployment will be configured with an HPA based on Pub/Sub subscription depth, but with a **`maxReplicas`** limit.
    - **Reason:** This allows the system to scale to meet demand but prevents a "thundering herd" scenario where an unlimited number of pods could try to connect to the database.

### 5.4. Graceful Shutdowns

The application must handle `SIGTERM` signals from Kubernetes.

- **Mechanism:** Use `context.Context` throughout the application. A top-level context will be canceled upon receiving a `SIGTERM`.
- **Reason:** This allows in-flight database operations and message acknowledgements to complete cleanly, preventing data loss or redelivery during routine deployments and scaling events.

## 6. Backfill Strategy

To backfill historical data, the same ingestion pipeline will be used. The backfill process will generate historical event data and publish it to the same Pub/Sub topic.

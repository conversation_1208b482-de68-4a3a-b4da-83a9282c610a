# API Documentation

## Core Modules

### Price Module

#### `get_sol_price`

```rust
pub async fn get_sol_price() -> Result<f64>
```

Fetches the current SOL/USD price from CoinGecko.

**Returns:**

- `Ok(f64)`: The current SOL price in USD
- `Err`: If the API request fails or response parsing fails

**Example:**

```rust
let sol_price = get_sol_price().await?;
println!("Current SOL price: ${}", sol_price);
```

### Raydium Module

#### `RaydiumPoolState`

```rust
#[derive(BorshDeserialize, BorshSerialize, Debug)]
#[repr(C)]
pub struct RaydiumPoolState {
    pub status: u64,
    pub nonce: u64,
    pub order_num: u64,
    // ... other fields
}
```

Represents the state of a Raydium liquidity pool.

#### `get_raydium_price`

```rust
pub async fn get_raydium_price(
    rpc_client: &RpcClient,
    pool_pubkey: &Pubkey
) -> Result<f64>
```

Calculates token price from Raydium pool data.

**Parameters:**

- `rpc_client`: Solana RPC client
- `pool_pubkey`: Public key of the Raydium pool

**Returns:**

- `Ok(f64)`: Token price in SOL
- `Err`: If pool data cannot be fetched or price calculation fails

**Example:**

```rust
let pool_pubkey = Pubkey::from_str(RAYDIUM_POOL)?;
let price = get_raydium_price(&client, &pool_pubkey).await?;
```

### Token Metadata Module

#### `get_token_info`

```rust
pub async fn get_token_info(
    client: &RpcClient,
    mint_address: &str
) -> Result<(String, u8)>
```

Retrieves token metadata from Solana.

**Parameters:**

- `client`: Solana RPC client
- `mint_address`: Token mint address as string

**Returns:**

- `Ok((String, u8))`: Tuple of token symbol and decimals
- `Err`: If metadata cannot be fetched or parsed

**Example:**

```rust
let (symbol, decimals) = get_token_info(&client, ELON_MINT).await?;
```

### Wallet Module

#### `WalletContext`

```rust
pub struct WalletContext {
    keypair: Keypair,
}
```

Manages wallet operations and signing.

**Methods:**

##### `new`

```rust
pub fn new() -> Result<Self>
```

Creates new wallet context from environment variables.

##### `payer`

```rust
pub fn payer(&self) -> Pubkey
```

Returns the wallet's public key.

##### `signer`

```rust
pub fn signer(&self) -> &Keypair
```

Returns a reference to the signing keypair.

**Example:**

```rust
let wallet = WalletContext::new()?;
println!("Wallet address: {}", wallet.payer());
```

## Constants

### Token Constants

```rust
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
const ELON_MINT: &str = "BftUiGB2iDkNDa8AKdhtLuJHHXiUfqLvTNmiXaSopump";
```

### Pool Constants

```rust
const RAYDIUM_POOL: &str = "5c4znipkjwGe9PFJay3EswDr3kxAmg2FX1CJsggybEjh";
```

## Error Handling

All functions return `anyhow::Result<T>` for consistent error handling:

```rust
use anyhow::{anyhow, Result};

// Example error creation
Err(anyhow!("Failed to parse SOL price"))
```

## Logging

The application uses the `log` crate with multiple levels:

```rust
use log::{debug, error, info};

// Examples
debug!("Detailed information for debugging");
info!("General information about program execution");
error!("Error information when something goes wrong");
```

## Environment Variables

Required environment variables:

- `PRIVATE_KEY`: Base58-encoded private key for the wallet

Example `.env` file:

```
PRIVATE_KEY=your_private_key_here
```

## Type Definitions

### Common Types

```rust
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::Keypair;
use solana_client::rpc_client::RpcClient;
```

### Custom Results

```rust
type Result<T> = std::result::Result<T, anyhow::Error>;
```

## Usage Examples

### Basic Price Check

```rust
async fn check_prices() -> Result<()> {
    let client = RpcClient::new_with_commitment(
        "https://api.mainnet-beta.solana.com".to_string(),
        CommitmentConfig::confirmed()
    );
    
    let pool_pubkey = Pubkey::from_str(RAYDIUM_POOL)?;
    let raydium_price = get_raydium_price(&client, &pool_pubkey).await?;
    let sol_price = get_sol_price().await?;
    
    println!("Token price in SOL: {}", raydium_price);
    println!("Token price in USD: {}", raydium_price * sol_price);
    
    Ok(())
}
```

### Token Information

```rust
async fn get_token_details(client: &RpcClient) -> Result<()> {
    let (symbol, decimals) = get_token_info(client, ELON_MINT).await?;
    println!("Token: {} with {} decimals", symbol, decimals);
    Ok(())
}
```

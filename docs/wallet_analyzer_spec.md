# Wallet Analyzer Specification

## Overview

The Wallet Analyzer is a system designed to analyze Solana wallet transfer patterns and identify associated wallets based on transfer activity. It helps identify relationships between wallets by analyzing their transfer history and patterns.

## Core Data Structures

### TransferActivity

```go
type TransferActivity struct {
    BlockID        uint64  `json:"block_id"`
    TransID        string  `json:"trans_id"`
    BlockTime      uint64  `json:"block_time"`
    ActivityType   string  `json:"activity_type"`
    FromAddress    string  `json:"from_address"`
    FromTokenAccount string `json:"from_token_account"`
    ToAddress      string  `json:"to_address"`
    ToTokenAccount string  `json:"to_token_account"`
    TokenAddress   string  `json:"token_address"`
    TokenDecimals  uint8   `json:"token_decimals"`
    Amount         uint64  `json:"amount"`
    Flow           string  `json:"flow"`
    Value          float64 `json:"value"`
}
```

### AccountMetadata

```go
type AccountMetadata struct {
    AccountAddress string   `json:"account_address"`
    AccountLabel   *string  `json:"account_label,omitempty"`
    AccountTags    []string `json:"account_tags,omitempty"`
    AccountType    *string  `json:"account_type,omitempty"`
    AccountIcon    *string  `json:"account_icon,omitempty"`
}
```

### TransferResponse

```go
type TransferResponse struct {
    Success bool                    `json:"success"`
    Data    []TransferActivity     `json:"data"`
    Metadata struct {
        Accounts map[string]AccountMetadata `json:"accounts"`
    } `json:"metadata"`
}
```

### TransferCounter

```go
type TransferCounter struct {
    InTransfers  int
    OutTransfers int
}
```

### AssociatedWallet

```go
type AssociatedWallet struct {
    Address         string
    InTransfers     int
    OutTransfers    int
    IsBidirectional bool
    Tags           []string
    Label          string
}
```

### CachedWalletAnalysis

```go
type CachedWalletAnalysis struct {
    Timestamp         uint64
    AssociatedWallets []AssociatedWallet
}
```

## Configuration

### WalletAnalyzer Configuration

```go
type WalletAnalyzerConfig struct {
    Token              string
    MaxPages          int
    MinTransfers      int
    IncludeOneWay     bool
    MinOneWayTransfers int
    IncludeTokenCreators bool
}
```

## Core Functions

### 1. Wallet Analysis

```go
func (wa *WalletAnalyzer) AnalyzeWallet(wallet string) ([]AssociatedWallet, error)
```

- Checks cache for existing analysis
- Validates wallet is on Solana curve
- Fetches transfer data from Solscan API
- Identifies and excludes program accounts and known bad actors
- Analyzes transfer patterns
- Caches results
- Returns list of associated wallets

### 2. Transfer Fetching

```go
func fetchTransfers(client *http.Client, wallet, solToken, token, usdcToken string, toExclude, fromExclude []string, page int) (*TransferResponse, error)
```

- Constructs API request to Solscan
- Handles rate limiting and retries
- Manages authentication
- Returns transfer data

### 3. Account Exclusion Logic

```go
func shouldExcludeAccount(wallet string, account *AccountMetadata, includeTokenCreators bool) bool
```

- Checks against known exclusions list
- Validates wallet is on curve
- Analyzes account tags and labels
- Excludes program accounts and known bad actors

### 4. Cache Management

```go
func (wa *WalletAnalyzer) getCachePath(wallet string) string
func (wa *WalletAnalyzer) readFromCache(wallet string) ([]AssociatedWallet, error)
func (wa *WalletAnalyzer) writeToCache(wallet string, associatedWallets []AssociatedWallet) error
```

- Manages local cache of analysis results
- Implements 24-hour cache expiration
- Handles cache file I/O

## Key Features

1. **Transfer Pattern Analysis**
   - Identifies bidirectional transfers
   - Tracks one-way transfer patterns
   - Filters out program accounts and known bad actors

2. **Account Classification**
   - Labels accounts based on transfer patterns
   - Identifies token creators
   - Categorizes accounts by activity type

3. **Performance Optimizations**
   - Implements caching system
   - Parallel processing for multiple wallets
   - Rate limiting and retry logic

4. **Security Features**
   - Validates wallet addresses
   - Excludes known bad actors
   - Filters program accounts

## API Integration

### Solscan API

- Base URL: <https://api-v2.solscan.io/v2/account/transfer>
- Required Headers:
  - Accept: application/json
  - Origin: <https://solscan.io>
  - Referer: <https://solscan.io/>
  - User-Agent: [Browser User Agent]
  - Token: [Authentication Token]
  - Cookie: cf_clearance=[Cloudflare Token]

### Rate Limiting

- Implements exponential backoff
- Maximum 2 retry attempts
- 1-second initial delay
- 5/4 multiplier for subsequent delays

## Error Handling

1. **API Errors**
   - Handles rate limiting
   - Manages authentication failures
   - Implements retry logic

2. **Validation Errors**
   - Invalid wallet addresses
   - Off-curve public keys
   - Malformed responses

3. **Cache Errors**
   - File I/O failures
   - JSON parsing errors
   - Cache expiration

## Dependencies

1. **External Libraries**
   - HTTP client for API requests
   - JSON handling
   - Base58 encoding/decoding
   - Ed25519 curve validation

2. **Internal Dependencies**
   - Browser automation for authentication
   - Slack notification system
   - Logging system

## Implementation Notes

1. **Concurrency**
   - Use goroutines for parallel processing
   - Implement semaphore for rate limiting
   - Handle concurrent cache access

2. **Memory Management**
   - Implement pagination for large datasets
   - Use pointers for large structures
   - Clear unused data

3. **Error Propagation**
   - Use Go's error handling patterns
   - Implement custom error types
   - Proper error wrapping

4. **Testing**
   - Unit tests for core functions
   - Integration tests for API calls
   - Mock external dependencies

## Usage Example

```go
func main() {
    config := WalletAnalyzerConfig{
        Token: "YOUR_TOKEN_ADDRESS",
        MaxPages: 5,
        MinTransfers: 3,
        IncludeOneWay: true,
        MinOneWayTransfers: 2,
        IncludeTokenCreators: false,
    }
    
    analyzer := NewWalletAnalyzer(config)
    associatedWallets, err := analyzer.AnalyzeWallet("WALLET_ADDRESS")
    if err != nil {
        log.Fatal(err)
    }
    
    // Process results
    for _, wallet := range associatedWallets {
        fmt.Printf("Associated Wallet: %s\n", wallet.Address)
        fmt.Printf("In Transfers: %d\n", wallet.InTransfers)
        fmt.Printf("Out Transfers: %d\n", wallet.OutTransfers)
        fmt.Printf("Bidirectional: %v\n", wallet.IsBidirectional)
    }
}
```

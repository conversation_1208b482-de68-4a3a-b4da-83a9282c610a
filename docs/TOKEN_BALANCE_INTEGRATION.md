# Token Balance History Integration

This document describes the integration between the Rust Axum server and the Go `CalculateTokenBalanceHistory` function.

## Overview

The integration allows the Axum web server to call the Go function that calculates historical token balance points for suspect wallets. This provides a complete workflow:

1. **Get Suspect Wallets**: Identify suspect wallets using cluster analysis
2. **Calculate Balance History**: Get historical balance data for those wallets using the Go function
3. **Return Combined Results**: Provide both wallet addresses and their balance history

## API Endpoints

### 1. Basic Endpoint (Backward Compatible)

**POST** `/analyze/suspect_wallets_balances`

Request body:
```json
{
    "token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
}
```

### 2. Enhanced Endpoint (Full Configuration)

**POST** `/analyze/suspect_wallets_balances_enhanced`

Request body:
```json
{
    "token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "from_slot": 250000000,
    "rpc_url": "https://api.mainnet-beta.solana.com"
}
```

## Response Format

Both endpoints return the same response format:

```json
{
    "status": "success",
    "data": {
        "token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        "suspect_wallets": [
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
            "2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S"
        ],
        "balance_history": {
            "balance_points": [
                {
                    "timestamp": 1678901234,
                    "balance": 1000000
                },
                {
                    "timestamp": 1678902345,
                    "balance": 1500000
                }
            ],
            "total_current_balance": 2500000,
            "num_intervals": 2,
            "from_slot": 250000000
        }
    }
}
```

## Environment Variables

### Required for Go Binary

The following environment variables must be set for the Go binary to work:

```bash
# Database connection
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=solana_data
export DB_PASSWORD=your_password
export DB_NAME=solana_data
export DB_SSL_MODE=disable

# Optional: Custom RPC URL
export RPC_URL=https://api.mainnet-beta.solana.com

# Optional: Custom Go binary path
export GO_TOKEN_BALANCE_BINARY=/path/to/your/token-balance
```

### Default Values

If environment variables are not set, the following defaults are used:

- `DB_HOST`: localhost
- `DB_PORT`: 5432
- `DB_USER`: solana_data
- `DB_PASSWORD`: (empty)
- `DB_NAME`: solana_data
- `DB_SSL_MODE`: disable
- `from_slot`: 250000000 (if not specified in request)

## Setup Instructions

### 1. Build the Go Binary

```bash
cd /Users/<USER>/git/kg-solana-data/data-writer/cmd/token-balance
go build -o token-balance .
```

### 2. Set Environment Variables

Create a `.env` file or set environment variables:

```bash
export DB_HOST=your_db_host
export DB_PORT=5432
export DB_USER=solana_data
export DB_PASSWORD=your_password
export DB_NAME=solana_data
export GO_TOKEN_BALANCE_BINARY=/path/to/token-balance
```

### 3. Start the Rust Server

```bash
cargo run --bin token_analysis_server
```

## Usage Examples

### Using curl

```bash
# Basic request
curl -X POST http://localhost:8088/analyze/suspect_wallets_balances \
  -H "Content-Type: application/json" \
  -d '{"token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}'

# Enhanced request with custom parameters
curl -X POST http://localhost:8088/analyze/suspect_wallets_balances_enhanced \
  -H "Content-Type: application/json" \
  -d '{
    "token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
    "from_slot": 250000000,
    "rpc_url": "https://api.mainnet-beta.solana.com"
  }'
```

### Using JavaScript/TypeScript

```typescript
const response = await fetch('http://localhost:8088/analyze/suspect_wallets_balances_enhanced', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    token: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    from_slot: 250000000,
    rpc_url: 'https://api.mainnet-beta.solana.com'
  })
});

const data = await response.json();
console.log('Suspect wallets:', data.data.suspect_wallets);
console.log('Balance history:', data.data.balance_history.balance_points);
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

### 500 Internal Server Error

```json
{
    "status": "error",
    "message": "Error analyzing token: ..."
}
```

Common error scenarios:
- Go binary not found
- Database connection issues
- Invalid token mint address
- RPC endpoint unavailable

## Architecture Notes

### Integration Method

The integration uses subprocess calls to execute the Go binary, which is the most straightforward approach given the existing architecture. The Rust server:

1. Creates temporary files for wallet addresses
2. Calls the Go binary with appropriate arguments
3. Reads the JSON output from the Go binary
4. Cleans up temporary files
5. Returns the formatted response

### Performance Considerations

- Temporary files are created in `/tmp/` with unique process IDs
- Files are automatically cleaned up after use
- The Go binary handles its own database connections and RPC calls
- Each request spawns a new Go process (suitable for moderate load)

### Security Considerations

- Temporary files use process IDs to avoid conflicts
- Environment variables are passed securely to the subprocess
- File cleanup is performed even if errors occur
- Input validation is performed on both Rust and Go sides

## Troubleshooting

### Common Issues

1. **Go binary not found**: Set `GO_TOKEN_BALANCE_BINARY` environment variable
2. **Database connection failed**: Check database environment variables
3. **Permission denied**: Ensure the Go binary is executable
4. **Temporary file issues**: Ensure `/tmp/` directory is writable

### Debugging

Enable debug logging in the Rust server:
```bash
RUST_LOG=debug cargo run --bin token_analysis_server
```

Check Go binary directly:
```bash
./token-balance --help
```

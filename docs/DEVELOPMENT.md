# Development Guide

## Setting Up Development Environment

### Prerequisites

- Rust toolchain (latest stable version)
- Solana CLI tools
- Git

### Initial Setup

1. Clone the repository
2. Install dependencies:

   ```bash
   cargo build
   ```

3. Create a `.env` file with required configuration

## Code Organization

### Module Structure

#### `main.rs`

Entry point that ties all components together. Responsible for:

- Initialization of components
- Coordination of price fetching
- High-level error handling

#### `price.rs`

Handles external price data fetching:

```rust
pub async fn get_sol_price() -> Result<f64>
```

- Uses reqwest with custom user agent
- Implements error handling for API responses
- Returns SOL/USD price

#### `raydium.rs`

Core Raydium integration:

```rust
pub async fn get_raydium_price(rpc_client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64>
```

- Deserializes pool state
- Calculates token prices
- Handles pool fees

#### `token_metadata.rs`

Token metadata handling:

```rust
pub async fn get_token_info(client: &RpcClient, mint_address: &str) -> Result<(String, u8)>
```

- Fetches token metadata from Solana
- Returns symbol and decimals

#### `wallet.rs`

Wallet management:

```rust
pub struct WalletContext {
    keypair: Keypair,
}
```

- Loads and manages keypair
- Provides signing functionality

## Development Workflow

### Adding New Features

1. Create new module if needed
2. Implement core functionality
3. Add error handling
4. Update main.rs to integrate new feature
5. Add appropriate logging

### Error Handling Pattern

Use anyhow::Result for error propagation:

```rust
use anyhow::{anyhow, Result};

fn some_function() -> Result<T> {
    // Implementation
    if error_condition {
        return Err(anyhow!("Descriptive error message"));
    }
    Ok(result)
}
```

### Logging Best Practices

Use appropriate log levels:

- `error!`: For errors that need immediate attention
- `info!`: For important state changes
- `debug!`: For detailed debugging information

Example:

```rust
use log::{debug, error, info};

info!("Starting important operation");
debug!("Detailed state: {:?}", state);
if let Err(e) = operation() {
    error!("Operation failed: {}", e);
}
```

## Testing

### Unit Tests

Add tests in the same file as the code:

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_function() {
        // Test implementation
    }
}
```

### Integration Tests

Create tests in the `tests` directory for testing multiple components together.

## Common Development Tasks

### Adding New Token Support

1. Add token mint address to constants
2. Update pool address if needed
3. Verify token metadata handling
4. Test price calculations

### Updating Raydium Integration

1. Check pool state structure matches latest version
2. Update deserialization logic if needed
3. Test with actual pool data
4. Verify price calculations

### Implementing New Price Sources

1. Create new module for price source
2. Implement error handling
3. Add rate limiting if needed
4. Integrate with main price calculation

## Debugging Tips

### Common Issues

1. RPC Connection Issues
   - Check RPC endpoint status
   - Verify network connectivity
   - Check rate limiting

2. Pool Data Issues
   - Verify pool address
   - Check pool state structure
   - Log pool data for inspection

3. Price Calculation Issues
   - Log intermediate values
   - Verify decimal handling
   - Check fee calculations

### Useful Commands

Debug build with symbols:

```bash
cargo build --debug
```

Run with debug logging:

```bash
RUST_LOG=debug cargo run
```

## Performance Considerations

### Memory Usage

- Use appropriate types for numbers
- Clean up resources properly
- Avoid unnecessary cloning

### Network Optimization

- Implement caching where appropriate
- Use connection pooling
- Handle rate limiting properly

### Async Operations

- Use tokio for async operations
- Implement proper error handling
- Consider timeout mechanisms

## Security Guidelines

### Private Key Handling

- Never log private keys
- Use secure environment variables
- Implement proper key rotation

### API Security

- Use HTTPS for external APIs
- Implement rate limiting
- Validate all external data

### Transaction Security

- Verify transaction parameters
- Implement proper signing
- Check for sufficient funds

use anyhow::Result;
use log::error;
use solana_client::rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient};
use solana_client::rpc_config::RpcTransactionConfig;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status::{
    option_serializer::OptionSerializer, EncodedTransaction, UiMessage, UiTransactionEncoding,
};
use std::{str::FromStr, thread::sleep, time::Duration};

use crate::constants::{PUMP_FUN_MIGRATION, PUMP_FUN_PROGRAM};

/// Gets the first transaction related to a Solana account by iteratively fetching
/// signatures in batches until we find the earliest one
pub fn get_first_tx(client: &RpcClient, account: &Pubkey) -> Result<Option<String>> {
    let mut before_sig: Option<Signature> = None;
    let mut earliest_sig = None;

    loop {
        let mut attempts = 0;
        let signatures = loop {
            let config = GetConfirmedSignaturesForAddress2Config {
                before: before_sig,
                until: None,
                limit: Some(500),
                commitment: Some(CommitmentConfig::confirmed()),
            };
            match client.get_signatures_for_address_with_config(account, config) {
                Ok(sigs) => break sigs,
                Err(e) if attempts < 3 => {
                    attempts += 1;
                    error!("Error fetching signatures (attempt {}): {}", attempts, e);
                    sleep(Duration::from_secs(1));
                }
                Err(e) => return Err(e.into()),
            }
        };
        let all_length = signatures.len();
        if all_length == 0 {
            break;
        }
        // Set before signature for next batch
        before_sig = Some(Signature::from_str(&signatures.last().unwrap().signature)?);

        let filtered_sigs = &signatures
            .into_iter()
            .filter(|sig| sig.err.is_none())
            .collect::<Vec<_>>();

        // Update earliest signature found
        if filtered_sigs.len() > 0 {
            earliest_sig = Some(filtered_sigs.last().unwrap().signature.clone());
        }

        // If we got less than the limit, we've reached the end
        if all_length < 500 {
            break;
        }
    }

    if let Some(sig) = earliest_sig {
        Ok(Some(sig))
    } else {
        Ok(None)
    }
}

/// Analyzes a transaction to determine if it's a pump fun migration transaction.
/// If it is, returns the token mint and bonding curve addresses.
pub async fn is_pump_fun_migration_tx(
    client: &RpcClient,
    tx_sig: &str,
    min_token_amount: u64,
) -> Result<Option<(String, String)>> {
    // Get the full transaction
    let tx = {
        let mut attempts = 0;
        loop {
            match client.get_transaction_with_config(
                &Signature::from_str(tx_sig)?,
                RpcTransactionConfig {
                    commitment: Some(CommitmentConfig::confirmed()),
                    encoding: Some(UiTransactionEncoding::Json),
                    max_supported_transaction_version: Some(0),
                },
            ) {
                Ok(tx) => break tx,
                Err(_) if attempts < 3 => {
                    attempts += 1;
                    // error!(
                    //     "Error getting transaction {} (attempt {}): {:?}",
                    //     tx_sig, attempts, e
                    // );
                    sleep(Duration::from_secs(1));
                    continue;
                }
                Err(e) => {
                    error!("Failed to get transaction after 3 attempts: {:?}", e);
                    return Err(anyhow::anyhow!("Failed to get transaction: {}", e));
                }
            }
        }
    };

    // Parse the transaction
    let tx_json = match tx.transaction.transaction {
        EncodedTransaction::Json(tx) => tx,
        _ => return Ok(None),
    };

    let msg = match tx_json.message {
        UiMessage::Raw(msg) => msg,
        _ => return Ok(None),
    };

    let mut account_keys = msg.account_keys.clone();
    if let Some(meta) = tx.transaction.meta.as_ref() {
        if let OptionSerializer::Some(loaded_addresses) = meta.loaded_addresses.as_ref() {
            account_keys.extend(loaded_addresses.writable.clone());
            account_keys.extend(loaded_addresses.readonly.clone());
        }
    }

    // Check if the pump fun migration address received at least 80 SOL
    let min_sol_increase = 80_000_000_000; // 80 SOL in lamports
    let mut pump_fun_migration_sol_increase = 0;

    if let Some(meta) = tx.transaction.meta.as_ref() {
        // Find the index of the pump fun migration address in account_keys
        for (i, key) in account_keys.iter().enumerate() {
            if key == PUMP_FUN_MIGRATION {
                if i < meta.pre_balances.len() && i < meta.post_balances.len() {
                    let pre_balance = meta.pre_balances[i];
                    let post_balance = meta.post_balances[i];
                    if post_balance > pre_balance {
                        pump_fun_migration_sol_increase = post_balance - pre_balance;
                    }
                }
                break;
            }
        }
    }

    // If the SOL increase is less than required, return None
    if pump_fun_migration_sol_increase < min_sol_increase {
        return Ok(None);
    }

    // Check for pump fun program instruction with 12 account keys
    for (_, ix) in msg.instructions.iter().enumerate() {
        let program_id = match Pubkey::from_str(&account_keys[ix.program_id_index as usize]) {
            Ok(id) => id,
            Err(_) => continue,
        };

        if program_id.to_string() == PUMP_FUN_PROGRAM && ix.accounts.len() == 12 {
            // Check if there's a token account with balance increase
            if let Some(meta) = tx.transaction.meta.as_ref() {
                let token_mint_idx = ix.accounts[2] as usize;
                let bonding_curve_idx = ix.accounts[3] as usize;

                if token_mint_idx >= account_keys.len() || bonding_curve_idx >= account_keys.len() {
                    continue;
                }

                let token_mint = account_keys[token_mint_idx].clone();
                let bonding_curve = account_keys[bonding_curve_idx].clone();

                // Check token account balance changes in post_token_balances
                if let OptionSerializer::Some(post_token_balances) = &meta.post_token_balances {
                    for token_balance in post_token_balances {
                        // Check if this token balance is for the token mint we're interested in
                        if token_balance.mint == token_mint {
                            let amount = token_balance
                                .ui_token_amount
                                .amount
                                .parse::<u64>()
                                .unwrap_or(0);

                            // Check if the amount is greater than the minimum threshold
                            if amount >= min_token_amount {
                                return Ok(Some((token_mint, bonding_curve)));
                            }
                        }
                    }
                }
            }
        }
    }
    Ok(None)
}

#[cfg(test)]
mod tests {
    use std::env;

    use super::*;

    #[test]
    fn test_get_first_tx() -> Result<()> {
        dotenv::dotenv()?;
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        // Test account: ENiVj8K5bBeDaiPMec5gxzPXqDiy9euTsbThvc5DhA51
        let account = Pubkey::from_str("ENiVj8K5bBeDaiPMec5gxzPXqDiy9euTsbThvc5DhA51")?;

        let first_tx =
            get_first_tx(&client, &account)?.expect("Should have at least one transaction");

        assert_eq!(
            first_tx,
            "46BJfRbC6aUF52nHxCVJaJKBPZtrNtgy64upbEbJ1bpw55YLgjvLBzTvCWcAi4aN1ybntHomELVAC23HD7zFQN3C"
        );

        let account = Pubkey::from_str("4vzFjUSaArXJgFK634TVxQ2rex2shGeqrnBzqqdYpump")?;
        let first_tx =
            get_first_tx(&client, &account)?.expect("Should have at least one transaction");

        assert_eq!(
            first_tx,
            "4xPjaqhxpCXDEQdU8ywVxF8CLUHmkGfKhVz1sznos4XJdTKUdDxex83r8wuRPUtVc89TKAWPCVsAM7KC3RQH3X64"
        );

        Ok(())
    }

    #[test]
    fn test_get_pump_txs() -> Result<()> {
        dotenv::dotenv()?;
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let account = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")?;

        let config = GetConfirmedSignaturesForAddress2Config {
            before: None,
            until: None,
            limit: Some(100),
            commitment: Some(CommitmentConfig::confirmed()),
        };

        let signatures = client.get_signatures_for_address_with_config(&account, config)?;
        println!("sig length: {}", signatures.len());
        println!(
            "time: {} vs {}",
            signatures[0].block_time.unwrap(),
            signatures[signatures.len() - 1].block_time.unwrap()
        );

        Ok(())
    }

    #[tokio::test(flavor = "multi_thread")]
    async fn test_is_pump_fun_migration_tx() {
        let rpc_url = "https://api.mainnet-beta.solana.com".to_string();
        let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

        // Test with a known pump fun migration transaction
        // This transaction should have at least 80 SOL increase for the migration address
        let tx_sig = "5vuWnLhFioAb1VnRExkg4et4eADNHHm4cNK5uie5gfDMPB72z8z2gAqEcX9HuHX2QCd6FQ7sm235NR4wvbk4LVzb";
        let min_token_amount = 200_000_000_000_000; // 2e14

        let result = is_pump_fun_migration_tx(&client, tx_sig, min_token_amount).await;
        assert!(result.is_ok(), "Function should not return an error");

        let result = result.unwrap();
        assert!(
            result.is_some(),
            "Transaction should be identified as a pump fun migration"
        );

        if let Some((token_mint, bonding_curve)) = result {
            // Verify the token mint and bonding curve are valid Pubkeys
            assert!(
                Pubkey::from_str(&token_mint).is_ok(),
                "Token mint should be a valid Pubkey"
            );
            assert!(
                Pubkey::from_str(&bonding_curve).is_ok(),
                "Bonding curve should be a valid Pubkey"
            );

            // Verify the token mint ends with "pump"
            assert!(
                token_mint.to_lowercase().ends_with("pump"),
                "Token mint should end with 'pump'"
            );
        }
    }
}

use alloy::sol;

sol! {
    #[sol(rpc)]
    contract ERC20 {
        function balanceOf(address account) external view returns (uint256);
        function decimals() external view returns (uint8);
        function symbol() external view returns (string memory);
    }

    #[sol(rpc)]
    contract FourMeme {
        event TokenPurchase (
            address token_address,
            address buyer_address,
            uint256 param3,
            uint256 token_amount,
            uint256 bnb_amount,
            uint256 param6,
            uint256 param7,
            uint256 param8,
        );
        event TokenSale (
            address token_address,
            address buyer_address,
            uint256 param3,
            uint256 token_amount,
            uint256 bnb_amount,
            uint256 param6,
            uint256 param7,
            uint256 param8,
        );
    }

    #[sol(rpc)]
    contract PancakeV3Factory {
        function getPool(
            address tokenA,
            address tokenB,
            uint24 fee
        ) external view returns (address pool);
    }

    #[sol(rpc)]
    contract PancakeV3Pool {
        event Swap(
            address indexed sender,
            address indexed recipient,
            int256 amount0,
            int256 amount1,
            uint160 sqrtPriceX96,
            uint128 liquidity,
            int24 tick,
            uint128 protocolFeesToken0,
            uint128 protocolFeesToken1
        );
    }
}

use crate::token_monitor_file;
use crate::token_utils::{self, format_buy_signal_request};
use crate::{
    get_latest_trades, get_potential_wallets, get_token_account_balance, get_token_info,
    telegram::TelegramBot, wallet_store::WalletStore, ApiClient, PoolBuyInfo, UpsertBuySignalReq,
    API_CLIENT, TELEGRAM_BOT, WSOL_MINT,
};
use crate::{get_sol_price, system_time_serde, AddSellSignalReq};
use anyhow::Result;
use dashmap::DashMap;
use log::{error, info, warn};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use std::collections::HashMap;
use std::str::FromStr;
use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime};
use tokio::time::sleep;

// Track sent signals to avoid duplicates
pub static SENT_SIGNALS: Lazy<DashMap<String, u8>> = Lazy::new(DashMap::new);
pub static FOMO_MESSAGE_IDS: Lazy<Mutex<HashMap<String, i32>>> =
    Lazy::new(|| Mutex::new(HashMap::new()));

// Add a struct to track token monitoring state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenMonitorState {
    pub token_mint: String,
    pub symbol: String,
    pub tracked_wallets: Vec<TrackedWalletInfo>,
    pub entry_price: f64,
    #[serde(with = "system_time_serde")]
    pub entry_time: SystemTime,
    pub emit_time: i64,                  // Store the original emit time
    pub telegram_link: String,           // Store the original telegram link
    pub is_pump: Option<bool>,           // Whether this token is in a pump
    pub is_pump_amm: Option<bool>,       // Whether this token is in a pump amm
    pub pool_pubkey_str: Option<String>, // The pool pubkey as a string
    pub highest_price: Option<f64>,
    pub last_milestone: Option<f64>, // Track the last milestone that was sent (100%, 250%, etc.)
    pub sold_signal_sent_time: Option<i64>, // The time the sold signal was sent
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrackedWalletInfo {
    pub wallet: String,
    pub initial_amount: u64,
    pub current_amount: u64,
    pub max_amount: u64, // Track the maximum amount ever held
    pub win_rate: f64,
}

// Global state for token monitoring, changed from DashMap to Arc<Mutex<HashMap>>
pub static TOKEN_MONITORS: Lazy<Arc<Mutex<HashMap<String, TokenMonitorState>>>> =
    Lazy::new(|| Arc::new(Mutex::new(HashMap::new())));

/// Monitor trades from tracked wallets and send FOMO signals when conditions are met
pub async fn monitor_trades(
    bot: Arc<TelegramBot>,
    client: Arc<RpcClient>,
    seen_txs: Arc<tokio::sync::Mutex<HashMap<String, bool>>>,
) -> Result<()> {
    // Load saved token monitors and sent signals
    if let Err(e) = token_monitor_file::load_token_monitors() {
        error!("Failed to load token monitors from disk: {:?}", e);
    }
    if let Err(e) = token_monitor_file::load_sent_signals() {
        error!("Failed to load sent signals from disk: {:?}", e);
    }

    // Start background persistence task
    token_monitor_file::start_token_monitors_persistence().await;

    let mut interval = tokio::time::interval(Duration::from_millis(3000));
    let mut tracked_wallet_balances: HashMap<String, u64> = HashMap::new();
    let mut sol_price = crate::get_sol_price().await?;
    for cnt in 0.. {
        if cnt % 100 == 0 {
            info!("Still monitoring trades");
            if let Ok(new_sol_price) = crate::get_sol_price().await {
                sol_price = new_sol_price;
            }
        }
        interval.tick().await;

        // Load tracked wallets
        let wallet_store = match WalletStore::load("tracked_wallets.json") {
            Ok(store) => store,
            Err(e) => {
                error!("Error loading wallet store: {:?}", e);
                continue;
            }
        };

        let tracked_pub_keys: Vec<Pubkey> = wallet_store
            .get_wallets()
            .iter()
            .filter_map(|addr| Pubkey::from_str(addr).ok())
            .collect();

        if tracked_pub_keys.is_empty() {
            continue;
        }

        let last_wallet_balances = tracked_wallet_balances.clone();
        let (new_balances, potential_wallets) =
            match get_potential_wallets(&client, &tracked_pub_keys, &last_wallet_balances) {
                Ok((balances, wallets)) => (balances, wallets),
                Err(e) => {
                    error!("Error getting wallet balances: {:?}", e);
                    sleep(Duration::from_secs(1)).await;
                    continue;
                }
            };
        tracked_wallet_balances = new_balances;

        if potential_wallets.is_empty() {
            continue;
        }
        info!("Found {} potential wallets", potential_wallets.len());

        for target_wallet in potential_wallets.iter() {
            // Get latest trades
            let mut seen_txs = seen_txs.lock().await;
            let target_wallet = Pubkey::from_str(target_wallet).unwrap();
            let trades = match get_latest_trades(&client, &target_wallet, &mut seen_txs) {
                Ok(trades) => trades,
                Err(e) => {
                    error!("Error getting latest trades: {:?}", e);
                    continue;
                }
            };
            drop(seen_txs);

            for trade in trades {
                // Extract basic trade information using the utility function
                let (token_mint, sol_amount, pool_sol_amount, token_amount) =
                    match token_utils::extract_pool_buy_info_details(&trade) {
                        Some(details) => details,
                        None => continue,
                    };

                // Skip if it's below our thresholds or is WSOL
                if !token_utils::should_process_trade(
                    &token_mint,
                    sol_amount,
                    pool_sol_amount,
                    WSOL_MINT,
                ) {
                    info!(
                        "Skipping wallet {} token {} because of low sol amount or pool sol amount. sol_amount: {}, pool_sol_amount: {}",
                        target_wallet, token_mint, sol_amount, pool_sol_amount,
                    );
                    continue;
                }

                // Check if we've already sent a signal for this token
                if SENT_SIGNALS.contains_key(&token_mint) {
                    info!(
                        "Skipping token {} because we've already sent a signal for it",
                        token_mint
                    );
                    continue;
                }

                // Get token info
                let token_info =
                    match get_token_info(&client, &Pubkey::from_str(token_mint.as_str()).unwrap())
                        .await
                    {
                        Ok(info) => info,
                        Err(e) => {
                            error!("Error getting token info: {:?}", e);
                            continue;
                        }
                    };
                let (symbol, decimal) = token_info;

                // Get token account balance for this wallet
                let token_mint_pubkey = Pubkey::from_str(&token_mint).unwrap();
                let token_account =
                    get_associated_token_address(&target_wallet, &token_mint_pubkey);
                let initial_amount =
                    get_token_account_balance(&client, &token_account).unwrap_or(0);

                // Check if this token is already being monitored
                let is_token_monitored;
                let target_wallet_string = target_wallet.to_string();
                {
                    // Check if token is monitored and if wallet needs to be added - short lock duration
                    let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                        anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                    })?;

                    if let Some(monitor_state) = token_monitors.get_mut(&token_mint) {
                        // Token is already being monitored
                        is_token_monitored = true;

                        // Check if this wallet is already tracked for this token
                        let is_wallet_already_tracked = monitor_state
                            .tracked_wallets
                            .iter()
                            .any(|w| w.wallet == target_wallet_string);

                        // If wallet isn't tracked, add it to the monitored wallets
                        if !is_wallet_already_tracked {
                            // Get wallet win rate
                            let store = WalletStore::load("tracked_wallets.json").unwrap();
                            let win_rate = store.get_wallet_win_rate(target_wallet_string.as_str());

                            monitor_state.tracked_wallets.push(TrackedWalletInfo {
                                wallet: target_wallet_string.clone(),
                                initial_amount,
                                current_amount: initial_amount,
                                max_amount: initial_amount,
                                win_rate,
                            });

                            info!(
                                "Added wallet {} to existing monitoring for token {}",
                                target_wallet, token_mint
                            );
                        }
                    } else {
                        is_token_monitored = false;
                    }
                }
                if is_token_monitored {
                    continue;
                }

                // Calculate price using the utility function
                let price_usd = token_utils::calculate_token_price_usd(
                    sol_amount,
                    token_amount,
                    decimal as i32,
                    sol_price,
                );

                // Get wallet win rate
                let store = WalletStore::load("tracked_wallets.json").unwrap();
                let wallet_win_rate = store.get_wallet_win_rate(target_wallet_string.as_str());

                // Send FOMO signal to Telegram
                let msg = match bot.send_fomo_signal(&token_mint, &symbol, price_usd).await {
                    Err(e) => {
                        error!("Failed to send FOMO signal: {:?}", e);
                        continue;
                    }
                    Ok(msg) => {
                        if let Ok(mut message_ids) = FOMO_MESSAGE_IDS.lock() {
                            message_ids.insert(token_mint.clone(), msg.id.0);
                        }
                        msg
                    }
                };
                let telegram_link = bot.get_message_link(&msg);
                let emit_time = ApiClient::current_timestamp();

                // Mark this token as sent
                SENT_SIGNALS.insert(token_mint.clone(), 1);

                // Determine if this is a PumpFun pool or Raydium pool
                let (is_pump, is_pump_amm, pool_pubkey) = match &trade {
                    PoolBuyInfo::PumpFun(info) => (true, false, info.bonding_curve),
                    PoolBuyInfo::Raydium(info) => (false, false, info.pool_pubkey),
                    PoolBuyInfo::PumpFunAmm(info) => (false, true, info.pool_pubkey),
                    _ => (false, false, Pubkey::default()),
                };

                // Create new monitoring state for this token
                let monitor_state = TokenMonitorState {
                    token_mint: token_mint.clone(),
                    symbol: symbol.clone(),
                    tracked_wallets: vec![TrackedWalletInfo {
                        wallet: target_wallet_string,
                        initial_amount,
                        current_amount: initial_amount,
                        max_amount: initial_amount,
                        win_rate: wallet_win_rate,
                    }],
                    entry_price: price_usd,
                    entry_time: SystemTime::now(),
                    emit_time,
                    telegram_link: telegram_link.clone(),
                    is_pump: Some(is_pump),
                    is_pump_amm: Some(is_pump_amm),
                    pool_pubkey_str: Some(pool_pubkey.to_string()),
                    highest_price: Some(price_usd),
                    last_milestone: None,
                    sold_signal_sent_time: None,
                };

                // Add to monitored tokens - short lock duration
                {
                    let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                        anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                    })?;
                    token_monitors.insert(token_mint.clone(), monitor_state);
                }

                info!(
                    "Started tracking token {} with wallet {}",
                    token_mint, target_wallet
                );

                // Send buy signal to API
                let req = format_buy_signal_request(
                    &token_mint,
                    1,
                    price_usd,
                    1.0,
                    wallet_win_rate,
                    0.0,
                    telegram_link,
                    emit_time,
                    price_usd,
                    symbol,
                );

                if let Err(e) = API_CLIENT.send_buy_signal(req).await {
                    error!("Failed to send buy signal to API: {:?}", e);
                }
            }
        }
    }
    Ok(())
}

/// Regularly monitor all token balances and send updates to API
pub async fn monitor_token_balances() -> Result<()> {
    let client = RpcClient::new_with_commitment(
        std::env::var("RPC_URL")
            .unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string()),
        CommitmentConfig::confirmed(),
    );

    let mut interval = tokio::time::interval(Duration::from_secs(15));
    // Keep the HashMaps as a cache to avoid parsing pubkeys repeatedly
    let mut token_status_cache: HashMap<String, (bool, bool)> = HashMap::new();
    let mut token_pool_pubkey_cache: HashMap<String, Pubkey> = HashMap::new();
    let mut sol_price = match get_sol_price().await {
        Ok(price) => price,
        Err(e) => {
            error!("Failed to get SOL price: {}", e);
            150.0
        }
    };

    for cnt in 0.. {
        interval.tick().await;
        if cnt % 20 == 0 {
            // Check TOKEN_MONITORS size without holding the lock for long
            let token_count = {
                let token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                })?;
                token_monitors.len()
            };

            info!("Still monitoring {} token balances", token_count);
            if let Ok(new_sol_price) = crate::get_sol_price().await {
                sol_price = new_sol_price;
            }
        }

        // Skip if no tokens are being monitored
        let is_empty = {
            let token_monitors = TOKEN_MONITORS
                .lock()
                .map_err(|e| anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e))?;
            token_monitors.is_empty()
        };

        if is_empty {
            continue;
        }

        // Get a snapshot of tokens to process and prepare batch request data
        let tokens_data: Vec<(String, TokenMonitorState)>;
        {
            let token_monitors = TOKEN_MONITORS
                .lock()
                .map_err(|e| anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e))?;

            tokens_data = token_monitors
                .iter()
                .map(|(token_mint, state)| (token_mint.clone(), state.clone()))
                .collect();
        }

        // Prepare token accounts to batch query
        let mut token_accounts = Vec::new();
        let mut token_account_mapping = HashMap::new();

        for (token_mint, state) in &tokens_data {
            let token_mint_pubkey = match Pubkey::from_str(token_mint) {
                Ok(pk) => pk,
                Err(_) => continue,
            };

            for wallet_info in &state.tracked_wallets {
                let wallet_pubkey = match Pubkey::from_str(&wallet_info.wallet) {
                    Ok(pk) => pk,
                    Err(_) => continue,
                };

                let token_account =
                    get_associated_token_address(&wallet_pubkey, &token_mint_pubkey);
                token_accounts.push(token_account);

                // Store the mapping of token_account -> (token_mint, wallet)
                token_account_mapping.insert(
                    token_account,
                    (token_mint.clone(), wallet_info.wallet.clone()),
                );
            }
        }

        // Batch get all token account balances
        let account_balances =
            match token_utils::get_multiple_token_balances(&client, &token_accounts) {
                Ok(balances) => balances,
                Err(e) => {
                    error!("Failed to get multiple token account balances: {:?}", e);
                    continue;
                }
            };

        // Now process each token with the pre-fetched balances
        let mut tokens_with_sold_signal = Vec::new();
        let mut buy_signals_to_send = Vec::new();
        let mut tokens_to_remove = Vec::new();
        for (token_mint, token_state_clone) in tokens_data {
            let token_mint_pubkey = match Pubkey::from_str(&token_mint) {
                Ok(pk) => pk,
                Err(_) => continue,
            };

            // Process the token state with the batch-fetched balances
            let mut updated_wallet_infos = Vec::new();
            let mut total_ratio = 0.0;
            let mut total_win_rate = 0.0;
            let mut wallets_count = 0;
            let mut wallets_still_holding = 0;
            let mut related_wallets = Vec::new();

            for wallet_info in &token_state_clone.tracked_wallets {
                related_wallets.push(wallet_info.wallet.clone());
                let wallet_pubkey = match Pubkey::from_str(&wallet_info.wallet) {
                    Ok(pk) => pk,
                    Err(_) => continue,
                };

                let token_account =
                    get_associated_token_address(&wallet_pubkey, &token_mint_pubkey);
                let mut updated_info = wallet_info.clone();

                // Get the balance from our batch result
                if let Some(&amount) = account_balances.get(&token_account) {
                    updated_info.current_amount = amount;

                    // Update max amount if current is greater
                    if amount > updated_info.max_amount {
                        updated_info.max_amount = amount;
                    }

                    // Calculate holding ratio based on max amount ever held
                    let ratio = amount as f64 / updated_info.max_amount as f64;
                    total_ratio += ratio;
                    total_win_rate += updated_info.win_rate;
                    wallets_count += 1;

                    // Count wallets still holding tokens
                    if amount > 0 {
                        wallets_still_holding += 1;
                    }

                    updated_wallet_infos.push(updated_info);
                } else {
                    // If we didn't get the balance in our batch, use the previous value
                    updated_wallet_infos.push(updated_info);
                }
            }

            // Skip if there was no wallet count
            if wallets_count == 0 {
                continue;
            }

            // Update the token state with new wallet info
            {
                let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                })?;
                if let Some(state) = token_monitors.get_mut(&token_mint) {
                    state.tracked_wallets = updated_wallet_infos.clone();
                } else {
                    continue; // Token was removed between iterations
                }
            }

            // Calculate averages
            let avg_ratio = total_ratio / wallets_count as f64;
            let avg_win_rate = total_win_rate / wallets_count as f64;

            // Get current price for profit/loss calculation
            // Check if we need to get pool info, or can use cached/persisted values
            let pool_pubkey_str = token_state_clone.pool_pubkey_str.clone();
            let is_pump_value = token_state_clone.is_pump;
            let is_pump_amm_value = token_state_clone.is_pump_amm;
            let mut pool_pubkey = match token_pool_pubkey_cache.get(&token_mint) {
                Some(pk) => *pk,
                None => {
                    if let Some(pool_pubkey_str) = &pool_pubkey_str {
                        match Pubkey::from_str(pool_pubkey_str) {
                            Ok(pk) => {
                                // Cache the parsed pubkey
                                token_pool_pubkey_cache.insert(token_mint.clone(), pk);
                                if let Some(is_pump) = is_pump_value {
                                    token_status_cache.insert(
                                        token_mint.clone(),
                                        (is_pump, is_pump_amm_value.unwrap_or(false)),
                                    );
                                }
                                pk
                            }
                            Err(_) => {
                                // No valid pool pubkey, need to find a new one
                                Pubkey::default()
                            }
                        }
                    } else {
                        // No pool pubkey, need to find a new one
                        Pubkey::default()
                    }
                }
            };

            let mut is_pump_status =
                token_status_cache
                    .get(&token_mint)
                    .cloned()
                    .unwrap_or_else(|| {
                        (
                            is_pump_value.unwrap_or(true),
                            is_pump_amm_value.unwrap_or(false),
                        )
                    });

            // If pool pubkey is default or cached info is missing, we need to get latest pool info
            if pool_pubkey == Pubkey::default()
                || !token_status_cache.contains_key(&token_mint)
                || !token_pool_pubkey_cache.contains_key(&token_mint)
            {
                // Update pool information using the utility function
                match token_utils::update_pool_info(
                    &client,
                    &token_mint,
                    &token_mint_pubkey,
                    &mut token_status_cache,
                    &mut token_pool_pubkey_cache,
                )
                .await
                {
                    Ok((new_is_pump, new_is_pump_amm, new_pool_pubkey)) => {
                        is_pump_status = (new_is_pump, new_is_pump_amm);
                        pool_pubkey = new_pool_pubkey;

                        // Update the token state with new pool info
                        {
                            let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                                anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                            })?;
                            if let Some(state) = token_monitors.get_mut(&token_mint) {
                                state.is_pump = Some(is_pump_status.0);
                                state.is_pump_amm = Some(is_pump_status.1);
                                state.pool_pubkey_str = Some(pool_pubkey.to_string());
                            } else {
                                continue; // Token was removed between iterations
                            }
                        }
                    }
                    Err(e) => {
                        // No buy info, try again later
                        warn!(
                            "No buy info found for token {}, try again later: {:?}",
                            token_mint, e
                        );
                        continue;
                    }
                };
            }

            // Get current price using the comprehensive function
            let current_price = match token_utils::get_current_token_price(
                &client,
                &token_mint_pubkey,
                &pool_pubkey,
                is_pump_status.0,
                is_pump_status.1,
                sol_price,
            )
            .await
            {
                Ok(price) => price,
                Err(e) => {
                    error!("Failed to get token price from pool: {:?}", e);
                    if e.to_string().contains("pool is complete") {
                        // Pool is complete, need to find a new one next time
                        let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                            anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                        })?;
                        if let Some(state) = token_monitors.get_mut(&token_mint) {
                            state.pool_pubkey_str = None;
                        }
                        token_pool_pubkey_cache.remove(&token_mint);
                    }
                    continue;
                }
            };

            // Calculate price change
            let price_change =
                token_utils::calculate_price_change(current_price, token_state_clone.entry_price);

            // Update the token state with new price
            let prev_highest_price = token_state_clone.highest_price;
            let highest_price = {
                let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                })?;
                if let Some(state) = token_monitors.get_mut(&token_mint) {
                    if let Some(highest_price) = state.highest_price {
                        state.highest_price = Some(f64::max(
                            token_state_clone.entry_price,
                            f64::max(highest_price, current_price),
                        ));
                    } else {
                        state.highest_price =
                            Some(f64::max(token_state_clone.entry_price, current_price));
                    }
                    state.highest_price.unwrap()
                } else {
                    continue; // Token was removed between iterations
                }
            };

            // Check for milestone thresholds and send notifications
            let entry_price = token_state_clone.entry_price;
            let price_increase_percentage = (highest_price / entry_price - 1.0) * 100.0;

            if token_state_clone.sold_signal_sent_time.is_some()
                && prev_highest_price.is_some()
                && highest_price > prev_highest_price.unwrap()
            {
                // continue to update sell signal highest gain
                let highest_gain = token_utils::calculate_price_change(
                    highest_price,
                    token_state_clone.entry_price,
                );
                info!(
                    "Updating sell signal highest gain for token {} to {}",
                    token_mint, highest_gain
                );
                let req = AddSellSignalReq {
                    token_address: token_mint.to_string(),
                    telegram_link: "https://t.me/pump_hound_signal".to_string(),
                    highest_gain,
                };
                if let Err(e) = API_CLIENT.send_sell_signal(req).await {
                    error!("Failed to send sell signal to API: {:?}", e);
                }
            }

            // Define milestone thresholds
            let milestone_thresholds = vec![
                50.0, 100.0, 200.0, 350.0, 500.0, 750.0, 1000.0, 1500.0, 2000.0, 3000.0, 4000.0,
                6000.0, 8000.0, 12000.0, 16000.0, 20000.0, 40000.0, 60000.0, 80000.0, 100000.0,
            ];

            // Get the last milestone that was sent
            let last_milestone = {
                let token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                })?;
                if let Some(state) = token_monitors.get(&token_mint) {
                    state.last_milestone
                } else {
                    None
                }
            };

            // Find the highest milestone that has been crossed
            let mut highest_crossed_milestone = None;
            for &threshold in milestone_thresholds.iter() {
                if price_increase_percentage >= threshold {
                    // Check if this milestone is higher than the last one we sent
                    if last_milestone.is_none() || threshold > last_milestone.unwrap() {
                        highest_crossed_milestone = Some(threshold);
                    }
                }
            }

            // If we found a new milestone, send a notification
            if let Some(milestone) = highest_crossed_milestone {
                info!(
                    "Token {} has reached a new milestone: +{:.0}% increase from entry price",
                    token_mint, milestone
                );

                // Get the original message ID for reply
                let reply_to_id = FOMO_MESSAGE_IDS
                    .lock()
                    .ok()
                    .and_then(|ids| ids.get(&token_mint).copied());

                // Send milestone notification
                let bot = TELEGRAM_BOT.get().unwrap();
                match tokio::time::timeout(
                    std::time::Duration::from_secs(15),
                    bot.send_milestone_notification(
                        &token_mint,
                        &token_state_clone.symbol,
                        current_price,
                        price_increase_percentage,
                        reply_to_id,
                    ),
                )
                .await
                {
                    Ok(result) => {
                        match result {
                            Ok(_) => {
                                // Update the last milestone in the token state
                                let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                                    anyhow::anyhow!(
                                        "Failed to acquire TOKEN_MONITORS lock: {:?}",
                                        e
                                    )
                                })?;
                                if let Some(state) = token_monitors.get_mut(&token_mint) {
                                    state.last_milestone = Some(milestone);
                                }
                            }
                            Err(e) => {
                                error!("Failed to send milestone notification: {:?}", e);
                            }
                        }
                    }
                    Err(_) => {
                        error!(
                            "Timed out waiting for Telegram API response for milestone notification for token {}",
                            token_mint
                        );
                    }
                }
            }

            // Check if average ratio is below threshold (10%)
            let now = ApiClient::current_timestamp();
            let smart_money_sold = avg_ratio <= 0.1;
            let buy_signal_expired = token_state_clone.emit_time < now - 12 * 60 * 60
                && current_price < 0.00005
                || token_state_clone.emit_time < now - 24 * 60 * 60 && current_price < 0.0001
                || token_state_clone.emit_time < now - 36 * 60 * 60 && current_price < 0.0002
                || token_state_clone.emit_time < now - 48 * 60 * 60 && current_price < 0.0004
                || token_state_clone.emit_time < now - 60 * 60 * 60 && current_price < 0.0008;
            if (smart_money_sold || buy_signal_expired)
                && token_state_clone.sold_signal_sent_time.is_none()
            {
                if smart_money_sold {
                    info!(
                        "Smart wallets sold token {}, average ratio: {:.2}%",
                        token_mint,
                        avg_ratio * 100.0
                    );
                } else if buy_signal_expired {
                    info!(
                        "Buy signal expired for token {}, current price: {:.8}",
                        token_mint, current_price
                    );
                }

                // Calculate minutes monitored
                let now = SystemTime::now();
                let minutes_monitored = now
                    .duration_since(token_state_clone.entry_time)
                    .unwrap_or(Duration::from_secs(0))
                    .as_secs()
                    / 60;

                // Record to CSV file
                if let Err(e) = token_monitor_file::record_sold_token_to_csv(
                    &token_mint,
                    &token_state_clone.symbol,
                    price_change,
                    minutes_monitored,
                    related_wallets,
                ) {
                    error!("Failed to record sold token to CSV: {:?}", e);
                }

                info!("Sending sold signal for token {} to API", token_mint);
                let highest_gain = token_utils::calculate_price_change(
                    highest_price,
                    token_state_clone.entry_price,
                );
                let req = AddSellSignalReq {
                    token_address: token_mint.to_string(),
                    telegram_link: "https://t.me/pump_hound_signal".to_string(),
                    highest_gain,
                };
                if let Err(e) = API_CLIENT.send_sell_signal(req).await {
                    error!("Failed to send sell signal to API: {:?}", e);
                }

                // Mark this token for removal
                if smart_money_sold {
                    tokens_with_sold_signal.push(token_mint.clone());
                } else if buy_signal_expired {
                    tokens_to_remove.push(token_mint.clone());
                }
                continue;
            }

            // Prepare update to API
            if token_state_clone.sold_signal_sent_time.is_none() {
                let req = UpsertBuySignalReq {
                    token_address: token_mint.clone(),
                    smart_wallet_count: wallets_still_holding as i32,
                    buy_entry_price: token_state_clone.entry_price,
                    emit_time: token_state_clone.emit_time, // Use the stored emit_time
                    telegram_link: token_state_clone.telegram_link.clone(), // Use the stored telegram_link
                    win_rate: ApiClient::get_win_rate_v3(avg_ratio, avg_win_rate, price_change),
                    average_holding: (avg_ratio * 100.0).round() / 100.0,
                    average_win_rate: (avg_win_rate * 100.0).round() / 100.0,
                    highest_price,
                    symbol: token_state_clone.symbol.clone(),
                };
                buy_signals_to_send.push(req);
            }
        }

        if !buy_signals_to_send.is_empty() {
            if let Err(e) = API_CLIENT.batch_send_buy_signals(buy_signals_to_send).await {
                error!("Failed to update buy signals: {:?}", e);
            }
        }

        // Mark tokens with sold signal
        for token_mint in tokens_with_sold_signal {
            {
                let mut token_monitors = TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e)
                })?;

                if let Some(token_state) = token_monitors.get_mut(&token_mint) {
                    // Set the sold_signal_sent_time to current timestamp
                    token_state.sold_signal_sent_time = Some(ApiClient::current_timestamp());
                }
            }

            // Save immediately after removal
            if let Err(e) = token_monitor_file::save_token_monitors() {
                error!(
                    "Failed to save token monitors after removing {}: {:?}",
                    token_mint, e
                );
            }
        }
        {
            // Remove tokens with sold_signal_sent_time before 48 hours
            let mut token_monitors = TOKEN_MONITORS
                .lock()
                .map_err(|e| anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e))?;
            for (token_mint, token_state) in token_monitors.iter_mut() {
                if let Some(sent_time) = token_state.sold_signal_sent_time {
                    let now = SystemTime::now();
                    let sent_time = SystemTime::UNIX_EPOCH + Duration::from_secs(sent_time as u64);
                    let time_diff = now
                        .duration_since(sent_time)
                        .unwrap_or(Duration::from_secs(0));
                    if time_diff > Duration::from_secs(24 * 60 * 60) {
                        tokens_to_remove.push(token_mint.clone());
                    }
                }
            }
            for token_mint in tokens_to_remove {
                token_monitors.remove(&token_mint);
            }
        }

        // Save the updated token monitors
        if let Err(e) = token_monitor_file::save_token_monitors() {
            error!(
                "Failed to save token monitors after removing tokens: {:?}",
                e
            );
        }
    }
    Ok(())
}

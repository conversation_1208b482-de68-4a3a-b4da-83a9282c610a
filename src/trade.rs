use crate::{
    analyze_trade, get_wallets_balance, parse_and_buy_multi, wallet::WalletContext, PoolBuyInfo,
    PUMP_FUN_MIGRATION, RAYDIUM_V4_PROGRAM, WSOL_MINT,
};
use anyhow::Result;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{error, warn};
use solana_client::{
    rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient},
    rpc_config::RpcTransactionConfig,
};
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status::{
    option_serializer::OptionSerializer, EncodedTransaction, UiMessage, UiTransactionEncoding,
    UiTransactionTokenBalance,
};
use std::{
    collections::HashMap,
    error::Error,
    str::FromStr,
    sync::{atomic::AtomicBool, Arc},
    time::{SystemTime, UNIX_EPOCH},
};

#[derive(Debug)]
pub struct TradeState {
    pub target_wallet: Pubkey,
    pub token_mint: String,
    pub pool_pubkey: Pubkey,
    pub bought_price: f64,
    pub bought_sol: f64,
    pub amount: u64,
    pub decimals: u8,
    pub bought_time: SystemTime,
    pub initial_price_usd: f64,
    pub last_target_hit_time: SystemTime, // Time when last target was hit
    pub highest_multiplier_hit: f64,      // Highest price multiplier reached
    pub is_from_pump: bool,               // Is this trade observed from pump
    pub target_highest_balance: u64,      // Highest token balance observed in target's wallet
    pub last_target_balance_ratio: f64,   // Last observed target's balance ratio (current/highest)
    pub last_balance_ratio_sell: f64,     // Last balance ratio that triggered a sell
}

pub fn get_latest_trades(
    client: &RpcClient,
    target_wallet: &Pubkey,
    seen_txs: &mut HashMap<String, bool>,
) -> Result<Vec<PoolBuyInfo>> {
    let config = GetConfirmedSignaturesForAddress2Config {
        before: None,
        until: None,
        limit: Some(3),
        commitment: Some(CommitmentConfig::confirmed()),
    };

    let signatures = client
        .get_signatures_for_address_with_config(target_wallet, config)
        .map_err(|e| {
            error!("Error getting signatures. Response: {}", e.to_string());
            if let Some(source) = e.source() {
                if let Some(reqwest_err) = source.downcast_ref::<reqwest::Error>() {
                    error!("HTTP Error: {}", reqwest_err);
                    if let Some(url) = reqwest_err.url() {
                        error!("Failed URL: {}", url);
                    }
                }
            }
            e
        })?;

    let mut trades = Vec::new();
    for sig_info in signatures {
        let signature = sig_info.signature.to_string();
        if seen_txs.contains_key(&signature) {
            continue;
        }
        seen_txs.insert(signature.clone(), true);

        // Check if tx is within 10 seconds
        let block_time = sig_info.block_time.unwrap_or(0);
        let age = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs() as i64 - block_time;
        if age > 10 {
            continue;
        }

        match analyze_trade(client, &sig_info.signature) {
            Ok(pool_info) => {
                trades.push(pool_info);
            }
            Err(e) => {
                error!("Error analyzing tx: {:?}", e);
            }
        }
    }

    Ok(trades)
}

pub async fn process_trade(
    trade_info: PoolBuyInfo,
    target_wallet: &Pubkey,
    client: &RpcClient,
    jito_sdk: &JitoJsonRpcSDK,
    jito_tip_account: Pubkey,
    wallet: &WalletContext,
    my_wallet_trading: Arc<AtomicBool>,
) -> Option<TradeState> {
    // Check wallet balance to determine how much SOL to use
    let buy_sol_amount = match client.get_balance(&wallet.payer()) {
        Ok(balance) => {
            if balance < 300_000_000 {
                warn!("Wallet balance is less than 0.3 SOL, skipping trade");
                return None;
            }
            let balance_amount = balance as f64 / 1_000_000_000.0 / 10.0;
            let pool_amount = match &trade_info {
                PoolBuyInfo::Raydium(info) => info.pool_sol_amount as f64 / 1_000_000_000.0 * 0.015,
                PoolBuyInfo::PumpFun(info) => info.pool_sol_amount as f64 / 1_000_000_000.0 * 0.015,
                PoolBuyInfo::PumpFunAmm(_) => return None,
                PoolBuyInfo::NotBuy => return None,
            };
            ((balance_amount.min(pool_amount) / 0.1).floor() * 0.1).max(0.1)
        }
        Err(e) => {
            error!("Error getting balance: {:?}", e);
            return None;
        }
    };

    match parse_and_buy_multi(
        &trade_info,
        vec![target_wallet.to_string()],
        client,
        buy_sol_amount,
        jito_sdk,
        jito_tip_account,
        wallet,
        my_wallet_trading,
    )
    .await
    {
        Ok(state) => state,
        Err(e) => {
            warn!("parse_and_buy_from_raydium error: {}", e);
            None
        }
    }
}

pub fn get_raydium_pool_pubkey_from_tx(
    client: &RpcClient,
    sig: String,
    token_mint: &Pubkey,
) -> Result<Option<(Pubkey, Pubkey)>> {
    let sig = Signature::from_str(&sig)?;
    let tx = match client.get_transaction_with_config(
        &sig,
        RpcTransactionConfig {
            commitment: Some(CommitmentConfig::confirmed()),
            encoding: Some(UiTransactionEncoding::Json),
            max_supported_transaction_version: Some(0),
        },
    ) {
        Ok(tx) => tx,
        Err(e) => return Err(anyhow::anyhow!(e)),
    };
    let tx_json = match tx.transaction.transaction {
        EncodedTransaction::Json(tx) => tx,
        _ => return Err(anyhow::anyhow!("Unexpected transaction encoding")),
    };
    let msg = match tx_json.message {
        UiMessage::Raw(msg) => msg,
        _ => return Err(anyhow::anyhow!("Unexpected transaction encoding")),
    };
    let mut account_keys = msg.account_keys.clone();
    if let Some(meta) = tx.transaction.meta.as_ref() {
        if let OptionSerializer::Some(loaded_addresses) = meta.loaded_addresses.as_ref() {
            account_keys.extend(loaded_addresses.writable.clone());
            account_keys.extend(loaded_addresses.readonly.clone());
        }
    }
    for ix in msg.instructions {
        let program = &account_keys[ix.program_id_index as usize];
        if program.to_string() != RAYDIUM_V4_PROGRAM || ix.accounts.len() != 21 {
            continue;
        }
        if (&account_keys[ix.accounts[9] as usize]).to_string() == token_mint.to_string() {
            return Ok(Some((
                Pubkey::from_str(&account_keys[ix.accounts[4] as usize]).unwrap(),
                Pubkey::from_str(&account_keys[ix.accounts[10] as usize]).unwrap(),
            )));
        }
        if (&account_keys[ix.accounts[8] as usize]).to_string() == token_mint.to_string() {
            return Ok(Some((
                Pubkey::from_str(&account_keys[ix.accounts[4] as usize]).unwrap(),
                Pubkey::from_str(&account_keys[ix.accounts[11] as usize]).unwrap(),
            )));
        }
    }
    Ok(None)
}

// returns token mint and pool pub key and is_pump_fun
pub fn get_new_raydium_pool_from_tx(
    client: &RpcClient,
    sig: String,
) -> Result<Option<(Pubkey, Pubkey, bool)>> {
    let sig = Signature::from_str(&sig)?;
    let tx = match client.get_transaction_with_config(
        &sig,
        RpcTransactionConfig {
            commitment: Some(CommitmentConfig::confirmed()),
            encoding: Some(UiTransactionEncoding::Json),
            max_supported_transaction_version: Some(0),
        },
    ) {
        Ok(tx) => tx,
        Err(e) => return Err(anyhow::anyhow!(e)),
    };
    let tx_json = match tx.transaction.transaction {
        EncodedTransaction::Json(tx) => tx,
        _ => return Err(anyhow::anyhow!("Unexpected transaction encoding")),
    };
    let msg = match tx_json.message {
        UiMessage::Raw(msg) => msg,
        _ => return Err(anyhow::anyhow!("Unexpected transaction encoding")),
    };
    let mut account_keys = msg.account_keys.clone();
    if let Some(meta) = tx.transaction.meta.as_ref() {
        if let OptionSerializer::Some(loaded_addresses) = meta.loaded_addresses.as_ref() {
            account_keys.extend(loaded_addresses.writable.clone());
            account_keys.extend(loaded_addresses.readonly.clone());
        }
    }
    let is_pump_fun = account_keys
        .iter()
        .any(|k| k.to_string() == PUMP_FUN_MIGRATION);
    let pre_token_balances = match &tx.transaction.meta {
        Some(meta) => meta
            .pre_token_balances
            .clone()
            .unwrap_or(Vec::<UiTransactionTokenBalance>::new()),
        None => Vec::<UiTransactionTokenBalance>::new(),
    };
    let post_token_balances = match &tx.transaction.meta {
        Some(meta) => meta
            .post_token_balances
            .clone()
            .unwrap_or(Vec::<UiTransactionTokenBalance>::new()),
        None => Vec::<UiTransactionTokenBalance>::new(),
    };
    for ix in msg.instructions {
        let program = &account_keys[ix.program_id_index as usize];
        if program.to_string() != RAYDIUM_V4_PROGRAM || ix.accounts.len() != 21 {
            continue;
        }
        if let Ok(decoded) = bs58::decode(&ix.data).into_vec() {
            if !decoded.is_empty() && decoded[0] != 1 {
                // not add liquidity ix
                continue;
            }
        }

        let mut token_mint = account_keys[ix.accounts[8] as usize].as_str();
        let mut pool_wsol_account_idx = ix.accounts[11];
        if token_mint == WSOL_MINT {
            token_mint = account_keys[ix.accounts[9] as usize].as_str();
            pool_wsol_account_idx = ix.accounts[10];
        }
        let pool_pubkey = account_keys[ix.accounts[4] as usize].as_str();
        match pre_token_balances
            .iter()
            .find(|b| b.account_index == pool_wsol_account_idx && b.mint.as_str() == WSOL_MINT)
        {
            Some(balance) => {
                // pool already has wsol
                if balance.ui_token_amount.ui_amount.unwrap_or(0.0) > 0.0 {
                    return Ok(None);
                }
            }
            None => (),
        }
        match post_token_balances
            .iter()
            .find(|b| b.account_index == pool_wsol_account_idx && b.mint.as_str() == WSOL_MINT)
        {
            Some(balance) => {
                // pool doesn't have sol
                if balance.ui_token_amount.ui_amount.unwrap_or(0.0) == 0.0 {
                    return Ok(None);
                }
            }
            None => (),
        }
        return Ok(Some((
            Pubkey::from_str(token_mint).unwrap(),
            Pubkey::from_str(pool_pubkey).unwrap(),
            is_pump_fun,
        )));
    }
    Ok(None)
}

pub fn get_potential_wallets(
    client: &RpcClient,
    tracked_pub_keys: &Vec<Pubkey>,
    last_wallet_balances: &HashMap<String, u64>,
) -> Result<(HashMap<String, u64>, Vec<String>)> {
    let new_wallet_balances = get_wallets_balance(client, tracked_pub_keys)?;
    let mut potential_wallets = vec![];

    for (wallet, cur_balance) in new_wallet_balances.iter() {
        let last_balance: &u64 = last_wallet_balances.get(wallet).unwrap_or(&0);
        if *last_balance > 0 && *last_balance > (*cur_balance) + 100_000_000 {
            potential_wallets.push(wallet.clone());
        }
    }

    Ok((new_wallet_balances, potential_wallets))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_raydium_pool_pubkey_from_tx() {
        let client = RpcClient::new("https://api.mainnet-beta.solana.com".to_string());
        let token_mint = Pubkey::from_str("Gb45enz8fs56Jh6pAowi2d2TtmQj9Sydx2XbbeWgpump").unwrap();
        let expected_pool =
            Pubkey::from_str("42o5sw5zwREj1iTXEv1K41JGk7UGKnb2NyQHoskfLQWV").unwrap();
        let tx_sig = "eZQtAMqeAzsZiYo4EdRigmyNWeoRWyc7LQBreQEKWWGLVrKvoGFyuXjVQiXodnjuM7hunxFz7cdaYYTVxcjHrBT".to_string();

        let (pool_address, pool_wsol_account) =
            get_raydium_pool_pubkey_from_tx(&client, tx_sig, &token_mint)
                .unwrap()
                .unwrap();
        assert_eq!(pool_address, expected_pool);
        assert_eq!(
            pool_wsol_account,
            Pubkey::from_str("EYZLF7YBSiP1RwPz367GJbfydwJYX7EinyxD8qbfLDiU").unwrap()
        );
    }

    #[test]
    fn test_get_new_raydium_pool_from_tx() {
        let client = RpcClient::new("https://api.mainnet-beta.solana.com".to_string());
        let expected_token_mint =
            Pubkey::from_str("Gb45enz8fs56Jh6pAowi2d2TtmQj9Sydx2XbbeWgpump").unwrap();
        let expected_pool =
            Pubkey::from_str("42o5sw5zwREj1iTXEv1K41JGk7UGKnb2NyQHoskfLQWV").unwrap();
        let tx_sig = "eZQtAMqeAzsZiYo4EdRigmyNWeoRWyc7LQBreQEKWWGLVrKvoGFyuXjVQiXodnjuM7hunxFz7cdaYYTVxcjHrBT".to_string();

        let (token_mint, pool_address, is_pump_fun) = get_new_raydium_pool_from_tx(&client, tx_sig)
            .unwrap()
            .unwrap();
        assert_eq!(pool_address, expected_pool);
        assert_eq!(token_mint, expected_token_mint);
        assert_eq!(is_pump_fun, true);
    }

    #[test]
    fn test_get_new_raydium_pool_from_tx_2() {
        let client = RpcClient::new("https://api.mainnet-beta.solana.com".to_string());
        let expected_token_mint =
            Pubkey::from_str("GhTUH7cMPH2xjTj1SWniPLwqZ8xR19eSfCgU44aFpump").unwrap();
        let expected_pool =
            Pubkey::from_str("4yw5nQCDw3cxSGV6XPMkWWzPUmBFvJ5pKKfj3HYZDLVP").unwrap();
        let tx_sig = "4tfJ4JSoYBWi63jjhXmCAftqg5FZdXmmsup6j7QNzBUk2cddG6RiQCmgLD8mU5W5bjNDhGt2w8CTuuXb9GCxFQMC".to_string();

        let (token_mint, pool_address, is_pump_fun) = get_new_raydium_pool_from_tx(&client, tx_sig)
            .unwrap()
            .unwrap();
        assert_eq!(pool_address, expected_pool);
        assert_eq!(token_mint, expected_token_mint);
        assert_eq!(is_pump_fun, true);
    }
}

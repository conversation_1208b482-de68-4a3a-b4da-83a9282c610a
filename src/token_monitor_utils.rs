use alloy::{
    primitives::{aliases::U24, Address, TxHash, U256},
    providers::Provider,
    rpc::types::{Filter, Topic},
};
use anyhow::Result;
use dashmap::DashMap;
use log::{debug, error, info};
use once_cell::sync::Lazy;
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    fs::{self, OpenOptions},
    io::Write,
    path::Path,
    str::FromStr,
    sync::{Arc, Mutex},
    time::{Duration, SystemTime, UNIX_EPOCH},
};

use crate::abis::{
    self,
    FourMeme::{TokenPurchase, TokenSale},
    PancakeV3Pool::Swap,
};
use crate::system_time_serde;

// Constants
pub const FOUR_MEME_CONTRACT: &str = "0x5c952063c7fc8610FFDB798152D69F0B9550762b";
pub const PANCAKE_FACTORY_V3: &str = "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865";
pub const WBNB_ADDRESS: &str = "0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c";
const BNB_TOKEN_MONITORS_FILE: &str = "bnb_token_monitors.json";
const BNB_SOLD_TOKENS_CSV_FILE: &str = "bnb_sold_tokens.csv";
const SAVE_INTERVAL_SECS: u64 = 10;

// Track sent signals to avoid duplicates
pub static SENT_SIGNALS: Lazy<DashMap<String, u8>> = Lazy::new(DashMap::new);
pub static FOMO_MESSAGE_IDS: Lazy<Arc<Mutex<HashMap<String, i32>>>> =
    Lazy::new(|| Arc::new(Mutex::new(HashMap::new())));

// Add a struct to track token monitoring state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BnbTokenMonitorState {
    pub token_mint: String,
    pub symbol: String,
    pub tracked_wallets: Vec<BnbTrackedWalletInfo>,
    pub entry_price: f64,
    #[serde(with = "system_time_serde")]
    pub entry_time: SystemTime,
    pub emit_time: i64,
    pub telegram_link: String,
    pub highest_price: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BnbTrackedWalletInfo {
    pub wallet: String,
    pub initial_amount_scaled: u64,
    pub current_amount_scaled: u64,
    pub max_amount_scaled: u64,
    pub win_rate: f64,
}

// Global state for token monitoring
pub static BNB_TOKEN_MONITORS: Lazy<Arc<Mutex<HashMap<String, BnbTokenMonitorState>>>> =
    Lazy::new(|| Arc::new(Mutex::new(HashMap::new())));

// Save BNB_TOKEN_MONITORS to disk
pub fn save_bnb_token_monitors() -> Result<()> {
    // Create a clone of the monitors while holding the lock
    let monitors: HashMap<String, BnbTokenMonitorState>;
    {
        let token_monitors = BNB_TOKEN_MONITORS
            .lock()
            .map_err(|e| anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e))?;
        monitors = token_monitors.clone();
    }

    // Write to a temporary file first (now outside the lock)
    let temp_file = format!("{}.tmp", BNB_TOKEN_MONITORS_FILE);
    let json = serde_json::to_string_pretty(&monitors)?;
    fs::write(&temp_file, json)?;

    // Then rename to the actual file (more atomic)
    fs::rename(&temp_file, BNB_TOKEN_MONITORS_FILE)?;

    debug!("Saved {} BNB token monitors to disk", monitors.len());
    Ok(())
}

// Load BNB_TOKEN_MONITORS from disk
pub async fn load_bnb_token_monitors() -> Result<()> {
    if !Path::new(BNB_TOKEN_MONITORS_FILE).exists() {
        return Ok(());
    }

    let json = fs::read_to_string(BNB_TOKEN_MONITORS_FILE)?;
    let monitors: HashMap<String, BnbTokenMonitorState> = serde_json::from_str(&json)?;

    // Acquire lock once to update BNB_TOKEN_MONITORS
    {
        let mut token_monitors = BNB_TOKEN_MONITORS
            .lock()
            .map_err(|e| anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e))?;
        *token_monitors = monitors.clone();
    }

    // Update FOMO_MESSAGE_IDS
    if let Ok(mut message_ids) = FOMO_MESSAGE_IDS.lock() {
        for (key, value) in &monitors {
            if let Ok(id) = value
                .telegram_link
                .split("/")
                .last()
                .unwrap()
                .parse::<i32>()
            {
                message_ids.insert(key.clone(), id);
            }
        }
    }

    info!("Loaded {} BNB token monitors from disk", monitors.len());
    Ok(())
}

// Start a background task to periodically save BNB_TOKEN_MONITORS
pub async fn start_bnb_token_monitors_persistence() {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(SAVE_INTERVAL_SECS));
        loop {
            interval.tick().await;
            if let Err(e) = save_bnb_token_monitors() {
                error!("Failed to save BNB token monitors: {:?}", e);
            }
        }
    });
}

// Record sold token data to CSV file
pub fn record_bnb_sold_token_to_csv(
    token_address: &str,
    symbol: &str,
    price_change: f64,
    minutes_monitored: u64,
    related_wallets: Vec<String>,
) -> Result<()> {
    let symbol = symbol.trim_end_matches('\0');
    let file_exists = Path::new(BNB_SOLD_TOKENS_CSV_FILE).exists();

    // Open file in append mode, create if it doesn't exist
    let mut file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(BNB_SOLD_TOKENS_CSV_FILE)?;

    // Write header if the file is new
    if !file_exists {
        writeln!(
            file,
            "timestamp,token_address,symbol,price_change,minutes_monitored,related_wallets"
        )?;
    }

    // Get current timestamp
    let now = SystemTime::now();
    let unix_timestamp = now
        .duration_since(UNIX_EPOCH)
        .unwrap_or(Duration::from_secs(0))
        .as_secs();
    let related_wallets_str = related_wallets.join("|");

    // Write data
    writeln!(
        file,
        "{},{},{},{:.4},{},{}",
        unix_timestamp, token_address, symbol, price_change, minutes_monitored, related_wallets_str
    )?;

    info!(
        "Recorded sold BNB token {} ({}) to CSV with price change {:.2}%, monitored for {} minutes, related wallets: {}",
        token_address,
        symbol,
        price_change * 100.0,
        minutes_monitored,
        related_wallets.len()
    );

    Ok(())
}

pub async fn get_token_info<T: Provider + Clone>(
    provider: T,
    token_address: &str,
) -> Result<(String, u8)> {
    let token_address = Address::from_str(token_address)?;
    let contract = abis::ERC20::new(token_address, provider);
    let symbol = contract.symbol().call().await?;
    let decimals = contract.decimals().call().await?;
    info!(
        "{} Token Symbol: {}, Decimals: {}",
        token_address, symbol._0, decimals._0
    );
    Ok((symbol._0, decimals._0))
}

#[derive(Debug)]
pub struct TokenStatus {
    pub is_active: bool,
    pub pool_address: Option<String>,
    pub price_per_bnb: Option<f64>,
}

/// Get latest token transfer logs from four.meme contract
async fn get_latest_four_meme_transfer<T: Provider>(
    provider: T,
    token_address: &str,
    blocks: u64,
) -> Result<Option<alloy::rpc::types::Log>> {
    let token_address = Address::from_str(token_address)?;
    let four_meme_address = Address::from_str(FOUR_MEME_CONTRACT)?;

    // Get current block number
    let current_block = provider.get_block_number().await?;
    let from_block = current_block - blocks;

    let filter = Filter::new()
        .address(token_address)
        .from_block(from_block)
        .to_block(current_block)
        .event("Transfer(address,address,uint256)")
        .topic1(Topic::from(four_meme_address.into_word()));

    let transfer_out_logs = provider.get_logs(&filter).await?;
    let filter = Filter::new()
        .address(token_address)
        .from_block(from_block)
        .to_block(current_block)
        .event("Transfer(address,address,uint256)")
        .topic2(Topic::from(four_meme_address.into_word()));
    let transfer_in_logs = provider.get_logs(&filter).await?;

    let mut logs = transfer_out_logs;
    logs.extend(transfer_in_logs);

    if logs.is_empty() {
        return Ok(None);
    }

    // Sort logs by block number and log index
    logs.sort_by(|a, b| match a.block_number.cmp(&b.block_number) {
        std::cmp::Ordering::Equal => a.log_index.cmp(&b.log_index),
        other => other,
    });
    Ok(logs.last().cloned())
}

/// Get latest Swap events from pancake pool
async fn get_latest_pool_swap<T: Provider>(
    provider: T,
    pool_address: &str,
    blocks: u64,
) -> Result<Option<alloy::rpc::types::Log>> {
    let pool_address = Address::from_str(pool_address)?;

    // Get current block number
    let current_block = provider.get_block_number().await?;
    let from_block = current_block - blocks;

    let filter = Filter::new()
        .address(pool_address)
        .from_block(from_block)
        .to_block(current_block)
        .event("Swap(address,address,int256,int256,uint160,uint128,int24,uint128,uint128)");

    let mut logs = provider.get_logs(&filter).await?;
    if logs.is_empty() {
        return Ok(None);
    }

    // Sort logs by block number and log index
    logs.sort_by(|a, b| match a.block_number.cmp(&b.block_number) {
        std::cmp::Ordering::Equal => a.log_index.cmp(&b.log_index),
        other => other,
    });

    Ok(logs.last().cloned())
}

pub fn get_token_price_per_bnb(token_amount: U256, bnb_amount: U256) -> f64 {
    let token_amount_scaled = (token_amount / U256::from(1e18 as u64)).to::<u64>();
    let bnb_amount_scaled = (bnb_amount / U256::from(1e9 as u64)).to::<u64>();
    let price_per_bnb_scaled = (bnb_amount_scaled as f64) / (token_amount_scaled as f64);
    price_per_bnb_scaled / 1e9
}

/// Check if a token is still in four.meme and get its pool address and price
pub async fn check_token_status<T: Provider + Clone>(
    provider: T,
    token_address: &str,
) -> Result<TokenStatus> {
    // First check if token has a pancake pool
    let factory =
        abis::PancakeV3Factory::new(Address::from_str(PANCAKE_FACTORY_V3)?, provider.clone());

    let token_address = Address::from_str(token_address)?;
    let wbnb_address = Address::from_str(WBNB_ADDRESS)?;

    let pool_address = factory
        .getPool(token_address, wbnb_address, U24::from(2500))
        .call()
        .await?;
    let pool_address = pool_address.pool;

    // If pool address is empty, token is in four.meme
    if pool_address == Address::ZERO {
        // Get latest transfer logs from four.meme
        let transfer_log =
            get_latest_four_meme_transfer(provider.clone(), token_address.to_string().as_str(), 20)
                .await?;

        // Get latest tx hash
        if let Some(latest_log) = transfer_log {
            if let Some(tx_hash) = latest_log.transaction_hash {
                // Get transaction logs
                let hash = TxHash::from(tx_hash);
                let receipt = match provider.get_transaction_receipt(hash).await {
                    Ok(Some(receipt)) => receipt,
                    _ => {
                        error!("Failed to get transaction receipt");
                        return Ok(TokenStatus {
                            is_active: false,
                            pool_address: None,
                            price_per_bnb: None,
                        });
                    }
                };
                let logs = receipt.logs();
                for log in logs {
                    if let Ok(event) = log.log_decode::<TokenPurchase>() {
                        let price_per_bnb = get_token_price_per_bnb(
                            event.data().token_amount,
                            event.data().bnb_amount,
                        );
                        return Ok(TokenStatus {
                            is_active: true,
                            pool_address: Some(FOUR_MEME_CONTRACT.to_string()),
                            price_per_bnb: Some(price_per_bnb),
                        });
                    }
                    if let Ok(event) = log.log_decode::<TokenSale>() {
                        let price_per_bnb = get_token_price_per_bnb(
                            event.data().token_amount,
                            event.data().bnb_amount,
                        );
                        return Ok(TokenStatus {
                            is_active: true,
                            pool_address: Some(FOUR_MEME_CONTRACT.to_string()),
                            price_per_bnb: Some(price_per_bnb),
                        });
                    }
                }
            }
        }
    } else {
        // Token is in pancake swap
        // Get latest Swap events from pool
        let swap_log =
            get_latest_pool_swap(provider.clone(), pool_address.to_string().as_str(), 20).await?;

        if let Some(latest_log) = swap_log {
            let event = latest_log.log_decode::<Swap>()?;
            let price_per_bnb = get_token_price_per_bnb(
                event.data().amount0.unsigned_abs(),
                event.data().amount1.unsigned_abs(),
            );
            return Ok(TokenStatus {
                is_active: true,
                pool_address: Some(pool_address.to_string()),
                price_per_bnb: Some(price_per_bnb),
            });
        }
    }

    // If no events found, token is not active
    Ok(TokenStatus {
        is_active: false,
        pool_address: None,
        price_per_bnb: None,
    })
}

#[cfg(test)]
mod tests {
    use std::env;

    use super::*;
    use alloy::providers::ProviderBuilder;
    use reqwest::Url;

    #[tokio::test]
    async fn test_check_token_status_four_meme() {
        // Create a provider
        dotenv::dotenv().ok();
        let rpc_url = env::var("BNB_RPC_URL").unwrap();
        let url = Url::parse(&rpc_url).unwrap();
        let provider = ProviderBuilder::new().on_http(url);

        // Test token address
        let token_address = "0x795d2710e383f33fbebe980a155b29757b6703f3";

        // Check token status
        let result = check_token_status(provider, token_address)
            .await
            .expect("Failed to check token status");

        // Assert results
        println!("result: {:?}", result);
        assert!(result.is_active, "Token should be active");
        assert_eq!(
            result.pool_address,
            Some(FOUR_MEME_CONTRACT.to_string()),
            "Pool address should be four.meme contract"
        );
        assert!(result.price_per_bnb.is_some(), "Price should be available");
    }
}

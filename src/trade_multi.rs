use crate::{acquire_trading_lock, get_sol_price, get_token_info, PumpFunPool};
use crate::{get_raydium_pool_implementation, wallet::WalletContext, RaydiumPoolType, TradeState};
use crate::{get_token_account_balance, send_tx_via_jito, PoolBuyInfo};
use anyhow::{anyhow, Result};
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{debug, error, info, warn};
use once_cell::sync::Lazy;
use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;
use spl_associated_token_account::get_associated_token_address;
use std::collections::{HashMap, VecDeque};
use std::sync::atomic::AtomicBool;
use std::sync::{Arc, Mutex};
use std::{
    str::FromStr,
    time::{Duration, SystemTime},
};
use tokio::time::sleep;

pub const LEAST_BUY_COUNT: u32 = 3;
pub const LEAST_BUY_THRESHOLD: u32 = 2;

pub static BUY_SIGNAL_TIME: Lazy<Mutex<HashMap<String, i64>>> =
    Lazy::new(|| Mutex::new(HashMap::new()));

// Struct to store wallet buy information with timestamp
#[derive(Debug, Clone)]
pub struct WalletBuyInfo {
    pub wallet: String,
    pub timestamp: SystemTime,
    pub token_amount: u64,
    pub current_balance: u64,
}

// Shared state for active trades
pub struct ActiveTrades {
    // token to tracked wallets
    pub trades: HashMap<String, Vec<String>>,
    // token to recent buys
    pub recent_buys: HashMap<String, VecDeque<WalletBuyInfo>>,
}

const TRACK_BUY_WINDOW_MINUTES: u64 = 30;

impl ActiveTrades {
    pub fn new() -> Self {
        Self {
            trades: HashMap::new(),
            recent_buys: HashMap::new(),
        }
    }

    pub fn is_token_trading(&self, token_mint: &str) -> bool {
        self.trades.contains_key(token_mint)
    }

    pub fn add_trade(&mut self, token_mint: String, target_wallet: String) -> bool {
        if self.is_token_trading(&token_mint) {
            false
        } else {
            self.trades.insert(token_mint, vec![target_wallet]);
            true
        }
    }

    pub fn remove_trade(&mut self, token_mint: &str) {
        self.trades.remove(token_mint);
        // self.recent_buys.remove(token_mint);
    }

    pub fn record_buy(&mut self, token_mint: &str, wallet: String, token_amount: u64) {
        let recent_buys = self.recent_buys.entry(token_mint.to_string()).or_default();

        if !recent_buys.iter().any(|buy| buy.wallet == wallet) {
            recent_buys.push_back(WalletBuyInfo {
                wallet,
                timestamp: SystemTime::now(),
                token_amount,
                current_balance: token_amount,
            });
        }
    }

    pub fn update_wallet_balance(&mut self, token_mint: &str, wallet: &str, current_balance: u64) {
        if let Some(recent_buys) = self.recent_buys.get_mut(token_mint) {
            for buy_info in recent_buys.iter_mut() {
                if buy_info.wallet == wallet {
                    buy_info.current_balance = current_balance;
                    break;
                }
            }
        }
    }

    pub fn get_recent_buys(&mut self, token_mint: &str) -> Vec<WalletBuyInfo> {
        let time_threshold = SystemTime::now()
            .checked_sub(Duration::from_secs(TRACK_BUY_WINDOW_MINUTES * 60))
            .unwrap_or_else(SystemTime::now);

        if let Some(recent_buys) = self.recent_buys.get_mut(token_mint) {
            // Remove buys too old
            while !recent_buys.is_empty() {
                if let Some(front) = recent_buys.front() {
                    if front.timestamp < time_threshold {
                        recent_buys.pop_front();
                        continue;
                    }
                }
                break;
            }

            // Return a copy of the remaining buys
            return recent_buys
                .iter()
                .filter(|buy| buy.current_balance >= buy.token_amount * 5 / 10) // Only include wallets that still have at least 50% of their tokens
                .cloned()
                .collect();
        }

        Vec::new()
    }

    pub fn get_close_buys(&mut self, token_mint: &str) -> Vec<WalletBuyInfo> {
        let three_minutes_ago = SystemTime::now()
            .checked_sub(Duration::from_secs(5 * 60))
            .unwrap_or_else(SystemTime::now);

        if let Some(recent_buys) = self.recent_buys.get_mut(token_mint) {
            // Return a copy of the remaining buys
            return recent_buys
                .iter()
                .filter(|buy| {
                    buy.timestamp > three_minutes_ago
                        && buy.current_balance >= buy.token_amount * 5 / 10
                })
                .cloned()
                .collect();
        }

        Vec::new()
    }
}

pub async fn process_trade_multi(
    trade_info: PoolBuyInfo,
    client: &RpcClient,
    jito_sdk: &JitoJsonRpcSDK,
    jito_tip_account: Pubkey,
    wallet: &WalletContext,
    my_wallet_trading: Arc<AtomicBool>,
    active_trades: &Arc<Mutex<ActiveTrades>>,
) -> Option<TradeState> {
    let token_mint = match &trade_info {
        PoolBuyInfo::Raydium(info) => info.token_mint.as_ref(),
        PoolBuyInfo::PumpFun(info) => info.token_mint.as_ref(),
        PoolBuyInfo::PumpFunAmm(_) => return None,
        PoolBuyInfo::NotBuy => return None,
    };
    let token_mint_pubkey = Pubkey::from_str(token_mint).unwrap();

    // Get recent buys for this token
    let recent_buys = {
        let mut active_trades_guard = active_trades.lock().unwrap();
        active_trades_guard.get_recent_buys(token_mint)
    };

    // Check if we have at least 2 different wallets buying this token
    if (recent_buys.len() as u32) < LEAST_BUY_THRESHOLD {
        info!(
            "Not enough wallets ({}) buying token {}",
            recent_buys.len(),
            token_mint
        );
        return None;
    }

    // Check if all tracked wallets still have their tokens
    let mut qualified_wallets = Vec::new();
    for buy_info in &recent_buys {
        let wallet_pubkey = match Pubkey::from_str(&buy_info.wallet) {
            Ok(pubkey) => pubkey,
            Err(_) => continue,
        };

        let token_account = get_associated_token_address(&wallet_pubkey, &token_mint_pubkey);
        match get_token_account_balance(client, &token_account) {
            Ok(balance) => {
                // Check if wallet still has at least 50% of their initial buy amount
                if balance >= buy_info.token_amount * 5 / 10 {
                    qualified_wallets.push(buy_info.wallet.clone());
                } else {
                    info!(
                        "Wallet {} reduced token balance from {} to {}",
                        buy_info.wallet, buy_info.token_amount, balance
                    );
                    // Update the current balance in active_trades
                    let mut active_trades_guard = active_trades.lock().unwrap();
                    active_trades_guard.update_wallet_balance(
                        token_mint,
                        &buy_info.wallet,
                        balance,
                    );
                }
            }
            Err(e) => {
                error!(
                    "Failed to get token balance for wallet {}: {:?}",
                    buy_info.wallet, e
                );
            }
        }
    }

    // Check if we still have at least 3 qualified wallets
    if (qualified_wallets.len() as u32) < LEAST_BUY_THRESHOLD {
        info!(
            "Not enough qualified wallets ({}/{}) for token {}",
            qualified_wallets.len(),
            recent_buys.len(),
            token_mint
        );
        return None;
    }

    info!(
        "Found {} qualified wallets buying token {}",
        qualified_wallets.len(),
        token_mint
    );

    // Check wallet balance to determine how much SOL to use
    let buy_sol_amount = match client.get_balance(&wallet.payer()) {
        Ok(balance) => {
            if balance < 1_000_000_000 {
                warn!("Wallet balance is less than 1 SOL, skipping trade");
                return None;
            }
            let balance_amount = balance as f64 / 1_000_000_000.0 / 10.0;
            let pool_amount = match &trade_info {
                PoolBuyInfo::Raydium(info) => info.pool_sol_amount as f64 / 1_000_000_000.0 * 0.015,
                PoolBuyInfo::PumpFun(info) => info.pool_sol_amount as f64 / 1_000_000_000.0 * 0.015,
                PoolBuyInfo::PumpFunAmm(_) => return None,
                PoolBuyInfo::NotBuy => return None,
            };
            (balance_amount.min(pool_amount) / 0.1).floor() * 0.1
        }
        Err(e) => {
            error!("Error getting balance: {:?}", e);
            return None;
        }
    };

    // Call the new  parse_and_buy_multi function
    match parse_and_buy_multi(
        &trade_info,
        qualified_wallets,
        client,
        buy_sol_amount,
        jito_sdk,
        jito_tip_account,
        wallet,
        my_wallet_trading,
    )
    .await
    {
        Ok(state) => state,
        Err(e) => {
            warn!("parse_and_buy_multi error: {}", e);
            None
        }
    }
}

pub async fn parse_and_buy_multi(
    trade_info: &PoolBuyInfo,
    qualified_wallets: Vec<String>,
    client: &RpcClient,
    buy_sol_f: f64,
    jito_sdk: &JitoJsonRpcSDK,
    jito_tip_account: Pubkey,
    wallet: &WalletContext,
    my_wallet_trading: Arc<AtomicBool>,
) -> Result<Option<TradeState>> {
    let is_pump = match trade_info {
        PoolBuyInfo::Raydium(_) => false,
        PoolBuyInfo::PumpFun(_) => true,
        PoolBuyInfo::PumpFunAmm(_) => return Ok(None),
        PoolBuyInfo::NotBuy => return Ok(None),
    };
    let token_mint = match trade_info {
        PoolBuyInfo::Raydium(info) => info.token_mint.as_ref(),
        PoolBuyInfo::PumpFun(info) => info.token_mint.as_ref(),
        PoolBuyInfo::PumpFunAmm(_) => return Ok(None),
        PoolBuyInfo::NotBuy => return Ok(None),
    };
    let timestamp = match trade_info {
        PoolBuyInfo::Raydium(info) => info.timestamp,
        PoolBuyInfo::PumpFun(info) => info.timestamp,
        PoolBuyInfo::PumpFunAmm(_) => return Ok(None),
        PoolBuyInfo::NotBuy => return Ok(None),
    };
    let pool_pubkey = match trade_info {
        PoolBuyInfo::Raydium(info) => info.pool_pubkey,
        PoolBuyInfo::PumpFun(info) => info.bonding_curve,
        PoolBuyInfo::PumpFunAmm(_) => return Ok(None),
        PoolBuyInfo::NotBuy => return Ok(None),
    };
    let token_mint_pubkey = Pubkey::from_str(token_mint).unwrap();

    let age = SystemTime::now().duration_since(timestamp)?;
    info!(
        "Found multi-wallet trade target! {} wallets buy {} within last {} minutes, parse delay: {}s",
        qualified_wallets.len(),
        token_mint,
        TRACK_BUY_WINDOW_MINUTES,
        age.as_secs()
    );
    let token_info = get_token_info(&client, &token_mint_pubkey).await;
    if let Err(e) = token_info {
        return Err(anyhow!("Error getting token info: {:?}", e));
    }
    let (symbol, decimal) = token_info.unwrap();
    info!("Token: {} {}", symbol, token_mint);
    info!("Qualified wallets: {}", qualified_wallets.join(", "));

    // Calculate buy price (not considering decimals)
    let pool_impl = if is_pump {
        Box::new(PumpFunPool {
            token_mint: Pubkey::from_str(token_mint).unwrap(),
        })
    } else {
        get_raydium_pool_implementation(&client, &pool_pubkey, &RaydiumPoolType::OpenBook).unwrap()
    };
    let current_price = pool_impl.get_price(&client, &pool_pubkey).await?;
    let sol_price = get_sol_price().await?;
    info!("Buying {} using {:.2} SOL", symbol, buy_sol_f);

    // Build and send buy transaction
    let buy_sol_amount = if is_pump {
        // for pump's slippage
        (buy_sol_f * 1e9 * 1.2) as u64
    } else {
        (buy_sol_f * 1e9) as u64
    };
    let output_amount = if is_pump {
        ((buy_sol_f / current_price) * 10f64.powi(decimal as i32)) as u64
    } else {
        // for raydium's slippage
        (((buy_sol_f / current_price) * 10f64.powi(decimal as i32)) / 1.2) as u64
    };

    // send buy transaction in next 30 seconds
    let token_account = get_associated_token_address(&wallet.payer(), &token_mint_pubkey);

    // Get the target wallets' token accounts for monitoring
    let mut target_token_accounts = Vec::new();
    for wallet_str in &qualified_wallets {
        if let Ok(wallet_pubkey) = Pubkey::from_str(wallet_str) {
            let token_account = get_associated_token_address(&wallet_pubkey, &token_mint_pubkey);
            target_token_accounts.push((wallet_pubkey, token_account));
        }
    }

    let _guard = acquire_trading_lock(&my_wallet_trading).await;

    let initial_token_balance = match client.get_token_account_balance(&token_account) {
        Ok(bal) => bal.amount.parse::<u64>()?,
        Err(_) => 0,
    };
    let initial_sol_balance = client.get_balance(&wallet.payer())?;
    let self_send_sol = initial_sol_balance - (buy_sol_amount * 3 / 2);

    // Record initial balances of target wallets for later comparison
    let mut initial_target_balances = HashMap::new();
    for (wallet_pubkey, token_account) in &target_token_accounts {
        match get_token_account_balance(&client, token_account) {
            Ok(balance) => {
                initial_target_balances.insert(wallet_pubkey.to_string(), balance);
            }
            Err(e) => {
                error!(
                    "Failed to get token balance for wallet {}: {:?}",
                    wallet_pubkey, e
                );
            }
        }
    }

    let start_time = SystemTime::now();
    let mut cnt = 0;
    while SystemTime::now().duration_since(start_time)? < Duration::from_secs(30) {
        cnt += 1;
        if cnt > 1 {
            sleep(Duration::from_millis(300)).await;
        }
        if cnt > 1 && qualified_wallets.len() > 1 {
            // Check if target wallets still hold their tokens
            let mut qualified_count = 0;
            for (wallet_pubkey, token_account) in &target_token_accounts {
                if let Ok(current_balance) = get_token_account_balance(&client, token_account) {
                    if let Some(initial_balance) =
                        initial_target_balances.get(&wallet_pubkey.to_string())
                    {
                        if current_balance >= *initial_balance * 5 / 10 {
                            qualified_count += 1;
                        } else {
                            info!(
                                "Wallet {} reduced balance from {} to {}",
                                wallet_pubkey, initial_balance, current_balance
                            );
                        }
                    }
                }
            }

            // If we don't have at least 2 qualified wallets anymore, abort
            if qualified_count < LEAST_BUY_THRESHOLD {
                info!(
                    "Only {} qualified wallets remain, aborting buy",
                    qualified_count
                );
                break;
            }
        }

        info!(
            "Sending tx to buy {} at {:.6} USD",
            symbol,
            current_price * sol_price
        );
        let swap_tx = pool_impl
            .build_swap_transaction(
                &client,
                wallet.signer(),
                &pool_pubkey,
                buy_sol_amount,
                output_amount,
                &token_mint_pubkey,
                true,
                &jito_tip_account,
                1_200_000 + cnt * 100_000,
                4_000_000, // 4 lamports priority fee
                self_send_sol,
            )
            .await?;
        let uuid = match send_tx_via_jito(&swap_tx, &jito_sdk).await? {
            Some(uuid) => uuid,
            None => {
                continue;
            }
        };
        debug!("Buy order sent! Bundle UUID: {}", uuid);

        let bought_amount = match client.get_token_account_balance(&token_account) {
            Ok(balance) => match balance.amount.parse::<u64>() {
                Ok(balance) if balance > initial_token_balance => balance - initial_token_balance,
                _ => continue,
            },
            Err(_) => continue,
        };
        let token_balance_ui = (bought_amount as f64) / (10f64.powi(decimal as i32));
        let bought_sol_f = if is_pump {
            let current_sol_balance = match client.get_balance(&wallet.payer()) {
                Ok(bal) => bal,
                Err(_) => 0,
            };
            (initial_sol_balance - current_sol_balance) as f64 / 1_000_000_000.0
        } else {
            buy_sol_f
        };
        let bought_price = bought_sol_f / token_balance_ui;
        let sol_price = match get_sol_price().await {
            Ok(price) => price,
            Err(e) => {
                error!("Failed to get SOL price: {:?}", e);
                140.0
            }
        };
        return Ok(Some(TradeState {
            target_wallet: Pubkey::from_str(&qualified_wallets[0]).unwrap_or_default(),
            token_mint: token_mint.to_string(),
            pool_pubkey: pool_pubkey,
            bought_price: bought_price,
            bought_sol: bought_sol_f,
            amount: bought_amount,
            decimals: decimal,
            bought_time: SystemTime::now(),
            initial_price_usd: sol_price * bought_price,
            last_target_hit_time: SystemTime::now(),
            highest_multiplier_hit: 1.0,
            is_from_pump: is_pump,
            target_highest_balance: 0,
            last_target_balance_ratio: 1.0,
            last_balance_ratio_sell: 1.0,
        }));
    }

    // make sure tx is confirmed
    sleep(Duration::new(30, 0)).await;
    let bought_amount = match client.get_token_account_balance(&token_account) {
        Ok(balance) => match balance.amount.parse::<u64>() {
            Ok(balance) if balance > initial_token_balance => balance - initial_token_balance,
            _ => 0,
        },
        Err(_) => 0,
    };
    if bought_amount == 0 {
        info!("Didn't buy token {}", token_mint.to_string());
        return Ok(None);
    }
    let token_balance_ui = (bought_amount as f64) / (10f64.powi(decimal as i32));
    let bought_sol_f = if is_pump {
        let current_sol_balance = match client.get_balance(&wallet.payer()) {
            Ok(bal) => bal,
            Err(_) => 0,
        };
        (initial_sol_balance - current_sol_balance) as f64 / 1_000_000_000.0
    } else {
        buy_sol_f
    };
    let bought_price = bought_sol_f / token_balance_ui;
    let sol_price = match get_sol_price().await {
        Ok(price) => price,
        Err(e) => {
            error!("Failed to get SOL price: {:?}", e);
            140.0
        }
    };

    return Ok(Some(TradeState {
        target_wallet: Pubkey::from_str(&qualified_wallets[0]).unwrap_or_default(),
        token_mint: token_mint.to_string(),
        pool_pubkey: pool_pubkey,
        bought_price: bought_price,
        bought_sol: bought_sol_f,
        amount: bought_amount,
        decimals: decimal,
        bought_time: SystemTime::now(),
        initial_price_usd: sol_price * bought_price,
        last_target_hit_time: SystemTime::now(),
        highest_multiplier_hit: 1.0,
        is_from_pump: is_pump,
        target_highest_balance: 0,
        last_target_balance_ratio: 1.0,
        last_balance_ratio_sell: 1.0,
    }));
}

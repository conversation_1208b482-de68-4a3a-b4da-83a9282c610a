use crate::format_number;
use anyhow::{anyhow, Result};
use serde::Deserialize;
use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;
use teloxide::types::ChatId;

/// Struct to hold detailed token info
#[derive(Debug, Clone)]
pub struct DetailedTokenInfo {
    pub name: String,
    pub symbol: String,
    pub contract_address: String,
    pub price_usd: f64,
    pub market_cap: f64,
    pub liquidity: f64,
}

/// Format token info into a nicely formatted message
pub fn format_token_info(
    info: &DetailedTokenInfo,
    chat_id: ChatId,
    query: &str,
    get_url_with_ref: impl Fn(&str, &str, ChatId, &str) -> String,
    lang: Option<&str>,
) -> String {
    match lang {
        Some("zh-tw") => format!(
            "{} ({}) ⛓️ Solana\n\
            <code>{}</code>\n\n\
            🔵  價格: ${}\n\
            🟡  市值: ${}\n\
            🔵  流動性: ${}\n\
            🟠  <a href=\"{}\">查看更多資訊</a>\n\n\
            🔔  精選訊號頻道: https://t.me/+ril5PG0N1p4zODA1",
            info.symbol,
            info.name,
            info.contract_address,
            format_number(info.price_usd),
            format_number(info.market_cap),
            format_number(info.liquidity),
            get_url_with_ref(
                "https://www.kryptogo.xyz/token/sol",
                &info.contract_address,
                chat_id,
                query,
            ),
        ),
        _ => format!(
            "{} ({}) ⛓️ Solana\n\
            <code>{}</code>\n\n\
            🔵  Price: ${}\n\
            🟡  MarketCap: ${}\n\
            🔵  Liquidity: ${}\n\
            🟠  <a href=\"{}\">Check more Info</a>\n\n\
            🔔  Featured Signal Channel: https://t.me/+ril5PG0N1p4zODA1",
            info.symbol,
            info.name,
            info.contract_address,
            format_number(info.price_usd),
            format_number(info.market_cap),
            format_number(info.liquidity),
            get_url_with_ref(
                "https://www.kryptogo.xyz/token/sol",
                &info.contract_address,
                chat_id,
                query,
            ),
        ),
    }
}

/// Get detailed token information from a token pubkey
pub async fn get_detailed_token_info(
    _client: &RpcClient,
    pubkey: &Pubkey,
) -> Result<DetailedTokenInfo> {
    let url = format!(
        "https://www.okx.com/priapi/v1/dx/market/v2/latest/info?tokenContractAddress={}&chainId=501",
        pubkey.to_string()
    );

    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .header("accept", "application/json")
        .send()
        .await?;

    #[derive(Deserialize)]
    struct OkxResponse {
        code: i32,
        data: OkxTokenData,
    }
    #[derive(Deserialize)]
    struct OkxTokenData {
        #[serde(rename = "tokenName")]
        token_name: String,
        #[serde(rename = "tokenSymbol")]
        token_symbol: String,
        #[serde(rename = "tokenContractAddress")]
        token_contract_address: String,
        price: String,
        #[serde(rename = "marketCap")]
        market_cap: String,
        liquidity: String,
    }

    let okx_data = response.json::<OkxResponse>().await?;
    if okx_data.code != 0 {
        return Err(anyhow!("Failed to get token info from OKX API"));
    }

    let data = okx_data.data;
    Ok(DetailedTokenInfo {
        name: data.token_name,
        symbol: data.token_symbol,
        contract_address: data.token_contract_address,
        price_usd: data.price.parse().unwrap_or(0.0),
        market_cap: data.market_cap.parse().unwrap_or(0.0),
        liquidity: data.liquidity.parse().unwrap_or(0.0),
    })
}

/// Extracts a Solana contract address from a message.
pub fn extract_contract_address(text: &str) -> Option<&str> {
    // Simple regex to match Solana addresses (44 characters)
    text.split_whitespace().find(|word| {
        (word.len() == 44 || word.len() == 43)
            && word.chars().all(|c| c.is_ascii_alphanumeric())
            && *word != "So11111111111111111111111111111111111111112"
            && *word != "11111111111111111111111111111111"
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_detailed_token_info() {
        let client = RpcClient::new("https://api.mainnet-beta.solana.com".to_string());
        let pubkey = Pubkey::from_str_const("1Qf8gESP4i6CFNWerUSDdLKJ9U1LpqTYvjJ2MM4pain");

        let result = get_detailed_token_info(&client, &pubkey).await;
        match result {
            Ok(info) => {
                println!("Token Info:");
                println!("Name: {}", info.name);
                println!("Symbol: {}", info.symbol);
                println!("Contract Address: {}", info.contract_address);
                println!("Price USD: ${:.6}", info.price_usd);
                println!("Market Cap: ${:.2}", info.market_cap);
                println!("Liquidity: ${:.2}", info.liquidity);
            }
            Err(e) => println!("Error getting token info: {}", e),
        }
    }
}

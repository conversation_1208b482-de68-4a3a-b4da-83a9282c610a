use anyhow::Result;
use log::error;
use serde::{Deserialize, Serialize};
use std::{
    fs::{self, create_dir_all},
    path::Path,
    time::{SystemTime, UNIX_EPOCH},
};

use crate::token_analysis::BuyRecord;

pub const CACHE_DIR: &str = "cache/buy_records";

#[derive(Debug, Serialize, Deserialize)]
pub struct BuyRecordCache {
    pub token_address: String,
    pub bonding_curve_address: String,
    pub creation_time: i64,
    pub buy_records: Vec<CachedBuyRecord>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CachedBuyRecord {
    pub source_address: String,
    pub sol_amount: f64,
    pub timestamp: i64,
}

/// Get the cache file path for a token address
pub fn get_cache_path(token_address: &str) -> String {
    format!("{}/{}.json", CACHE_DIR, token_address)
}

/// Save buy records to cache
pub fn save_buy_records_to_cache(
    token_address: &str,
    bonding_curve_address: &str,
    creation_time: i64,
    buy_records: &[BuyRecord],
) -> Result<Vec<CachedBuyRecord>> {
    // Create cache directory if it doesn't exist
    create_dir_all(CACHE_DIR)?;

    // Convert BuyRecord to CachedBuyRecord
    let cached_records: Vec<CachedBuyRecord> = buy_records
        .iter()
        .map(|r| CachedBuyRecord {
            source_address: r.source_address.clone(),
            sol_amount: r.sol_amount,
            timestamp: r.timestamp,
        })
        .collect();

    let cache = BuyRecordCache {
        token_address: token_address.to_string(),
        bonding_curve_address: bonding_curve_address.to_string(),
        creation_time,
        buy_records: cached_records.clone(),
    };

    let json = serde_json::to_string_pretty(&cache)?;
    fs::write(get_cache_path(token_address), json)?;

    Ok(cached_records)
}

/// Load buy records from cache
pub fn load_buy_records_from_cache(token_address: &str) -> Option<BuyRecordCache> {
    let cache_path = get_cache_path(token_address);
    if Path::new(&cache_path).exists() {
        match fs::read_to_string(&cache_path) {
            Ok(content) => match serde_json::from_str::<BuyRecordCache>(&content) {
                Ok(cache) => Some(cache),
                Err(e) => {
                    error!("Error parsing cache file: {}", e);
                    None
                }
            },
            Err(e) => {
                error!("Error reading cache file: {}", e);
                None
            }
        }
    } else {
        None
    }
}

/// Get current timestamp in seconds
pub fn current_timestamp() -> i64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs() as i64
}

/// Ensure cache directory exists
pub fn ensure_cache_dir() -> Result<()> {
    if !Path::new(CACHE_DIR).exists() {
        create_dir_all(CACHE_DIR)?;
    }
    Ok(())
}

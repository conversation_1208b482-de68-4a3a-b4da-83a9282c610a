use log::info;
use std::{sync::atomic::AtomicBool, time::Duration};
use tokio::time::sleep;

pub struct WalletTradingGuard<'a> {
    lock: &'a AtomicBool,
}

impl<'a> Drop for WalletTradingGuard<'a> {
    fn drop(&mut self) {
        self.lock.store(false, std::sync::atomic::Ordering::Relaxed);
    }
}

pub async fn acquire_trading_lock<'a>(lock: &'a AtomicBool) -> WalletTradingGuard<'a> {
    let mut count = 0;
    while lock.load(std::sync::atomic::Ordering::Relaxed) {
        sleep(Duration::from_millis(100)).await;
        count += 1;
        if count % 10 == 0 {
            info!("Still waiting for trading lock");
        }
    }
    lock.store(true, std::sync::atomic::Ordering::Relaxed);
    WalletTradingGuard { lock }
}

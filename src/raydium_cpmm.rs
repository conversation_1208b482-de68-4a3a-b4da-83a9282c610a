use anyhow::{anyhow, Result};
use borsh::{BorshDeserialize, BorshSerialize};
use log::info;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    compute_budget::ComputeBudgetInstruction,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
    system_instruction,
    transaction::Transaction,
};
use spl_associated_token_account::get_associated_token_address;
use spl_associated_token_account::instruction::create_associated_token_account;
use std::str::FromStr;

use crate::{config::TradingPool, PriceProvider};
use crate::constants::WSOL_MINT;
use async_trait::async_trait;

#[derive(BorshDeserialize, BorshSerialize, Debug)]
#[repr(C)]
pub struct RaydiumCPMMPoolState {
    pub nonce: u64,
    pub amm_config: <PERSON><PERSON>,
    pub pool_creator: Pub<PERSON>,
    pub token0_vault: Pubkey,
    pub token1_vault: Pubkey,
    pub lp_mint: Pubkey,
    pub token0_mint: Pubkey,
    pub token1_mint: Pubkey,
    pub token0_program: Pubkey,
    pub token1_program: Pubkey,
    pub observation_key: Pubkey,
    pub auth_bump: u8,
    pub status: u8,
    pub lp_mint_decimals: u8,
    pub mint0_decimals: u8,
    pub mint1_decimals: u8,
    pub lp_supply: u64,
    pub protocol_fees_token0: u64,
    pub protocol_fees_token1: u64,
    pub fund_fees_token0: u64,
    pub fund_fees_token1: u64,
    pub open_time: u64,
    pub padding: [u64; 32],
}

impl RaydiumCPMMPoolState {
    fn from_bytes(data: &[u8]) -> Result<Self> {
        Self::try_from_slice(data)
            .map_err(|e| anyhow!("Failed to deserialize RaydiumPoolState: {}", e))
    }
}

pub struct RaydiumCPMMPool;

#[async_trait]
impl PriceProvider for RaydiumCPMMPool {
    async fn get_price(&self, client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64> {
        get_raydium_cpmm_price(client, pool_pubkey).await
    }
}

#[async_trait]
impl TradingPool for RaydiumCPMMPool {
    async fn build_swap_transaction(
        &self,
        client: &RpcClient,
        wallet: &Keypair,
        pool_pubkey: &Pubkey,
        input_amount: u64,
        output_amount: u64,
        token_mint: &Pubkey,
        is_buying: bool,
        jito_tip_account: &Pubkey,
        jito_tip_amount: u64,
        priority_fee_amount: u64,
        self_send_amount: u64,
    ) -> Result<Transaction> {
        build_cpmm_swap_transaction(
            client,
            wallet,
            pool_pubkey,
            input_amount,
            output_amount,
            token_mint,
            is_buying,
            jito_tip_account,
            jito_tip_amount,
            priority_fee_amount,
            self_send_amount,
        )
        .await
    }
}

/// Calculates the price of a token in SOL units from a Raydium liquidity pool
///
/// # Arguments
///
/// * `rpc_client` - A reference to the Solana RPC client
/// * `pool_pubkey` - The public key of the Raydium liquidity pool
///
/// # Returns
///
/// * `Result<f64>` - The price of the token in SOL units, or an error if the calculation fails
pub async fn get_raydium_cpmm_price(rpc_client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64> {
    let account = rpc_client.get_account(&pool_pubkey)?;
    let data = account.data;

    info!("Raydium CPMM Pool State length: {:#?}", data.len());
    info!(
        "State struct bytes size: {:#?}",
        std::mem::size_of::<RaydiumCPMMPoolState>()
    );

    // Parse pool state
    let pool_state = RaydiumCPMMPoolState::from_bytes(&data)?;

    // Get token account balances
    let token0_balance = rpc_client
        .get_token_account_balance(&pool_state.token0_vault)?
        .amount
        .parse::<u64>()
        .map_err(|e| anyhow!("Failed to parse base balance: {}", e))?;

    let token1_balance = rpc_client
        .get_token_account_balance(&pool_state.token1_vault)?
        .amount
        .parse::<u64>()
        .map_err(|e| anyhow!("Failed to parse quote balance: {}", e))?;

    if token0_balance == 0 || token1_balance == 0 {
        return Err(anyhow!(
            "No liquidity in pool, base: {}, quote: {}",
            token0_balance,
            token1_balance
        ));
    }

    // Calculate price with decimal adjustment
    // price = (base_balance / base_decimals) / (quote_balance / quote_decimals)
    let token0_factor = 10u64.pow(pool_state.mint0_decimals as u32);
    let token1_factor = 10u64.pow(pool_state.mint1_decimals as u32);
    let price = (token0_balance as f64 / token0_factor as f64)
        / (token1_balance as f64 / token1_factor as f64);

    // Apply swap fee
    // let fee_numerator = pool_state.swap_fee_numerator as f64;
    // let fee_denominator = pool_state.swap_fee_denominator as f64;
    let fee_multiplier = 1.0;

    // Calculate final price
    if pool_state.token1_mint == Pubkey::from_str(WSOL_MINT)? {
        Ok((1.0 / price) * fee_multiplier)
    } else {
        Ok(price * fee_multiplier)
    }
}

/// Builds a transaction to swap tokens in a Raydium liquidity pool
///
/// # Arguments
///
/// * `rpc_client` - A reference to the Solana RPC client
/// * `wallet` - The wallet to use for the transaction
/// * `pool_pubkey` - The public key of the Raydium liquidity pool
/// * `wsol_amount` - The amount of WSOL to swap
/// * `min_token_amount` - The minimum amount of tokens to receive
///
/// # Returns
///
/// * `Result<Transaction>` - The transaction to swap tokens, or an error if the transaction cannot be built
pub async fn build_cpmm_swap_transaction(
    rpc_client: &RpcClient,
    wallet: &Keypair,
    pool_pubkey: &Pubkey,
    input_amount: u64,
    output_amount: u64,
    token_mint: &Pubkey,
    is_buying: bool,
    jito_tip_account: &Pubkey,
    jito_tip_amount: u64,
    priority_fee_amount: u64,
    self_send_amount: u64,
) -> Result<Transaction> {
    if !is_buying {
        return Err(anyhow!("CPMM only supports buying"));
    }

    let vault_authority = Pubkey::from_str("GpMZbSM2GgvTKHJirzeGfMFoaZ8UR2X7F4v8vHTvxFbL")?;
    let amm_config = Pubkey::from_str("D4FPEruKEHrG5TenZ2mpDGEfu1iUvTiqBxvpU8HLBvC2")?;
    let observation_state = Pubkey::from_str("HSYeHzVCyb2GmVqhug9jP2BgZj5jswgrpU5P8GtfA5M3")?;

    // Get pool state to get token accounts
    let pool_account = rpc_client.get_account(pool_pubkey)?;
    let pool_state = RaydiumCPMMPoolState::from_bytes(&pool_account.data)?;

    // Create a temporary WSOL account
    let temp_wsol_account = Keypair::new();
    let create_wsol_account_ix = system_instruction::create_account(
        &wallet.pubkey(),
        &temp_wsol_account.pubkey(),
        rpc_client.get_minimum_balance_for_rent_exemption(165)? + input_amount,
        165,
        &spl_token::id(),
    );

    // Initialize WSOL account
    let init_wsol_account_ix = spl_token::instruction::initialize_account(
        &spl_token::id(),
        &temp_wsol_account.pubkey(),
        &Pubkey::from_str(WSOL_MINT)?,
        &wallet.pubkey(),
    )?;

    // Get or create associated token account for destination token
    let token_account = get_associated_token_address(&wallet.pubkey(), token_mint);
    let create_token_account_ix = create_associated_token_account(
        &wallet.pubkey(),
        &wallet.pubkey(),
        token_mint,
        &spl_token::id(),
    );

    // Build swap instruction data
    let mut swap_data = vec![2u8]; // SwapBaseInput instruction discriminator
    swap_data.extend_from_slice(&input_amount.to_le_bytes()); // Amount in
    swap_data.extend_from_slice(&output_amount.to_le_bytes()); // Minimum amount out

    let swap_ix = Instruction {
        program_id: Pubkey::from_str("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C")?,
        accounts: vec![
            AccountMeta::new(wallet.pubkey(), true), // Payer/User wallet
            AccountMeta::new_readonly(vault_authority, false), // Vault Authority
            AccountMeta::new_readonly(amm_config, false), // AMM Config
            AccountMeta::new(*pool_pubkey, false),   // Pool State
            AccountMeta::new(temp_wsol_account.pubkey(), false), // Input Token Account
            AccountMeta::new(token_account, false),  // Output Token Account
            AccountMeta::new(pool_state.token0_vault, false), // Input Vault
            AccountMeta::new(pool_state.token1_vault, false), // Output Vault
            AccountMeta::new_readonly(spl_token::id(), false), // Input Token Program
            AccountMeta::new_readonly(spl_token::id(), false), // Output Token Program
            AccountMeta::new_readonly(Pubkey::from_str(WSOL_MINT)?, false), // Input Token Mint
            AccountMeta::new_readonly(*token_mint, false), // Output Token Mint
            AccountMeta::new(observation_state, false), // Observation State
        ],
        data: swap_data,
    };

    // Close temporary WSOL account
    let close_wsol_account_ix = spl_token::instruction::close_account(
        &spl_token::id(),
        &temp_wsol_account.pubkey(),
        &wallet.pubkey(),
        &wallet.pubkey(),
        &[],
    )?;

    // Set compute unit limit and price
    let set_compute_unit_limit_ix = ComputeBudgetInstruction::set_compute_unit_limit(300_000);
    let set_compute_unit_price_ix =
        ComputeBudgetInstruction::set_compute_unit_price(priority_fee_amount);

    // Build transaction
    let mut instructions = vec![
        set_compute_unit_limit_ix,
        set_compute_unit_price_ix,
        create_wsol_account_ix,
        init_wsol_account_ix,
    ];

    // Only add create token account instruction if it doesn't exist
    if rpc_client.get_account(&token_account).is_err() {
        instructions.push(create_token_account_ix);
    }

    instructions.extend_from_slice(&[swap_ix, close_wsol_account_ix]);

    if self_send_amount > 0 {
        if is_buying {
            let self_transfer_ix =
                system_instruction::transfer(&wallet.pubkey(), &wallet.pubkey(), self_send_amount);
            instructions.push(self_transfer_ix);
        } else {
            let self_transfer_ix = spl_token::instruction::transfer(
                &spl_token::id(),
                &token_account,
                &token_account,
                &wallet.pubkey(),
                &[&wallet.pubkey()],
                self_send_amount,
            )?;
            instructions.push(self_transfer_ix);
        }
    }

    // add jito tip
    let jito_tip_ix =
        system_instruction::transfer(&wallet.pubkey(), jito_tip_account, jito_tip_amount);
    instructions.push(jito_tip_ix);

    let recent_blockhash = rpc_client.get_latest_blockhash()?;
    let transaction = Transaction::new_signed_with_payer(
        &instructions,
        Some(&wallet.pubkey()),
        &[wallet, &temp_wsol_account],
        recent_blockhash,
    );

    Ok(transaction)
}

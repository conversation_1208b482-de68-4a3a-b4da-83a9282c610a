use alloy::{
    primitives::{Address, U256},
    providers::{Provider, ProviderBuilder},
    rpc::types::Filter,
};
use anyhow::Result;
use log::{debug, error, info};
use reqwest::Url;
use std::{
    str::FromStr,
    time::{Duration, SystemTime, UNIX_EPOCH},
};

use crate::{
    abis::FourMeme::TokenPurchase,
    calculate_price_change, get_evm_token_balance,
    multi_chain_wallet_store::MultiChainWalletStore,
    price::get_bnb_price,
    token_monitor_utils::{
        check_token_status, get_token_info, get_token_price_per_bnb, record_bnb_sold_token_to_csv,
        save_bnb_token_monitors, BnbTokenMonitorState, BnbTrackedWalletInfo, BNB_TOKEN_MONITORS,
        FOMO_MESSAGE_IDS, FOUR_MEME_CONTRACT, SENT_SIGNALS,
    },
    TelegramBot, TELEGRAM_BOT,
};

/// Monitor BNB trades by watching for TokenSale events
pub async fn monitor_bnb_trades(rpc_url: String, telegram_bot: &TelegramBot) -> Result<()> {
    let url = Url::parse(&rpc_url)?;
    let provider = ProviderBuilder::new().on_http(url);
    let contract_address = Address::from_str(FOUR_MEME_CONTRACT)?;

    // Get current BNB price
    let mut bnb_price = match get_bnb_price().await {
        Ok(price) => price,
        Err(e) => {
            error!("Failed to get initial BNB price: {}", e);
            650.0
        }
    };

    // Initialize variables
    let mut tracked_addresses = Vec::new();
    let mut last_processed_block = provider.get_block_number().await?;

    // Main monitoring loop
    let mut interval = tokio::time::interval(Duration::from_secs(3));
    for cnt in 0.. {
        interval.tick().await;

        // Every 3 minutes (or 36 ticks), update price and tracked wallets
        if cnt % 36 == 0 {
            // Update BNB price
            if let Ok(new_price) = get_bnb_price().await {
                bnb_price = new_price;
            }

            // Update tracked addresses
            match update_tracked_addresses().await {
                Ok(addresses) => {
                    tracked_addresses = addresses;
                }
                Err(e) => {
                    error!("Failed to update tracked addresses: {}", e);
                }
            }
        }

        // Query for new token sale logs
        match query_token_sale_logs(&provider, contract_address, last_processed_block + 1).await {
            Ok((logs, latest_block)) => {
                // Process each log
                for log in logs {
                    if let Err(e) = process_token_sale_log(
                        &rpc_url,
                        log,
                        &tracked_addresses,
                        bnb_price,
                        telegram_bot,
                    )
                    .await
                    {
                        error!("Error processing token sale log: {}", e);
                    }
                }

                // Update last processed block
                if latest_block > last_processed_block {
                    last_processed_block = latest_block;
                }
            }
            Err(e) => {
                error!("Error querying token sale logs: {}", e);
                tokio::time::sleep(Duration::from_secs(2)).await;
            }
        }
    }
    Ok(())
}

/// Update the list of tracked addresses
async fn update_tracked_addresses() -> Result<Vec<String>> {
    let mut store = MultiChainWalletStore::load("tracked_wallets_multi.json")?;
    let bsc_store = store.get_chain_store("bnb");
    Ok(bsc_store.get_wallets().to_vec())
}

/// Query for token sale logs since a given block
async fn query_token_sale_logs(
    provider: &impl alloy::providers::Provider,
    contract_address: Address,
    from_block: u64,
) -> Result<(Vec<alloy::rpc::types::Log>, u64)> {
    // Get the current block number
    let latest_block = provider.get_block_number().await?;

    // If we're up to date, return empty results
    if latest_block < from_block {
        return Ok((Vec::new(), latest_block));
    }

    // Create a filter for TokenPurchase
    let token_sale_event_signature =
        "TokenPurchase(address,address,uint256,uint256,uint256,uint256,uint256,uint256)";
    let filter = Filter::new()
        .address(contract_address)
        .event(token_sale_event_signature)
        .from_block(from_block)
        .to_block(latest_block);

    // Query logs
    let logs = provider.get_logs(&filter).await?;

    Ok((logs, latest_block))
}

/// Process a token sale log
async fn process_token_sale_log(
    rpc_url: &str,
    log: alloy::rpc::types::Log,
    tracked_addresses: &[String],
    bnb_price: f64,
    telegram_bot: &TelegramBot,
) -> Result<()> {
    let token_sale_event = log.log_decode::<TokenPurchase>()?;

    // Extract token_address and buyer_address from topics
    let token_address = token_sale_event.data().token_address;
    let buyer_address = token_sale_event.data().buyer_address;

    // Convert to strings for easier handling
    let token_address_str = format!("{:?}", token_address);
    let buyer_address_str = format!("{:?}", buyer_address);

    // Check if the buyer is in our tracked addresses
    if !tracked_addresses
        .iter()
        .any(|addr| addr.eq_ignore_ascii_case(&buyer_address_str))
    {
        return Ok(());
    }

    let token_amount = token_sale_event.data().token_amount;
    let bnb_amount = token_sale_event.data().bnb_amount;

    // Check if token_amount_scaled is greater than 2^55
    if token_amount / U256::from(1e18 as u64) > U256::from(1u64 << 55) {
        return Err(anyhow::anyhow!(
            "Token amount exceeds the maximum allowed value"
        ));
    }

    // Scale the amounts for processing
    let bnb_amount_scaled = (bnb_amount / U256::from(1e9 as u64)).to::<u64>();
    let token_amount_scaled = (token_amount / U256::from(1e18 as u64)).to::<u64>();

    // Skip if BNB amount is too small
    if bnb_amount_scaled < 400_000_000 {
        // buy less than 0.4 BNB, skip
        return Ok(());
    }

    let price_per_bnb = get_token_price_per_bnb(token_amount, bnb_amount);
    let price = bnb_price * price_per_bnb;
    // Process the token purchase
    process_token_purchase(
        rpc_url,
        &token_address_str,
        &buyer_address_str,
        token_amount_scaled,
        bnb_amount_scaled,
        price,
        telegram_bot,
    )
    .await
}

/// Process a token purchase event
async fn process_token_purchase(
    rpc_url: &str,
    token_address: &str,
    buyer_address: &str,
    token_amount_scaled: u64,
    bnb_amount_scaled: u64,
    token_price_usd: f64,
    telegram_bot: &TelegramBot,
) -> Result<()> {
    info!(
        "process_token_purchase: {}, {}, {}, {}, {}",
        token_address, buyer_address, token_amount_scaled, bnb_amount_scaled, token_price_usd
    );

    let signal_key = format!("{}:signal", token_address);
    if SENT_SIGNALS.contains_key(&signal_key) {
        debug!("Already sent signal for token {}", token_address);
        return Ok(());
    }

    let url = Url::parse(&rpc_url)?;
    let provider = ProviderBuilder::new().on_http(url);
    let (symbol, _) = match get_token_info(provider, token_address).await {
        Ok(info) => info,
        Err(e) => {
            error!("Failed to get token info for {}: {}", token_address, e);
            return Ok(());
        }
    };
    info!("Got token info for {}: {}", token_address, symbol);

    let target_wallet = buyer_address.to_string();
    let token_address = token_address.to_string();

    // Get wallet win rate outside of any locks
    let mut store = MultiChainWalletStore::load("tracked_wallets_multi.json").unwrap();
    let bsc_store = store.get_chain_store("bnb");
    let win_rate = bsc_store.get_wallet_win_rate(target_wallet.as_str());

    // Check if token is monitored and if wallet needs to be added
    let is_token_monitored = {
        let token_monitors = BNB_TOKEN_MONITORS
            .lock()
            .map_err(|e| anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e))?;

        if let Some(monitor_state) = token_monitors.get(&token_address) {
            // Token is already being monitored
            let is_wallet_already_tracked = monitor_state
                .tracked_wallets
                .iter()
                .any(|w| w.wallet == target_wallet);

            // If wallet isn't tracked, add it to the monitored wallets
            if !is_wallet_already_tracked {
                let mut token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
                })?;
                if let Some(monitor_state) = token_monitors.get_mut(&token_address) {
                    monitor_state.tracked_wallets.push(BnbTrackedWalletInfo {
                        wallet: target_wallet.clone(),
                        initial_amount_scaled: token_amount_scaled,
                        current_amount_scaled: token_amount_scaled,
                        max_amount_scaled: token_amount_scaled,
                        win_rate,
                    });
                    info!(
                        "Added wallet {} to existing monitoring for token {}",
                        target_wallet, token_address
                    );
                }
            }
            true
        } else {
            false
        }
    };

    if !is_token_monitored {
        // Send FOMO signal to Telegram
        let msg = match telegram_bot
            .send_bnb_fomo_signal(&token_address, &symbol, token_price_usd)
            .await
        {
            Err(e) => {
                error!("Failed to send FOMO signal: {:?}", e);
                return Ok(());
            }
            Ok(msg) => {
                if let Ok(mut message_ids) = FOMO_MESSAGE_IDS.lock() {
                    message_ids.insert(token_address.clone(), msg.id.0);
                }
                msg
            }
        };
        let telegram_link = telegram_bot.get_message_link(&msg);
        let emit_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        // Add new token to monitors with initial wallet info
        {
            let mut token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
            })?;
            token_monitors.insert(
                token_address.clone(),
                BnbTokenMonitorState {
                    token_mint: token_address.clone(),
                    symbol: symbol.clone(),
                    tracked_wallets: vec![BnbTrackedWalletInfo {
                        wallet: target_wallet.clone(),
                        initial_amount_scaled: token_amount_scaled,
                        current_amount_scaled: token_amount_scaled,
                        max_amount_scaled: token_amount_scaled,
                        win_rate,
                    }],
                    entry_price: token_price_usd,
                    entry_time: SystemTime::now(),
                    emit_time,
                    telegram_link,
                    highest_price: Some(token_price_usd),
                },
            );
            info!(
                "Added new token {} with wallet {} to monitoring",
                token_address, target_wallet
            );
        }

        // Mark signal as sent
        SENT_SIGNALS.insert(signal_key, 1);
    }

    info!(
        "Token {} is monitored: {}",
        token_address, is_token_monitored
    );
    Ok(())
}

/// Regularly monitor all token balances and send updates to API
pub async fn monitor_bnb_token_balances(rpc_url: String) -> Result<()> {
    let url = reqwest::Url::parse(&rpc_url)?;
    let provider = ProviderBuilder::new().on_http(url);
    let mut interval = tokio::time::interval(Duration::from_secs(15));

    let mut bnb_price = match get_bnb_price().await {
        Ok(price) => price,
        Err(e) => {
            error!("Failed to get BNB price: {}", e);
            0.0
        }
    };

    for cnt in 0.. {
        interval.tick().await;

        if cnt % 20 == 0 {
            // Check BNB_TOKEN_MONITORS size without holding the lock for long
            let _token_count = {
                let token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
                })?;
                token_monitors.len()
            };
            if let Ok(new_bnb_price) = get_bnb_price().await {
                bnb_price = new_bnb_price;
            }
        }

        // Skip if no tokens are being monitored
        let is_empty = {
            let token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
            })?;
            token_monitors.is_empty()
        };

        if is_empty {
            continue;
        }

        let mut tokens_to_remove = Vec::new();
        let tokens_to_process: Vec<String>;

        // First, gather all tokens that need to be processed
        {
            let token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
            })?;
            tokens_to_process = token_monitors.keys().cloned().collect();
        }

        // Process each token individually
        for token_address in tokens_to_process {
            // Get a clone of the token state to work with outside the lock
            let token_state_clone = {
                let token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
                })?;
                match token_monitors.get(&token_address) {
                    Some(state) => state.clone(),
                    None => continue, // Token was removed between iterations
                }
            };

            // Process the token state (largely unchanged logic, but no lock held)
            let mut updated_wallet_infos = Vec::new();
            let mut total_ratio = 0.0;
            let mut total_win_rate = 0.0;
            let mut wallets_count = 0;
            let mut wallets_still_holding = 0;

            let mut related_wallets = Vec::new();
            for wallet_info in &token_state_clone.tracked_wallets {
                related_wallets.push(wallet_info.wallet.clone());

                let mut updated_info = wallet_info.clone();
                match get_evm_token_balance(provider.clone(), &token_address, &wallet_info.wallet)
                    .await
                {
                    Ok(amount) => {
                        let amount_scaled = (amount / U256::from(1e18 as u64)).to::<u64>();
                        updated_info.current_amount_scaled = amount_scaled;

                        // Update max amount if current is greater
                        if amount_scaled > updated_info.max_amount_scaled {
                            updated_info.max_amount_scaled = amount_scaled;
                        }

                        // Calculate holding ratio based on max amount ever held
                        let ratio =
                            (amount_scaled as f64) / (updated_info.max_amount_scaled as f64);
                        total_ratio += ratio;
                        total_win_rate += updated_info.win_rate;
                        wallets_count += 1;

                        // Count wallets still holding tokens
                        if amount.gt(&U256::from(0)) {
                            wallets_still_holding += 1;
                        }

                        updated_wallet_infos.push(updated_info);
                    }
                    Err(e) => {
                        error!(
                            "Failed to get token balance for wallet {}: {:?}",
                            wallet_info.wallet, e
                        );
                        // Add the original info if there was an error
                        updated_wallet_infos.push(updated_info);
                    }
                }
            }

            // Skip tokens with no tracked wallets
            if wallets_count == 0 {
                continue;
            }

            // Update the token state with new wallet info
            {
                let mut token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
                })?;
                if let Some(state) = token_monitors.get_mut(&token_address) {
                    state.tracked_wallets = updated_wallet_infos.clone();
                } else {
                    continue; // Token was removed between iterations
                }
            }

            // Calculate averages
            let avg_ratio = total_ratio / wallets_count as f64;
            let avg_win_rate = total_win_rate / wallets_count as f64;

            let token_status = match check_token_status(provider.clone(), &token_address).await {
                Ok(status) => status,
                Err(e) => {
                    error!("Error getting token status: {:?}", e);
                    continue;
                }
            };
            let current_price = match token_status.price_per_bnb {
                Some(p) => p * bnb_price,
                None => {
                    error!("Failed to get token price for {}", token_address);
                    continue;
                }
            };

            // Calculate price change
            let price_change = calculate_price_change(current_price, token_state_clone.entry_price);

            // Update the token state with new price
            let highest_price = {
                let mut token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
                })?;
                if let Some(state) = token_monitors.get_mut(&token_address) {
                    if let Some(highest_price) = state.highest_price {
                        state.highest_price = Some(f64::max(
                            token_state_clone.entry_price,
                            f64::max(highest_price, current_price),
                        ));
                    } else {
                        state.highest_price =
                            Some(f64::max(token_state_clone.entry_price, current_price));
                    }
                    state.highest_price.unwrap()
                } else {
                    continue; // Token was removed between iterations
                }
            };

            info!(
                "Current status of token {}: price {}, highest price {}, entry price {}, price change {}, avg_ratio {}, avg_win_rate {}, wallets_count {}, wallets_still_holding {}",
                token_address, current_price, highest_price, token_state_clone.entry_price, price_change, avg_ratio, avg_win_rate, wallets_count, wallets_still_holding
            );

            // Check if average ratio is below threshold (20%)
            if avg_ratio <= 0.25 {
                info!(
                    "Smart wallets sold BNB token {}, average ratio: {:.2}%",
                    token_address,
                    avg_ratio * 100.0
                );

                // Calculate minutes monitored
                let now = SystemTime::now();
                let minutes_monitored = now
                    .duration_since(token_state_clone.entry_time)
                    .unwrap_or(Duration::from_secs(0))
                    .as_secs()
                    / 60;

                // Record to CSV file
                if let Err(e) = record_bnb_sold_token_to_csv(
                    &token_address,
                    &token_state_clone.symbol,
                    price_change,
                    minutes_monitored,
                    related_wallets,
                ) {
                    error!("Failed to record sold token to CSV: {:?}", e);
                }

                // Send sold signal to Telegram
                let bot = TELEGRAM_BOT.get().unwrap();
                let reply_to_id = FOMO_MESSAGE_IDS
                    .lock()
                    .ok()
                    .and_then(|ids| ids.get(&token_address).copied());

                info!(
                    "Sending sold signal for token {} to Telegram",
                    token_address
                );

                // Use a timeout to ensure we don't get stuck on Telegram API calls
                let telegram_result = match tokio::time::timeout(
                    std::time::Duration::from_secs(15), // Add a second layer of protection
                    bot.send_bnb_sold_signal(
                        &token_address,
                        &token_state_clone.symbol,
                        current_price,
                        reply_to_id,
                    ),
                )
                .await
                {
                    Ok(result) => result,
                    Err(_) => {
                        error!(
                            "Timed out waiting for Telegram API response for token {}",
                            token_address
                        );
                        Err(anyhow::anyhow!("Telegram API timeout at outer layer"))
                    }
                };

                match telegram_result {
                    Ok(_msg) => {
                        // Send sold signal to API
                        // info!("Sending sold signal for token {} to API", token_address);
                        // let telegram_link = bot.get_message_link(&msg);
                        // let req = format_sell_signal_request(&token_address, telegram_link);
                        // if let Err(e) = API_CLIENT.send_sell_signal(req).await {
                        //     error!("Failed to send sell signal to API: {:?}", e);
                        // }
                    }
                    Err(e) => {
                        error!("Failed to send sold signal to Telegram: {:?} - continuing with token removal", e);
                        continue;
                    }
                }

                // Mark this token for removal
                tokens_to_remove.push(token_address.clone());
                continue;
            }
        }

        // Remove tokens marked for removal
        for token_address in tokens_to_remove {
            // Remove token from monitoring
            {
                let mut token_monitors = BNB_TOKEN_MONITORS.lock().map_err(|e| {
                    anyhow::anyhow!("Failed to acquire BNB_TOKEN_MONITORS lock: {:?}", e)
                })?;
                token_monitors.remove(&token_address);
            }

            // Save immediately after removal
            if let Err(e) = save_bnb_token_monitors() {
                error!(
                    "Failed to save BNB token monitors after removing {}: {:?}",
                    token_address, e
                );
            }
        }
    }
    Ok(())
}

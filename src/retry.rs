use anyhow::Result;
use std::time::Duration;

pub fn retry_with_backoff<T, F>(max_retries: u32, initial_delay: Duration, mut f: F) -> Result<T>
where
    F: FnMut() -> Result<T>,
{
    let mut retries = 0;
    let mut delay = initial_delay;

    loop {
        match f() {
            Ok(result) => return Ok(result),
            Err(_) if retries < max_retries => {
                retries += 1;
                std::thread::sleep(delay);
                delay *= 2;
            }
            Err(e) => return Err(e),
        }
    }
}

pub async fn retry_with_backoff_async<T, F>(
    max_retries: u32,
    initial_delay: Duration,
    mut f: F,
) -> Result<T>
where
    F: FnMut() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T>>>>,
{
    let mut retries = 0;
    let mut delay = initial_delay;

    loop {
        match f().await {
            Ok(result) => return Ok(result),
            Err(_) if retries < max_retries => {
                retries += 1;
                tokio::time::sleep(delay).await;
                delay *= 2;
            }
            Err(e) => return Err(e),
        }
    }
}

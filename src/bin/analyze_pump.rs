use anyhow::Result;
use dotenv::dotenv;
use solana_bot::{
    analyze_pump_transactions,
    buy_records::{ensure_cache_dir, save_buy_records_to_cache},
    filter_pump_tokens, get_token_mint_activity, get_trending_tokens, has_significant_buy_activity,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use std::time::Duration;
use std::{collections::HashMap, env};
use tokio::time::sleep;

async fn analyze_token(client: &RpcClient, token_address: &str) -> Result<()> {
    println!("Analyzing token: {}", token_address);

    // Get token mint activity
    let mint_activity = match get_token_mint_activity(token_address).await {
        Ok(Some(activity)) => activity,
        Ok(None) => {
            println!("No mint activity found for token {}", token_address);
            return Ok(());
        }
        Err(e) => {
            println!(
                "Error getting mint activity for token {}: {}",
                token_address, e
            );
            return Ok(());
        }
    };
    let bonding_curve_address = mint_activity.to_address.clone();
    let creation_time = mint_activity.block_time;

    // Analyze transactions
    let buy_records = analyze_pump_transactions(client, &bonding_curve_address).await?;

    // Save buy records to cache for future use
    if let Err(e) = save_buy_records_to_cache(
        token_address,
        &bonding_curve_address,
        creation_time,
        &buy_records,
    ) {
        println!("Error saving buy records to cache: {}", e);
    }

    if buy_records.is_empty() {
        // println!("No buy records found for token {}", token_address);
        return Ok(());
    }

    // Check for significant buy activity
    let has_significant_activity = has_significant_buy_activity(&buy_records, 1, 30.0);

    if has_significant_activity {
        println!(
            "✅ FOUND SIGNIFICANT BUY ACTIVITY: {} SOL in 1 minute window",
            buy_records.iter().map(|r| r.sol_amount).sum::<f64>()
        );

        // Group buys by source address
        let mut buys_by_address: HashMap<String, f64> = HashMap::new();
        for record in &buy_records {
            *buys_by_address
                .entry(record.source_address.clone())
                .or_insert(0.0) += record.sol_amount;
        }

        // Output addresses with significant buys
        // println!("\nSignificant buyers (>0.5 SOL):");
        let mut significant_buyers: Vec<_> = buys_by_address
            .iter()
            .filter(|(_, amount)| **amount > 0.5)
            .collect();

        // Sort by amount in descending order
        significant_buyers.sort_by(|a, b| b.1.partial_cmp(a.1).unwrap());

        for (address, _) in significant_buyers {
            println!("{}", address);
        }
    } else {
        println!("❌ No significant buy activity found");
    }

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::init();

    // Get RPC URL from environment or use default
    let rpc_url =
        env::var("RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
    let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

    // Ensure cache directory exists
    ensure_cache_dir()?;

    let args: Vec<String> = env::args().collect();

    if args.len() > 1 {
        // Analyze specific tokens provided as arguments
        for token_address in &args[1..] {
            analyze_token(&client, token_address).await?;
            // Add a small delay between token analyses
            sleep(Duration::from_secs(1)).await;
        }
    } else {
        // Fetch trending tokens and filter for pump tokens
        println!("Fetching trending tokens...");
        let mut tokens = get_trending_tokens(true).await?;
        let tokens_6h = get_trending_tokens(false).await?;

        // Combine tokens from both time frames
        for token_6h in tokens_6h {
            if tokens
                .iter()
                .position(|t| t.address == token_6h.address)
                .is_none()
            {
                tokens.push(token_6h);
            }
        }

        // Filter for tokens with "pump" in the address
        let pump_tokens = filter_pump_tokens(tokens);

        println!("Found {} pump tokens to analyze", pump_tokens.len());

        // Analyze each pump token
        for token in pump_tokens {
            analyze_token(&client, &token.address).await?;
            // Add a small delay between token analyses
            sleep(Duration::from_secs(2)).await;
        }
    }

    Ok(())
}

use anyhow::Result;
use dotenv::dotenv;
use solana_bot::telegram::TelegramBot;
use std::env;

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenv().ok();
    env_logger::init();

    // Get message from command line arguments
    let args: Vec<String> = env::args().collect();
    if args.len() < 3 {
        println!("Usage: cargo run --bin send_signal \"contract_address\" \"symbol\"");
        return Ok(());
    }

    // Initialize Telegram bot
    let telegram_bot = TelegramBot::new(
        &env::var("TELEGRAM_BOT_TOKEN").expect("TELEGRAM_BOT_TOKEN must be set"),
        env::var("TELEGRAM_CHANNEL_ID")
            .expect("TELEGRAM_CHANNEL_ID must be set")
            .parse::<i64>()
            .expect("TELEGRAM_CHANNEL_ID must be a valid integer"),
    );

    // Get the message (combine all arguments after the program name)
    let contract_address = &args[1];
    let symbol = &args[2];

    // Send the message
    telegram_bot
        .send_fomo_signal(contract_address, symbol, 0.0)
        .await?;
    println!("Message sent successfully!");

    Ok(())
}

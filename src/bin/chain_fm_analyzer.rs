use anyhow::{anyhow, Result};
use serde_json::Value;
use solana_bot::wallet;
use std::collections::HashSet;
use tokio::time;

async fn fetch_channel_ids(chain: &str) -> Result<Vec<String>> {
    let trending_url = format!(
        "https://chain.fm/api/trpc/channel.list?batch=1&input=%7B%220%22%3A%7B%22json%22%3A%7B%22kind%22%3A%22{}%22%2C%22chains%22%3A%5B%22{}%22%5D%2C%22pagination%22%3A%7B%22page%22%3A1%2C%22pageSize%22%3A50%7D%2C%22showSpam%22%3Afalse%2C%22includeFields%22%3A%5B%22recentFollowers%22%2C%22owner%22%2C%22meta%22%5D%7D%7D%7D",
        "trending", chain
    );
    let newly_url = format!(
        "https://chain.fm/api/trpc/channel.list?batch=1&input=%7B%220%22%3A%7B%22json%22%3A%7B%22kind%22%3A%22{}%22%2C%22chains%22%3A%5B%22{}%22%5D%2C%22pagination%22%3A%7B%22page%22%3A1%2C%22pageSize%22%3A50%7D%2C%22showSpam%22%3Afalse%2C%22includeFields%22%3A%5B%22recentFollowers%22%2C%22owner%22%2C%22meta%22%5D%7D%7D%7D",
        "newly", chain
    );

    let trending_response = reqwest::get(&trending_url).await?.text().await?;
    let newly_response = reqwest::get(&newly_url).await?.text().await?;

    let mut channel_ids = HashSet::new();

    for response in [trending_response, newly_response].iter() {
        let json: Value = serde_json::from_str(response)?;
        let items = json[0]["result"]["data"]["json"]["items"]
            .as_array()
            .ok_or_else(|| anyhow!("Failed to parse items array"))?;

        items
            .iter()
            .filter_map(|item| item["channel"]["id"].as_str())
            .for_each(|id| {
                channel_ids.insert(id.to_string());
            });
    }

    Ok(channel_ids.into_iter().collect())
}

async fn fetch_channel_addresses(channel_id: &str) -> Result<Vec<String>> {
    let url = format!(
        "https://chain.fm/api/trpc/channel.get,notification.countNewNotifications,searchParam.query,product.getActiveChannelTipProduct,channel.getHourlyStats,product.getProduct?batch=1&input=%7B%220%22%3A%7B%22json%22%3A%22{}%22%7D%2C%221%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%222%22%3A%7B%22json%22%3A%7B%22kind%22%3A%22channel%22%2C%22object_id%22%3A%22{}%22%7D%7D%2C%223%22%3A%7B%22json%22%3A%7B%22channelId%22%3A%22{}%22%7D%7D%2C%224%22%3A%7B%22json%22%3A%7B%22channelId%22%3A%22{}%22%7D%7D%2C%225%22%3A%7B%22json%22%3A%7B%22id%22%3A%224bbbe253-aeca-4ecc-a046-c4aab8e188ba%22%7D%7D%7D",
        channel_id, channel_id, channel_id, channel_id
    );
    let response = reqwest::get(&url).await?.text().await?;

    let json: Value = serde_json::from_str(&response)?;
    let addresses = json[0]["result"]["data"]["json"]["addresses"]
        .as_array()
        .ok_or_else(|| anyhow!("Failed to parse addresses array"))?;

    let wallet_addresses: Vec<String> = addresses
        .iter()
        .filter_map(|addr| addr["address"].as_str().map(|s| s.to_string()))
        .collect();

    Ok(wallet_addresses)
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv::dotenv()?;

    println!("Fetching channel IDs from Chain FM...");
    let sol_channel_ids = fetch_channel_ids("solana").await?;
    // let bsc_channel_ids = fetch_channel_ids("bsc").await?;
    // let channel_ids = HashMap::from([("solana", sol_channel_ids), ("bsc", bsc_channel_ids)]);
    // println!("Found {} channels", channel_ids.len());

    // let mut all_addresses: HashMap<String, HashSet<String>> = HashMap::new();
    let mut addresses = HashSet::new();

    // Fetch addresses from each channel
    // for (chain, channel_ids) in channel_ids.into_iter() {
    println!("Fetching addresses for chain solana");
    for channel_id in sol_channel_ids {
        match fetch_channel_addresses(&channel_id).await {
            Ok(new_addresses) => {
                println!("Found {} addresses in channel", new_addresses.len());
                addresses.extend(new_addresses);
            }
            Err(e) => {
                println!("Error fetching addresses for channel {}: {}", channel_id, e);
                continue;
            }
        }
        // Add a small delay between requests
        time::sleep(time::Duration::from_secs(1)).await;
    }
    // }

    println!("\nTotal unique addresses found: {}", addresses.len());

    // Load wallet store to check for already tracked wallets
    // let wallet_store = WalletStore::load("tracked_wallets.json")?;
    // for chain in ["solana", "bsc"] {
    // let tracked_pub_keys: HashSet<String> = wallet_store
    //     .get_chain_wallets(chain)
    //     .iter()
    //     .cloned()
    //     .collect();
    // for wallet in tracked_pub_keys {
    //     if addresses.contains(&wallet) {
    //         addresses.remove(&wallet);
    //     }
    // }
    // }

    // Call the analyze_wallets_batch function with the HashMap
    let results = wallet::analyze_sol_wallets_batch(&addresses).await?;

    let (good_wallets_for_signal, good_wallets_for_bot) = results;

    println!(
        "\nAnalysis complete for solana. Found {} profitable wallets for signal, {} for bot:",
        good_wallets_for_signal.len(),
        good_wallets_for_bot.len()
    );

    println!("Wallets for signal:");
    for wallet in good_wallets_for_signal {
        println!("  {}", wallet);
    }

    println!("\nWallets for bot:");
    for wallet in good_wallets_for_bot {
        println!("  {}", wallet);
    }

    Ok(())
}

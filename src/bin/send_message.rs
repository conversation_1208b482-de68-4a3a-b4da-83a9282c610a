use anyhow::Result;
use dotenv::dotenv;
use solana_bot::telegram::TelegramBot;
use std::env;

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenv().ok();
    env_logger::init();

    // Get message from command line arguments
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        println!("Usage: cargo run --bin send_message \"Your message here\"");
        return Ok(());
    }

    // Initialize Telegram bot
    let telegram_bot = TelegramBot::new(
        &env::var("TELEGRAM_BOT_TOKEN").expect("TELEGRAM_BOT_TOKEN must be set"),
        env::var("TELEGRAM_CHANNEL_ID")
            .expect("TELEGRAM_CHANNEL_ID must be set")
            .parse::<i64>()
            .expect("TELEGRAM_CHANNEL_ID must be a valid integer"),
    );

    // Get the message (combine all arguments after the program name)
    let message = args[1..].join(" ");

    // Send the message
    telegram_bot.send_message(&message).await?;
    println!("Message sent successfully!");

    Ok(())
}

use anyhow::{anyhow, Result};
use chrono::Utc;
use dotenv::dotenv;
use log::{debug, error, info};
use solana_bot::{
    analyze_pump_transactions,
    buy_records::{
        current_timestamp, ensure_cache_dir, save_buy_records_to_cache, CachedBuyRecord,
    },
    get_raydium_pool_implementation, get_raydium_pool_pubkey_from_tx, get_sol_price,
    is_pump_fun_migration_tx, RaydiumPoolType, TradingPool, PUMP_FUN_MIGRATION,
};
use solana_client::rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient};
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use spl_associated_token_account::get_associated_token_address;
use std::{
    collections::{HashMap, HashSet},
    env,
    io::Write,
    str::FromStr,
    sync::{<PERSON>, <PERSON>tex},
    time::{Duration, SystemTime},
};
use tokio::time::sleep;

const MIN_TOKEN_AMOUNT: u64 = 200_000_000_000_000; // 2e14

#[derive(Debug)]
#[allow(dead_code)]
struct TokenTracker {
    token_mint: String,
    bonding_curve: String,
    raydium_pool: Option<Pubkey>,
    buy_records: Vec<CachedBuyRecord>,
    initial_balances: HashMap<String, u64>,
    last_balances: HashMap<String, u64>,
    first_total_balance: u64,
    last_total_balance: u64,
}

impl TokenTracker {
    fn new(token_mint: String, bonding_curve: String, buy_records: Vec<CachedBuyRecord>) -> Self {
        Self {
            token_mint,
            bonding_curve,
            raydium_pool: None,
            buy_records,
            initial_balances: HashMap::new(),
            last_balances: HashMap::new(),
            first_total_balance: 0,
            last_total_balance: 0,
        }
    }
}

async fn monitor_pump_fun_migration(
    client: Arc<RpcClient>,
    active_tokens: Arc<Mutex<HashMap<String, Arc<Mutex<TokenTracker>>>>>,
) -> Result<()> {
    let pump_fun_pubkey = Pubkey::from_str(PUMP_FUN_MIGRATION)?;
    let mut latest_tx: Option<Signature> = None;
    let mut seen_txs = HashSet::new();

    info!(
        "Starting to monitor pump fun migration address: {}",
        PUMP_FUN_MIGRATION
    );

    loop {
        let latest_txs = match client.get_signatures_for_address_with_config(
            &pump_fun_pubkey,
            GetConfirmedSignaturesForAddress2Config {
                before: None,
                until: latest_tx,
                limit: Some(5),
                commitment: Some(CommitmentConfig::confirmed()),
            },
        ) {
            Ok(txs) => txs,
            Err(_) => {
                // error!("Error getting latest txs: {:?}", e);
                sleep(Duration::from_secs(5)).await;
                continue;
            }
        };

        if !latest_txs.is_empty() {
            latest_tx = Some(Signature::from_str(&latest_txs[0].signature)?);
        } else {
            sleep(Duration::from_millis(1000)).await;
            continue;
        }

        for tx in latest_txs {
            let tx_sig = tx.signature.clone();
            if seen_txs.contains(&tx_sig) {
                continue;
            }
            seen_txs.insert(tx_sig.clone());

            // Use the new function to check if this is a pump fun migration tx
            match is_pump_fun_migration_tx(&client, &tx_sig, MIN_TOKEN_AMOUNT).await {
                Ok(Some((token_mint, bonding_curve))) => {
                    // Check if we're already tracking this token
                    {
                        let active_tokens_guard = active_tokens.lock().unwrap();
                        if active_tokens_guard.contains_key(&token_mint) {
                            debug!("Already tracking token: {}", token_mint);
                            continue;
                        }
                    }

                    // Analyze buy records
                    let client_clone = client.clone();
                    let active_tokens_clone = active_tokens.clone();

                    tokio::spawn(async move {
                        if let Err(e) = analyze_token(
                            client_clone,
                            token_mint.clone(),
                            bonding_curve.clone(),
                            active_tokens_clone,
                        )
                        .await
                        {
                            error!("Error analyzing token {}: {:?}", token_mint, e);
                        }
                    });
                }
                Ok(None) => {
                    // Not a pump fun migration tx
                    continue;
                }
                Err(e) => {
                    error!("Error analyzing tx {}: {:?}", tx_sig, e);
                    continue;
                }
            }
        }

        // Wait before checking for new transactions
        sleep(Duration::from_secs(15)).await;
    }
}

async fn analyze_token(
    client: Arc<RpcClient>,
    token_mint: String,
    bonding_curve: String,
    active_tokens: Arc<Mutex<HashMap<String, Arc<Mutex<TokenTracker>>>>>,
) -> Result<()> {
    info!("Analyzing token: {}", token_mint);

    // Get buy records
    let token_mint_pubkey = Pubkey::from_str(&token_mint)?;
    let buy_records = analyze_pump_transactions(&client, &bonding_curve).await?;
    let creation_time = current_timestamp();

    let cached_records =
        save_buy_records_to_cache(&token_mint, &bonding_curve, creation_time, &buy_records)?;
    if cached_records.is_empty() {
        // info!("No buy records found for token: {}", token_mint);
        return Ok(());
    }
    info!(
        "Found {} buy records for token: {}",
        cached_records.len(),
        token_mint
    );

    // Create token tracker
    let token_tracker = Arc::new(Mutex::new(TokenTracker::new(
        token_mint.clone(),
        bonding_curve.clone(),
        cached_records,
    )));

    // Add to active tokens
    {
        let mut active_tokens_guard = active_tokens.lock().unwrap();
        active_tokens_guard.insert(token_mint.clone(), token_tracker.clone());
    }

    info!(
        "Found a target token to trade! Mint address: {}",
        token_mint
    );

    // Wait for Raydium pool
    tokio::spawn(async move {
        if let Err(e) = wait_for_raydium_pool(client, token_mint_pubkey, token_tracker).await {
            error!("Error waiting for Raydium pool: {:?}", e);
        }
    });

    Ok(())
}

async fn wait_for_raydium_pool(
    client: Arc<RpcClient>,
    token_mint_pubkey: Pubkey,
    token_tracker: Arc<Mutex<TokenTracker>>,
) -> Result<()> {
    info!("Waiting for Raydium pool for token: {}", token_mint_pubkey);

    let start_time = SystemTime::now();
    let mut latest_tx: Option<Signature> = None;

    loop {
        // Check if we've been waiting too long (10 minutes)
        if SystemTime::now().duration_since(start_time)? > Duration::from_secs(600) {
            error!(
                "Not getting Raydium pool for {} after 10 minutes",
                token_mint_pubkey
            );
            return Ok(());
        }

        let latest_txs = match client.get_signatures_for_address_with_config(
            &Pubkey::from_str(PUMP_FUN_MIGRATION).unwrap(),
            GetConfirmedSignaturesForAddress2Config {
                before: None,
                until: latest_tx,
                limit: Some(3),
                commitment: Some(CommitmentConfig::confirmed()),
            },
        ) {
            Ok(txs) => txs,
            Err(_) => {
                sleep(Duration::from_secs(1)).await;
                continue;
            }
        };

        if !latest_txs.is_empty() {
            latest_tx = Some(Signature::from_str(&latest_txs[0].signature)?);
        } else {
            sleep(Duration::from_secs(1)).await;
            continue;
        }

        for tx in latest_txs {
            if let Ok(Some((pool_pubkey, _pool_wsol_account))) =
                get_raydium_pool_pubkey_from_tx(&client, tx.signature, &token_mint_pubkey)
            {
                info!(
                    "Got Raydium pool address for {}: {}",
                    token_mint_pubkey, pool_pubkey
                );

                // Update token tracker with pool pubkey
                {
                    let mut tracker = token_tracker.lock().unwrap();
                    tracker.raydium_pool = Some(pool_pubkey);
                }

                // Start monitoring token
                tokio::spawn(async move {
                    if let Err(e) = monitor_token(client, token_tracker).await {
                        error!("Error monitoring token: {:?}", e);
                    }
                });

                return Ok(());
            }
        }
    }
}

async fn monitor_token(
    client: Arc<RpcClient>,
    token_tracker: Arc<Mutex<TokenTracker>>,
) -> Result<()> {
    let token_mint;
    let raydium_pool;
    let buy_records;

    {
        let tracker = token_tracker.lock().unwrap();
        token_mint = tracker.token_mint.clone();
        raydium_pool = tracker.raydium_pool.unwrap();
        buy_records = tracker.buy_records.clone();
    }

    let token_mint_pubkey = Pubkey::from_str(&token_mint)?;

    info!(
        "Token {} has graduated! Starting to monitor price and balances",
        token_mint
    );

    // Get pool implementation
    let pool_impl: Box<dyn TradingPool> =
        get_raydium_pool_implementation(&client, &raydium_pool, &RaydiumPoolType::OpenBook)
            .unwrap();

    // Get initial token balances for all wallets in buy records
    let mut wallet_pubkeys = Vec::new();
    let mut wallet_addresses = Vec::new();

    for record in &buy_records {
        if let Ok(pubkey) = Pubkey::from_str(&record.source_address) {
            wallet_pubkeys.push(pubkey);
            wallet_addresses.push(record.source_address.clone());
        }
    }

    // Get associated token accounts for all wallets
    let mut token_accounts = Vec::new();
    for wallet_pubkey in &wallet_pubkeys {
        let token_account = get_associated_token_address(wallet_pubkey, &token_mint_pubkey);
        token_accounts.push(token_account);
    }

    // Get initial balances
    let accounts = client.get_multiple_accounts(&token_accounts)?;
    let mut initial_balances = HashMap::new();
    let mut first_total_balance: u64 = 0;

    for (i, account_opt) in accounts.iter().enumerate() {
        if i >= wallet_addresses.len() {
            break;
        }

        let wallet_address = &wallet_addresses[i];
        let balance = match account_opt {
            Some(account) => {
                if account.data.len() >= 72 {
                    let amount_bytes = &account.data[64..72];
                    let amount = u64::from_le_bytes([
                        amount_bytes[0],
                        amount_bytes[1],
                        amount_bytes[2],
                        amount_bytes[3],
                        amount_bytes[4],
                        amount_bytes[5],
                        amount_bytes[6],
                        amount_bytes[7],
                    ]);
                    amount
                } else {
                    0
                }
            }
            None => 0,
        };

        initial_balances.insert(wallet_address.clone(), balance);
        first_total_balance += balance;
    }

    // Update token tracker with initial balances
    {
        let mut tracker = token_tracker.lock().unwrap();
        tracker.initial_balances = initial_balances.clone();
        tracker.last_balances = initial_balances.clone();
        tracker.first_total_balance = first_total_balance;
        tracker.last_total_balance = first_total_balance;
    }

    let sol_price = match get_sol_price().await {
        Ok(price) => price,
        Err(e) => {
            error!("Failed to get SOL price: {}", e);
            140.0 // Default SOL price if we can't get the real one
        }
    };

    // Monitor loop
    let mut first_loop = true;
    loop {
        if first_loop {
            first_loop = false;
        } else {
            sleep(Duration::from_secs(15)).await;
        }

        // Get current price
        let price_by_sol = match pool_impl.get_price(&client, &raydium_pool).await {
            Ok(price) => price,
            Err(e) => {
                error!("Failed to get price: {:?}", e);
                continue;
            }
        };

        let price_usd = price_by_sol * sol_price;

        // Get current balances
        let accounts = match client.get_multiple_accounts(&token_accounts) {
            Ok(accounts) => accounts,
            Err(e) => {
                error!("Failed to get token accounts: {:?}", e);
                continue;
            }
        };

        let mut current_balances = HashMap::new();
        let mut current_total_balance: u64 = 0;
        let mut non_zero_wallets = 0;

        for (i, account_opt) in accounts.iter().enumerate() {
            if i >= wallet_addresses.len() {
                break;
            }

            let wallet_address = &wallet_addresses[i];
            let balance = match account_opt {
                Some(account) => {
                    if account.data.len() >= 72 {
                        let amount_bytes = &account.data[64..72];
                        let amount = u64::from_le_bytes([
                            amount_bytes[0],
                            amount_bytes[1],
                            amount_bytes[2],
                            amount_bytes[3],
                            amount_bytes[4],
                            amount_bytes[5],
                            amount_bytes[6],
                            amount_bytes[7],
                        ]);
                        amount
                    } else {
                        0
                    }
                }
                None => 0,
            };

            current_balances.insert(wallet_address.clone(), balance);
            current_total_balance += balance;

            if balance > 0 {
                non_zero_wallets += 1;
            }
        }

        // Calculate ratios
        let balance_ratio = if first_total_balance > 0 {
            current_total_balance as f64 / first_total_balance as f64
        } else {
            0.0
        };

        let non_zero_wallet_ratio = if wallet_addresses.is_empty() {
            0.0
        } else {
            non_zero_wallets as f64 / wallet_addresses.len() as f64
        };

        // Update token tracker
        {
            let mut tracker = token_tracker.lock().unwrap();
            tracker.last_balances = current_balances;
            tracker.last_total_balance = current_total_balance;
        }

        // Output status
        info!(
            "Token: {}, Price: ${:.6}, Balance ratio: {:.2}, Non-zero wallet ratio: {:.2}",
            token_mint, price_usd, balance_ratio, non_zero_wallet_ratio
        );

        // Check if we should end monitoring
        if balance_ratio <= 0.01 {
            info!(
                "Balance ratio <= 0.01, ending monitoring for token: {}",
                token_mint
            );
            break;
        }
    }

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();

    info!("Starting pump suspicious token tracking bot...");

    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    ));

    // Create cache directory if it doesn't exist
    ensure_cache_dir()?;

    // Active tokens map
    let active_tokens: Arc<Mutex<HashMap<String, Arc<Mutex<TokenTracker>>>>> =
        Arc::new(Mutex::new(HashMap::new()));

    // Start monitoring
    monitor_pump_fun_migration(client, active_tokens).await?;

    Ok(())
}

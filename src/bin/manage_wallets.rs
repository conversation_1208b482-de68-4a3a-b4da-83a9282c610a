use std::collections::HashSet;
use std::fs;

use anyhow::Result;
use clap::{Parser, Subcommand};
use log::debug;
use solana_bot::{browser_automation, get_wallet_win_rate, wallet_store::WalletStore, WalletData};

use dotenv::dotenv;

#[derive(Parser)]
#[command(author, version, about, long_about = None)]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// List all tracked wallets
    List,
    /// Add new wallets to track
    Add {
        /// Wallet addresses to add
        #[arg(required = true)]
        addresses: Vec<String>,
    },
    /// Remove wallets from tracking
    Remove {
        /// Wallet addresses to remove
        #[arg(required = true)]
        addresses: Vec<String>,
    },
    /// Clean up not profitable wallets
    Clean,
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    let cli = Cli::parse();
    let mut store = WalletStore::load("tracked_wallets.json")?;

    match cli.command {
        Commands::List => {
            println!("Currently tracked wallets:");
            for wallet in store.get_wallets() {
                println!("{}", wallet);
            }
        }
        Commands::Add { addresses } => {
            for address in addresses {
                match store.add_wallet(address.clone()) {
                    Ok(true) => println!("Successfully added wallet: {}", address),
                    Ok(false) => println!("Wallet already being tracked: {}", address),
                    Err(e) => println!("Error adding wallet: {}", e),
                }
                match get_wallet_win_rate("sol", &address).await {
                    Ok(win_rate) if win_rate > 0.0 => store.record_win_rate(&address, win_rate)?,
                    Ok(_) => {
                        println!("Wallet win rate is 0: {}", address);
                    }
                    Err(e) => println!("Error getting wallet win rate: {}", e),
                }
            }
        }
        Commands::Remove { addresses } => {
            for address in addresses {
                match store.remove_wallet(&address) {
                    Ok(true) => println!("Successfully removed wallet: {}", address),
                    Ok(false) => println!("Wallet not found: {}", address),
                    Err(e) => println!("Error removing wallet: {}", e),
                }
            }
        }
        Commands::Clean => {
            clean_wallets().await?;
        }
    }

    Ok(())
}

async fn clean_wallets() -> Result<()> {
    let mut store = WalletStore::load("tracked_wallets.json")?;
    let wallets = store.get_wallets().to_vec();

    let wallets_to_analyze = HashSet::<String>::from_iter(wallets.iter().cloned());

    let wallet_data =
        browser_automation::analyze_wallets_batch_with_browser(&wallets_to_analyze, false).await?;

    for wallet in wallets {
        let wallet_info = wallet_data.get(&wallet).ok_or_else(|| {
            anyhow::anyhow!("No data found for wallet: {} on chain solana", wallet)
        })?;
        let summary = wallet_info.get("wallet_summary");
        if let Some(summary) = summary {
            // Parse the summary data
            let summary_data = match serde_json::from_value::<WalletData>(summary.clone()) {
                Ok(data) => data,
                Err(e) => {
                    debug!("Failed to parse wallet summary data for {}: {}", wallet, e);
                    continue;
                }
            };
            if summary_data.total_trades() <= 3 {
                println!("Will remove wallet from signal: {}", wallet);
                store.remove_wallet(&wallet)?;
            }
        }
    }

    // Also check wallets from tracked_wallets_bot.json
    let home_dir =
        dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
    let bot_wallets_path = home_dir
        .join(".solana-bot")
        .join("tracked_wallets_bot.json");

    if bot_wallets_path.exists() {
        let bot_wallets_content = fs::read_to_string(&bot_wallets_path)?;
        let bot_wallets_json: serde_json::Value = serde_json::from_str(&bot_wallets_content)?;

        if let Some(all_wallets) = bot_wallets_json
            .get("all_wallets")
            .and_then(|v| v.as_array())
        {
            let bot_wallets: Vec<String> = all_wallets
                .iter()
                .filter_map(|w| w.as_str().map(String::from))
                .collect();

            if !bot_wallets.is_empty() {
                let bot_wallets_set = HashSet::<String>::from_iter(bot_wallets.iter().cloned());
                let bot_wallet_data =
                    browser_automation::analyze_wallets_batch_with_browser(&bot_wallets_set, false)
                        .await?;

                let mut wallets_to_remove = Vec::new();

                for wallet in bot_wallets {
                    let wallet_info = bot_wallet_data.get(&wallet).ok_or_else(|| {
                        anyhow::anyhow!("No data found for wallet: {} on chain solana", wallet)
                    })?;
                    let summary = wallet_info.get("wallet_summary");
                    if let Some(summary) = summary {
                        // Parse the summary data
                        let summary_data =
                            match serde_json::from_value::<WalletData>(summary.clone()) {
                                Ok(data) => data,
                                Err(e) => {
                                    debug!(
                                        "Failed to parse wallet summary data for {}: {}",
                                        wallet, e
                                    );
                                    continue;
                                }
                            };
                        if summary_data.total_trades() <= 3 {
                            wallets_to_remove.push(wallet);
                        }
                    }
                }

                if !wallets_to_remove.is_empty() {
                    let wallets_str = wallets_to_remove.join(",");
                    println!(
                        "To remove bot wallets, run: \n\npython py/bloom_bot_controller.py remove \"{}\"",
                        wallets_str
                    );
                }
            }
        }
    }

    Ok(())
}

use anyhow::{anyhow, Result};
use clap::{ArgAction, Parser};
use env_logger::Builder;
use log::{debug, info, LevelFilter};
use serde::Deserialize;
use serde_json;
use solana_bot::wallet_transfer_analyzer::{
    analyze_wallet_clusters, AssociatedWallet, WalletAnalyzer,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use std::io::Write;
use std::process::Command;
use std::{collections::HashMap, fs, io::BufRead, path::PathBuf, str::FromStr};

#[derive(Parser, Debug)]
#[command(
    author = "KryptoGO",
    version,
    about = "Analyze Solana wallet relationships through token transfers"
)]
struct Args {
    /// Wallet address to analyze
    #[arg(short, long)]
    wallet: Option<String>,

    /// Read wallet addresses from a file, one per line
    #[arg(short, long)]
    file: Option<PathBuf>,

    /// Token address to analyze
    #[arg(short, long, required = true)]
    token: String,

    /// Minimum number of transfers (in each direction) for bidirectional relationship
    #[arg(short, long, default_value = "1")]
    min_transfers: usize,

    /// Maximum number of pages to fetch (100 transfers per page)
    #[arg(short = 'p', long, default_value = "1")]
    max_pages: usize,

    /// Include wallets with one-way transfers
    #[arg(short = 'o', long, action = ArgAction::SetTrue)]
    include_one_way: bool,

    /// Minimum number of transfers for one-way relationship
    #[arg(long, default_value = "1")]
    min_one_way_transfers: usize,

    /// Include token creators that would otherwise be excluded
    #[arg(short = 'c', long, action = ArgAction::SetTrue)]
    include_token_creators: bool,

    /// Analyze clusters of wallets that transfer between each other
    #[arg(short = 'C', long, action = ArgAction::SetTrue)]
    cluster_mode: bool,

    /// Output detailed raw debug information to file
    #[arg(short = 'd', long, action = ArgAction::SetTrue)]
    debug_output: bool,

    /// Debug output file path (default: wallet_debug.txt)
    #[arg(long, default_value = "wallet_debug.txt")]
    debug_file: PathBuf,

    /// Calculate token holdings for each cluster
    #[arg(short = 'b', long, action = ArgAction::SetTrue)]
    calculate_balance: bool,

    /// Solana RPC URL
    #[arg(long, default_value = "https://api.mainnet-beta.solana.com")]
    rpc_url: String,

    /// Verbose output
    #[arg(short, long, action = ArgAction::Count)]
    verbose: u8,
}

#[derive(Debug, Deserialize)]
struct ApiResponse {
    data: ApiData,
}

#[derive(Debug, Deserialize)]
struct ApiData {
    list: Vec<HolderInfo>,
}

#[derive(Debug, Deserialize, Clone)]
struct HolderInfo {
    address: String,
    profit: Option<f64>,
}

async fn analyze_single_wallet(
    wallet: &str,
    token: &str,
    max_pages: usize,
    min_transfers: usize,
    include_one_way: bool,
    min_one_way_transfers: usize,
    include_token_creators: bool,
    debug_output: bool,
    debug_file: Option<&PathBuf>,
) -> Result<Vec<AssociatedWallet>> {
    debug!("Analyzing wallet: {}", wallet);
    // info!("Token: {}", token);

    // Create a wallet analyzer
    let analyzer = WalletAnalyzer::new(
        token.to_string(),
        max_pages,
        min_transfers,
        include_one_way,
        min_one_way_transfers,
        include_token_creators,
    )?;

    // Analyze the wallet
    let associated_wallets = analyzer.analyze_wallet(wallet).await?;

    // Print results
    println!("\nAssociated wallets for {}:", wallet);
    if associated_wallets.is_empty() {
        println!("No associated wallets found with the given criteria.");
    } else {
        println!(
            "{:<44} | {:<5} | {:<5} | {:<13} | {:<20}",
            "Address", "In", "Out", "Type", "Label"
        );
        println!(
            "{:-<44}-+-{:-<5}-+-{:-<5}-+-{:-<13}-+-{:-<20}",
            "", "", "", "", ""
        );

        for wallet in &associated_wallets {
            let relationship_type = if wallet.is_bidirectional {
                "Bidirectional"
            } else if wallet.in_transfers > wallet.out_transfers {
                "In-dominant"
            } else {
                "Out-dominant"
            };

            println!(
                "{:<44} | {:>5} | {:>5} | {:<13} | {}",
                wallet.address,
                wallet.in_transfers,
                wallet.out_transfers,
                relationship_type,
                wallet.label
            );
        }
    }

    // Write debug info if requested
    if debug_output && !associated_wallets.is_empty() {
        if let Some(debug_file_path) = debug_file {
            let mut file = fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(debug_file_path)?;

            writeln!(file, "\n=== WALLET DEBUG INFO ===\n")?;
            writeln!(file, "Source wallet: {}", wallet)?;
            writeln!(file, "Target token: {}", token)?;
            writeln!(
                file,
                "Associated wallets found: {}",
                associated_wallets.len()
            )?;
            writeln!(file, "\nDETAILED WALLET INFO:\n")?;

            for (i, wallet) in associated_wallets.iter().enumerate() {
                writeln!(file, "Wallet #{}", i + 1)?;
                writeln!(file, "  Address: {}", wallet.address)?;
                writeln!(file, "  In transfers: {}", wallet.in_transfers)?;
                writeln!(file, "  Out transfers: {}", wallet.out_transfers)?;
                writeln!(file, "  Bidirectional: {}", wallet.is_bidirectional)?;
                writeln!(file, "  Label: {}", wallet.label)?;
                writeln!(file, "  Tags: {:?}", wallet.tags)?;
                writeln!(file, "")?;
            }

            writeln!(file, "=== END DEBUG INFO ===\n")?;
            info!("Debug information written to {}", debug_file_path.display());
        }
    }

    Ok(associated_wallets)
}

// Function to calculate token balances for the given wallets
async fn calculate_token_balances(
    wallets: &[String],
    token_mint: &Pubkey,
    rpc_url: &str,
) -> Result<(u64, f64, HashMap<Pubkey, u64>)> {
    // Initialize Solana RPC client
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::confirmed());

    // Parse wallet addresses
    let wallet_pubkeys: Vec<Pubkey> = wallets
        .iter()
        .filter_map(|addr| match Pubkey::from_str(addr) {
            Ok(pubkey) => Some(pubkey),
            Err(err) => {
                eprintln!("Invalid wallet address {}: {}", addr, err);
                None
            }
        })
        .collect();

    // Get token metadata
    let token_metadata = solana_bot::token_utils::get_token_metadata(&client, token_mint).await;

    // Process wallets in chunks
    const CHUNK_SIZE: usize = 100;
    let mut all_balances = HashMap::new();

    for chunk in wallet_pubkeys.chunks(CHUNK_SIZE) {
        match solana_bot::token_utils::get_multiple_wallet_token_balances(
            &client, chunk, token_mint,
        )
        .await
        {
            Ok(balances) => {
                all_balances.extend(balances);
            }
            Err(err) => {
                eprintln!("Error getting balances: {}", err);
            }
        }
    }

    // Calculate sum of all balances
    let total_balance: u64 = all_balances.values().sum();

    // Calculate adjusted balance if we have decimals
    let adjusted_balance = if let Ok((_, decimals)) = token_metadata {
        let decimal_factor = 10u64.pow(decimals as u32);
        total_balance as f64 / decimal_factor as f64
    } else {
        total_balance as f64
    };

    Ok((total_balance, adjusted_balance, all_balances))
}

// Function to calculate token supply
async fn get_token_supply(token_mint: &Pubkey, rpc_url: &str) -> Result<(u64, f64)> {
    // Initialize Solana RPC client
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::confirmed());

    // Get token supply
    let supply = client.get_token_supply(token_mint)?;

    // Get token metadata
    let token_metadata = solana_bot::token_utils::get_token_metadata(&client, token_mint).await;

    // The token amount is returned as a UI-formatted string, we need to parse it
    let amount = match supply.amount.parse::<u64>() {
        Ok(val) => val,
        Err(_) => return Err(anyhow!("Failed to parse token supply amount")),
    };

    // Calculate adjusted supply
    let adjusted_supply = if let Ok((_, decimals)) = token_metadata {
        let decimal_factor = 10u64.pow(decimals as u32);
        amount as f64 / decimal_factor as f64
    } else {
        amount as f64
    };

    Ok((amount, adjusted_supply))
}

async fn fetch_all_traders(token: &str) -> Result<HashMap<String, Option<f64>>> {
    let url = format!(
        "https://gmgn.ai/vas/api/v1/token_traders/sol/{}?device_id=b117fa99-6839-41fc-80b3-ea223d5af82c&client_id=gmgn_web_20250508-823-3ce2338&from_app=gmgn&app_ver=20250508-823-3ce2338&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=zh-TW&fp_did=6f1f6aa8d7d90733fc55f6c3e032a5b7&os=web&limit=100&orderby=realized_profit&direction=desc",
        token
    );

    let output = Command::new("curl")
        .arg(&url)
        .arg("-H")
        .arg("accept: application/json, text/plain, */*")
        .arg("-H")
        .arg("accept-language: zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-CN;q=0.6")
        .arg("-H")
        .arg("cache-control: no-cache")
        .arg("-H")
        .arg("dnt: 1")
        .arg("-H")
        .arg("pragma: no-cache")
        .arg("-H")
        .arg("priority: u=1, i")
        .arg("-H")
        .arg(format!("referer: https://gmgn.ai/sol/token/MVNsiiWE_{}", token))
        .arg("-H")
        .arg("sec-ch-ua: \"Not.A/Brand\";v=\"99\", \"Chromium\";v=\"136\"")
        .arg("-H")
        .arg("sec-ch-ua-mobile: ?0")
        .arg("-H")
        .arg("sec-ch-ua-platform: \"macOS\"")
        .arg("-H")
        .arg("sec-fetch-dest: empty")
        .arg("-H")
        .arg("sec-fetch-mode: cors")
        .arg("-H")
        .arg("sec-fetch-site: same-origin")
        .arg("-H")
        .arg("user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        .output()?;

    let text = String::from_utf8(output.stdout)?;
    match serde_json::from_str::<ApiResponse>(&text) {
        Ok(resp) => {
            let mut profit_map = HashMap::new();
            for holder in resp.data.list {
                profit_map.insert(holder.address, holder.profit);
            }
            Ok(profit_map)
        }
        Err(e) => {
            let preview = text.chars().take(50).collect::<String>();
            eprintln!(
                "Error parsing response: {}. Response preview: {}",
                e, preview
            );
            Err(anyhow!("Failed to parse API response: {}", e))
        }
    }
}

async fn analyze_wallets_from_file(
    file_path: &PathBuf,
    token: &str,
    max_pages: usize,
    min_transfers: usize,
    include_one_way: bool,
    min_one_way_transfers: usize,
    include_token_creators: bool,
    cluster_mode: bool,
    calculate_balance: bool,
    rpc_url: &str,
    debug_output: bool,
    debug_file: Option<&PathBuf>,
) -> Result<()> {
    // Read wallets from file
    let file = fs::File::open(file_path).map_err(|e| anyhow!("Failed to open file: {}", e))?;
    let reader = std::io::BufReader::new(file);
    let wallets: Vec<String> = reader
        .lines()
        .filter_map(|line| line.ok())
        .filter(|line| !line.trim().is_empty() && !line.trim().starts_with('#'))
        .collect();

    if wallets.is_empty() {
        return Err(anyhow!("No wallet addresses found in the file"));
    }

    info!("Found {} wallet addresses in file", wallets.len());

    if cluster_mode {
        // Analyze wallet clusters
        info!("Analyzing wallet clusters...");
        let clusters = analyze_wallet_clusters(
            &wallets,
            token,
            max_pages,
            min_transfers,
            include_one_way,
            min_one_way_transfers,
            include_token_creators,
        )
        .await?;

        // Fetch all trader data once
        let profit_map = fetch_all_traders(token).await?;

        // Print results
        println!("\nFound {} wallet clusters:", clusters.len());

        // Convert token string to Pubkey
        let token_pubkey = match Pubkey::from_str(token) {
            Ok(pubkey) => pubkey,
            Err(e) => return Err(anyhow!("Invalid token address: {}", e)),
        };

        // Get token symbol if calculating balances
        let mut token_symbol = "".to_string();
        let mut token_supply = 0.0;
        if calculate_balance {
            let client =
                RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::confirmed());

            // Get token metadata
            if let Ok((symbol, _)) =
                solana_bot::token_utils::get_token_metadata(&client, &token_pubkey).await
            {
                token_symbol = symbol;
            }

            // Get token supply
            if let Ok((_, supply)) = get_token_supply(&token_pubkey, rpc_url).await {
                token_supply = supply;
            }
        }

        for (i, cluster) in clusters.iter().enumerate() {
            println!("Cluster {}: {} wallets", i + 1, cluster.len());
            for wallet in cluster {
                // Look up profit from the map
                let profit = profit_map
                    .get(wallet)
                    .and_then(|p| *p)
                    .map(|p| format!(" (Profit: ${:.2})", p))
                    .unwrap_or_default();
                println!("  {}{}", wallet, profit);
            }

            // Calculate and display token holdings if requested
            if calculate_balance {
                if let Ok((_, adjusted_balance, _)) =
                    calculate_token_balances(cluster, &token_pubkey, rpc_url).await
                {
                    let percentage = if token_supply > 0.0 {
                        (adjusted_balance / token_supply) * 100.0
                    } else {
                        0.0
                    };

                    println!(
                        "  Total cluster balance: {:.2} {} ({:.4}% of supply)",
                        adjusted_balance, token_symbol, percentage
                    );
                } else {
                    println!("  Failed to calculate token balance for this cluster");
                }
            }

            println!();
        }

        // Write debug info if requested
        if debug_output && !clusters.is_empty() {
            if let Some(debug_file_path) = debug_file {
                let mut file = fs::OpenOptions::new()
                    .create(true)
                    .write(true)
                    .truncate(true)
                    .open(debug_file_path)?;

                writeln!(file, "=== CLUSTER DEBUG INFO ===\n")?;
                writeln!(file, "Target token: {}", token)?;
                writeln!(file, "Number of input wallets: {}", wallets.len())?;
                writeln!(file, "Clusters found: {}", clusters.len())?;

                for (i, cluster) in clusters.iter().enumerate() {
                    writeln!(file, "\nCluster #{} ({} wallets):", i + 1, cluster.len())?;
                    for wallet in cluster {
                        writeln!(file, "  {}", wallet)?;
                    }

                    // Add balance information if available
                    if calculate_balance {
                        if let Ok((_, adjusted_balance, _)) =
                            calculate_token_balances(cluster, &token_pubkey, rpc_url).await
                        {
                            let percentage = if token_supply > 0.0 {
                                (adjusted_balance / token_supply) * 100.0
                            } else {
                                0.0
                            };

                            writeln!(
                                file,
                                "  Total balance: {:.2} {} ({:.4}% of supply)",
                                adjusted_balance, token_symbol, percentage
                            )?;
                        }
                    }
                }

                writeln!(file, "\n=== END CLUSTER DEBUG INFO ===\n")?;
                info!(
                    "Cluster debug information written to {}",
                    debug_file_path.display()
                );
            }
        }
    } else {
        // Initialize a mapping to store all associations for debug
        let mut all_associations: HashMap<String, Vec<AssociatedWallet>> = HashMap::new();

        // Analyze each wallet individually
        for wallet in wallets {
            let associated_wallets = analyze_single_wallet(
                &wallet,
                token,
                max_pages,
                min_transfers,
                include_one_way,
                min_one_way_transfers,
                include_token_creators,
                false, // Don't write individual debug yet
                None,
            )
            .await?;

            // Store for later collective debug output
            if debug_output {
                all_associations.insert(wallet.clone(), associated_wallets);
            }
        }

        // Write aggregate debug info if requested
        if debug_output && !all_associations.is_empty() {
            if let Some(debug_file_path) = debug_file {
                let mut file = fs::OpenOptions::new()
                    .create(true)
                    .write(true)
                    .truncate(true)
                    .open(debug_file_path)?;

                writeln!(file, "=== WALLET ASSOCIATIONS DEBUG ===\n")?;
                writeln!(file, "Target token: {}", token)?;
                writeln!(file, "Parameters:")?;
                writeln!(file, "  Min transfers (bidirectional): {}", min_transfers)?;
                writeln!(file, "  Include one-way transfers: {}", include_one_way)?;
                writeln!(file, "  Min one-way transfers: {}", min_one_way_transfers)?;
                writeln!(file, "  Include token creators: {}", include_token_creators)?;
                writeln!(file, "\nFull wallet association map:")?;

                // Count cross-wallet associations
                let mut wallet_connections: HashMap<(String, String), usize> = HashMap::new();

                for (source, associations) in &all_associations {
                    writeln!(file, "\nWallet: {}", source)?;
                    writeln!(file, "  Found {} associated wallets", associations.len())?;

                    for associated in associations {
                        writeln!(file, "  → {}", associated.address)?;
                        writeln!(file, "    In transfers: {}", associated.in_transfers)?;
                        writeln!(file, "    Out transfers: {}", associated.out_transfers)?;
                        writeln!(file, "    Bidirectional: {}", associated.is_bidirectional)?;
                        writeln!(file, "    Label: {}", associated.label)?;
                        writeln!(file, "    Tags: {:?}", associated.tags)?;

                        // Add to connection counts for both directions
                        if all_associations.contains_key(&associated.address) {
                            let key1 = if source < &associated.address {
                                (source.clone(), associated.address.clone())
                            } else {
                                (associated.address.clone(), source.clone())
                            };
                            *wallet_connections.entry(key1).or_insert(0) += 1;
                        }
                    }
                }

                // Print common connections
                writeln!(file, "\n=== COMMON WALLET CONNECTIONS ===\n")?;
                let mut connections: Vec<_> = wallet_connections.into_iter().collect();
                connections.sort_by(|a, b| b.1.cmp(&a.1));

                for ((wallet1, wallet2), count) in connections {
                    writeln!(file, "{} ↔ {} (strength: {})", wallet1, wallet2, count)?;
                }

                writeln!(file, "\n=== END DEBUG INFO ===\n")?;
                info!(
                    "Detailed wallet associations written to {}",
                    debug_file_path.display()
                );
            }
        }
    }

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let args = Args::parse();

    // Set up logging based on verbosity
    let log_level = match args.verbose {
        0 => LevelFilter::Info,
        1 => LevelFilter::Debug,
        _ => LevelFilter::Trace,
    };

    Builder::new().filter_level(log_level).init();

    // Clear debug file if debug output is enabled
    if args.debug_output {
        if let Ok(file) = fs::OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(&args.debug_file)
        {
            debug!("Initialized debug file: {}", args.debug_file.display());
            drop(file);
        }
    }

    // Run in appropriate mode based on arguments
    if args.cluster_mode && args.file.is_some() {
        // Cluster mode with file input
        analyze_wallets_from_file(
            args.file.as_ref().unwrap(),
            &args.token,
            args.max_pages,
            args.min_transfers,
            args.include_one_way,
            args.min_one_way_transfers,
            args.include_token_creators,
            true,
            args.calculate_balance,
            &args.rpc_url,
            args.debug_output,
            Some(&args.debug_file),
        )
        .await?;
    } else if let Some(file_path) = args.file {
        // Individual analysis from file
        analyze_wallets_from_file(
            &file_path,
            &args.token,
            args.max_pages,
            args.min_transfers,
            args.include_one_way,
            args.min_one_way_transfers,
            args.include_token_creators,
            false,
            args.calculate_balance,
            &args.rpc_url,
            args.debug_output,
            Some(&args.debug_file),
        )
        .await?;
    } else if let Some(wallet) = args.wallet {
        // Individual wallet analysis
        analyze_single_wallet(
            &wallet,
            &args.token,
            args.max_pages,
            args.min_transfers,
            args.include_one_way,
            args.min_one_way_transfers,
            args.include_token_creators,
            args.debug_output,
            Some(&args.debug_file),
        )
        .await?;
    } else {
        return Err(anyhow!("Either --wallet or --file must be specified"));
    }

    Ok(())
}

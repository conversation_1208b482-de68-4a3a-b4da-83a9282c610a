use anyhow::{anyhow, Result};
use dotenv::dotenv;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{debug, error, info, warn};
use serde_json::json;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use std::{env, process, str::FromStr};
use tokio::time::{sleep, Duration};

use solana_bot::{
    config::{TradingPool, RaydiumPoolType, TokenConfig, TradingState},
    raydium::{get_raydium_pool_address_from_api, RaydiumOpenBookPool, RaydiumOpenBookPoolState},
    raydium_cpmm::RaydiumCPMMPool,
    token_metadata::get_token_info,
    wallet::WalletContext,
};

fn get_pool_implementation(
    client: &RpcClient,
    pool_pubkey: &Pubkey,
    pool_type: &RaydiumPoolType,
) -> Box<dyn TradingPool> {
    match pool_type {
        RaydiumPoolType::CPMM => Box::new(RaydiumCPMMPool),
        RaydiumPoolType::OpenBook => {
            let pool_account = client.get_account(pool_pubkey).unwrap();
            let pool_state = RaydiumOpenBookPoolState::from_bytes(&pool_account.data).unwrap();
            Box::new(RaydiumOpenBookPool { state: pool_state })
        }
    }
}

const SEND_BUNDLE_COUNT: u64 = 10;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder().format_timestamp_millis().init();
    info!("Starting Solana trading bot...");

    // Initialize wallet and rpc
    let wallet = WalletContext::new()?;
    info!("Wallet public key: {}", wallet.payer());
    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::confirmed());
    let jito_sdk = JitoJsonRpcSDK::new("https://mainnet.block-engine.jito.wtf/api/v1", None);
    let random_tip_account = jito_sdk.get_random_tip_account().await?;
    let jito_tip_account = Pubkey::from_str(&random_tip_account)?;

    // Initialize token config
    let token_mint_str = fetch_top_token_address().await?;
    let token_mint = Pubkey::from_str(&token_mint_str)?;
    let raydium_pool = get_raydium_pool_address_from_api(&token_mint).await?;
    info!("Token: {}, Raydium pool: {}", token_mint, raydium_pool);
    let mut config = TokenConfig {
        token_mint: token_mint,
        raydium_pool: raydium_pool,
        pool_type: RaydiumPoolType::OpenBook,
        wsol_amount: 1.0,
        jito_tip_amount: 1_000_000,      // 0.001 SOL
        jito_tip_amount_fast: 3_000_000, // 0.003 SOL
        priority_fee_amount: 4_000_000,  // 4 lamports
        buy_price_ratio: 0.95,
        sell_price_ratio: 1.05,
        min_buy_price_ratio: 0.85,    // Don't go lower than 15% discount
        max_buy_price_ratio: 0.97,    // Start with standard 3% discount
        ratio_adjustment_rate: 0.002, // Adjust by 0.2% each time
        consecutive_losses: 0,
        trading_state: TradingState::Buying {
            sol_balance: client.get_balance(&wallet.payer())?,
        },
    };
    let (mut symbol, mut decimal) = get_token_info(&client, &config.token_mint).await?;
    info!("Token Symbol: {}, decimal: {}", symbol, decimal);

    let mut cnt = 0;
    loop {
        cnt += 1;
        if cnt % 30 == 0 {
            // check if we need to change target token
            if let Ok(balance) = client.get_balance(&wallet.payer()) {
                if (balance as f64) > config.wsol_amount * 1e9 {
                    // no holding now, we can switch token
                    if let Ok(new_token_mint) = fetch_top_token_address().await {
                        if new_token_mint == config.token_mint.to_string() {
                            continue;
                        }
                        config.token_mint = Pubkey::from_str(&new_token_mint)?;
                        config.raydium_pool =
                            get_raydium_pool_address_from_api(&config.token_mint).await?;
                        config.buy_price_ratio = config.max_buy_price_ratio;
                        info!(
                            "Token {}: {}, Raydium pool: {}, buy ratio {}",
                            symbol, config.token_mint, config.raydium_pool, config.buy_price_ratio
                        );
                        (symbol, decimal) = get_token_info(&client, &config.token_mint).await?;
                        info!("Switched to new token: {}", config.token_mint);
                    }
                } else {
                    cnt -= 1; // check it later
                }
            }
        }

        // Attempt new trade if not already trading
        if let Err(e) = perform_trading_operation(
            &client,
            &wallet,
            &mut config,
            &symbol,
            decimal,
            &jito_tip_account,
        )
        .await
        {
            error!("Error in trading operation: {}", e);
        }
    }
}

async fn fetch_top_token_address() -> Result<String> {
    let url = "https://www.okx.com/priapi/v1/dx/market/v2/advanced/ranking/content?chainIds=501&changePeriod=2&desc=true&liquidityMax=1500000&liquidityMin=300000&marketCapMax=********&marketCapMin=3000000&periodType=2&rankBy=3&riskFilter=true&stableTokenFilter=true&tags=0&tokenAgeMin=1&tokenAgeType=2&tradeNumMin=1000000&tradeNumPeriod=2&txsPeriod=2&uniqueTraderMin=10&uniqueTraderPeriod=2&volumeMin=100000&volumePeriod=2&totalPage=1&page=1&pageSize=5&t=*************";
    let client = reqwest::Client::new();
    let response = client
        .get(url)
        .header("accept", "application/json")
        .send()
        .await?
        .json::<serde_json::Value>()
        .await?;

    let token_address = response["data"]["marketListsTokenInfos"]
        .as_array()
        .ok_or_else(|| anyhow!("Failed to extract token list"))?
        .iter()
        .find_map(|token| {
            let address = token["tokenContractAddress"].as_str()?;
            if address.to_lowercase().ends_with("pump") {
                Some(address.to_string())
            } else {
                None
            }
        })
        .ok_or_else(|| anyhow!("No token with address ending in 'pump' found"))?;

    Ok(token_address)
}

fn adjust_buy_price_ratio(config: &mut TokenConfig, is_profit: bool) {
    if is_profit {
        // On profit, slowly increase the ratio back towards max
        if config.buy_price_ratio < config.max_buy_price_ratio {
            config.buy_price_ratio = (config.buy_price_ratio + config.ratio_adjustment_rate)
                .min(config.max_buy_price_ratio);
        }
        config.consecutive_losses = 0;
        info!(
            "Profit made! Adjusting buy_price_ratio up to: {:.4}",
            config.buy_price_ratio
        );
    } else {
        // On loss, decrease the ratio more aggressively based on consecutive losses
        config.consecutive_losses += 1;
        let adjustment = config.ratio_adjustment_rate * (1.0 + config.consecutive_losses as f64);
        config.buy_price_ratio =
            (config.buy_price_ratio - adjustment).max(config.min_buy_price_ratio);
        warn!(
            "Loss taken! Adjusting buy_price_ratio down to: {:.4} (consecutive losses: {})",
            config.buy_price_ratio, config.consecutive_losses
        );
    }
}

async fn perform_trading_operation(
    client: &RpcClient,
    wallet: &WalletContext,
    config: &mut TokenConfig,
    symbol: &str,
    decimal: u8,
    jito_tip_account: &Pubkey,
) -> Result<()> {
    let pool_impl = get_pool_implementation(client, &config.raydium_pool, &config.pool_type);
    let raydium_price = pool_impl.get_price(client, &config.raydium_pool).await?;
    info!("{}/SOL Price: {:.9}", symbol, raydium_price);

    let jito_sdk = JitoJsonRpcSDK::new("https://mainnet.block-engine.jito.wtf/api/v1", None);
    let mut bundle_uuid = String::new();

    let token_account = get_associated_token_address(&wallet.payer(), &config.token_mint);
    let token_balance = match client
        .get_token_account_balance(&token_account)
        .map(|balance| balance.amount.parse::<u64>().unwrap_or(0))
    {
        Ok(balance) => balance,
        Err(_) => 0,
    };
    info!("Token balance: {}", token_balance);
    let token_balance_ui = (token_balance as f64) / (10f64.powi(decimal as i32));

    let input_amount: u64;
    let mut output_amount: u64;
    let is_buying: bool;
    if token_balance > 0 {
        // start selling
        is_buying = false;

        let bought_price = config.wsol_amount / token_balance_ui;
        let sell_price = if raydium_price < bought_price * 0.96 {
            raydium_price * 0.8 // sell most aggressively
        } else if raydium_price < bought_price * 0.99 {
            raydium_price * 0.99 // sell less aggressively
        } else {
            bought_price * config.sell_price_ratio // sell with some profit
        };
        info!(
            "Sell price: 1 {} = {:.9} SOL (Bought price: {:.9} SOL)",
            symbol, sell_price, bought_price
        );
        let output_amount_ui = token_balance_ui * sell_price;
        input_amount = token_balance;
        output_amount = (output_amount_ui * 1e9) as u64;
        if let TradingState::Buying { sol_balance } = config.trading_state {
            config.trading_state = TradingState::Selling {
                prev_sol_balance: sol_balance,
            };
            info!("Switched to selling, prev_sol_balance: {}", sol_balance)
        }
        info!(
            "Swapping {} {} tokens for minimum {} SOL",
            token_balance_ui, symbol, output_amount_ui
        );
    } else {
        // start buying
        is_buying = true;

        // adjust price ratio if needed
        if let TradingState::Selling { prev_sol_balance } = config.trading_state {
            let sol_balance = client.get_balance(&wallet.payer())?;
            let last_trade_profit = sol_balance > prev_sol_balance;
            adjust_buy_price_ratio(config, last_trade_profit);
            config.trading_state = TradingState::Buying {
                sol_balance: sol_balance,
            };
            info!(
                "Switched to buying, sol balance {:.6} -> {:.6}",
                prev_sol_balance as f64 / 1e9,
                sol_balance as f64 / 1e9
            );
            // adjust buy amount if needed
            let new_wsol_amount = (sol_balance as f64 / 1e9 * 0.75 / 0.5).floor() * 0.5;
            if new_wsol_amount < 1.0 {
                error!("Insufficient SOL balance: {}", sol_balance as f64 / 1e9);
                process::exit(1);
            }
            config.wsol_amount = new_wsol_amount;
        }

        let buy_price = raydium_price * config.buy_price_ratio;
        info!("Buy price: 1 {} = {:.9} SOL", symbol, buy_price);
        input_amount = (config.wsol_amount * 1e9) as u64;
        output_amount = ((config.wsol_amount / buy_price) * (10f64.powi(decimal as i32))) as u64;
        info!(
            "Swapping {:.9} SOL for minimum {:.9} {} tokens",
            config.wsol_amount,
            config.wsol_amount / buy_price,
            symbol
        );
    }

    for i in 0..SEND_BUNDLE_COUNT {
        if is_buying {
            if let Ok(balance) = client.get_balance(&wallet.payer()) {
                if (balance as f64) < config.wsol_amount * 1e9 {
                    // already bought, change to sell immediately
                    break;
                }
                if i == 0 {
                    info!("Current SOL balance: {}", (balance as f64) / 1e9)
                }
            }
        } else {
            // re-calculate output amount
            let raydium_price = pool_impl.get_price(client, &config.raydium_pool).await?;
            let bought_price = config.wsol_amount / token_balance_ui;
            if raydium_price < bought_price * 0.99 {
                let sell_price = if raydium_price < bought_price * 0.96 {
                    raydium_price * 0.8 // sell most aggressively
                } else {
                    raydium_price * 0.99 // sell less aggressively
                };
                warn!(
                    "Sell price: 1 {} = {:.9} SOL (Bought price: {:.9} SOL)",
                    symbol, sell_price, bought_price
                );
                let output_amount_ui = token_balance_ui * sell_price;
                output_amount = (output_amount_ui * 1e9) as u64;
                warn!(
                    "Swapping {:.9} {} tokens for minimum {:.9} SOL",
                    token_balance_ui, symbol, output_amount_ui
                );
            }
        }

        let swap_tx = pool_impl
            .build_swap_transaction(
                client,
                wallet.signer(),
                &config.raydium_pool,
                input_amount,
                output_amount,
                &config.token_mint,
                is_buying,
                jito_tip_account,
                if is_buying {
                    config.jito_tip_amount
                } else {
                    config.jito_tip_amount / 2
                },
                config.priority_fee_amount,
                0,
            )
            .await?;

        // simulate_transaction(client, &swap_tx)?;

        let serialized_tx = bs58::encode(bincode::serialize(&swap_tx)?).into_string();
        let bundle = json!([serialized_tx]);
        let response = jito_sdk.send_bundle(Some(bundle), None).await?;
        if let Some(res) = response["result"].as_str() {
            bundle_uuid = res.to_string();
            debug!("Bundle sent with UUID: {}", bundle_uuid);
        } else {
            error!("Failed to send bundle: {}", response);
        }

        // try cheaper buy price with higher tip
        // if is_buying {
        //     sleep(Duration::from_millis(600)).await;
        //     output_amount = ((output_amount as f64) / config.buy_price_ratio) as u64;
        //     let swap_tx = pool_impl
        //         .build_swap_transaction(
        //             client,
        //             wallet.signer(),
        //             &config.raydium_pool,
        //             input_amount,
        //             output_amount,
        //             &config.token_mint,
        //             is_buying,
        //             jito_tip_account,
        //             config.jito_tip_amount_fast,
        //             config.priority_fee_amount * 2,
        //         )
        //         .await?;
        //     let serialized_tx = bs58::encode(bincode::serialize(&swap_tx)?).into_string();
        //     let bundle = json!([serialized_tx]);
        //     let response = jito_sdk.send_bundle(Some(bundle), None).await?;
        //     if let Some(res) = response["result"].as_str() {
        //         bundle_uuid = res.to_string();
        //         debug!("Bundle sent with UUID: {}", bundle_uuid);
        //     } else {
        //         error!("Failed to send bundle: {}", response);
        //     }
        // }
        sleep(Duration::from_millis(600)).await;
    }
    debug!("Last bundle UUID: {}", bundle_uuid);

    Ok(())
}

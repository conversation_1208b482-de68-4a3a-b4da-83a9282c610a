use anyhow::{anyhow, Result};
use chrono::Utc;
use dotenv::dotenv;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{error, info};
use solana_bot::{
    get_raydium_pool_implementation, get_sol_price, get_token_account_balance, get_token_info,
    send_tx_via_jito, wallet::WalletContext, RaydiumOpenBookPoolState, RaydiumPoolType,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use std::{env, io::Write, str::FromStr, time::Duration};
use tokio::time::sleep;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();
    info!("Starting Solana token seller...");

    // Get command line arguments
    let args: Vec<String> = env::args().collect();
    if args.len() != 3 {
        return Err(anyhow!("Usage: {} <token_mint_address> <ratio>", args[0]));
    }

    let token_mint = Pubkey::from_str(&args[1])?;
    let ratio: f64 = args[2].parse()?;
    if ratio <= 0.0 || ratio > 1.0 {
        return Err(anyhow!("Ratio must be between 0.0 and 1.0"));
    }

    // Initialize wallet and rpc
    let wallet = WalletContext::new()?;
    info!("Wallet public key: {}", wallet.payer());
    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());
    let jito_sdk = JitoJsonRpcSDK::new("https://mainnet.block-engine.jito.wtf/api/v1", None);
    let random_tip_account = jito_sdk.get_random_tip_account().await?;
    let jito_tip_account = Pubkey::from_str(&random_tip_account)?;

    // Get token info and balance
    let token_account = get_associated_token_address(&wallet.payer(), &token_mint);
    let (symbol, decimals) = get_token_info(&client, &token_mint).await?;

    let current_balance = get_token_account_balance(&client, &token_account)?;
    if current_balance == 0 {
        return Err(anyhow!("No token balance found"));
    }

    // Find Raydium pool
    let pool_pubkey = solana_bot::get_raydium_pool_address_from_api(&token_mint).await?;
    info!("Found Raydium pool: {}", pool_pubkey);

    let amount_to_sell = (current_balance as f64 * ratio) as u64;
    info!(
        "Current balance of {}: {:.2}, selling: {:.2}",
        symbol,
        current_balance as f64 / 10f64.powi(decimals as i32),
        amount_to_sell as f64 / 10f64.powi(decimals as i32),
    );

    let pool_impl =
        get_raydium_pool_implementation(&client, &pool_pubkey, &RaydiumPoolType::OpenBook).unwrap();
    // Get current price
    let price_to_sell = pool_impl.get_price(&client, &pool_pubkey).await?;
    let price_to_sell = price_to_sell * 1.2;
    let sol_price = get_sol_price().await?;
    let self_send_amount = current_balance - amount_to_sell;
    let min_out_amount =
        ((amount_to_sell as f64 / 10f64.powi(decimals as i32)) * price_to_sell * 1e9) as u64;
    info!("Selling at price {:.6}", price_to_sell * sol_price);
    loop {
        if let Ok(current_balance) = get_token_account_balance(&client, &token_account) {
            if current_balance <= self_send_amount {
                info!("Selling complete");
                break;
            }
        }

        let pool_account = client.get_account(&pool_pubkey)?;
        let pool_state = RaydiumOpenBookPoolState::from_bytes(&pool_account.data).unwrap();

        // Build and send transaction
        let tx = solana_bot::build_swap_transaction(
            &client,
            wallet.signer(),
            &pool_pubkey,
            amount_to_sell,
            min_out_amount,
            &token_mint,
            &pool_state.pool_base_token_account,
            &pool_state.pool_quote_token_account,
            false,
            &jito_tip_account,
            1_200_000,
            4_000,
            self_send_amount,
        )
        .await?;

        match send_tx_via_jito(&tx, &jito_sdk).await {
            Ok(sig) => {
                info!("Sell transaction sent: {:?}", sig);
            }
            Err(e) => {
                error!("Failed to send sell transaction: {}", e);
            }
        }

        sleep(Duration::from_secs(1)).await;
    }
    Ok(())
}

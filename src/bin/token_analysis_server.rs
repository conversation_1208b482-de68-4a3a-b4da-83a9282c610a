use anyhow::Result;
use axum::{
    http::StatusCode,
    response::IntoResponse,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;
use std::process::Command;
use tokio;

#[derive(Debug, Deserialize)]
struct TokenRequest {
    token: String,
}

#[derive(Debug, Deserialize, Serialize)]
struct BalancePoint {
    timestamp: i64,
    balance: i64,
}

#[derive(Debug, Deserialize)]
struct TokenBalanceResult {
    #[serde(rename = "BalancePoints")]
    balance_points: Vec<BalancePoint>,
    #[serde(rename = "TotalCurrentBalance")]
    total_current_balance: i64,
    #[serde(rename = "NumIntervals")]
    num_intervals: u64,
}

#[derive(Debug, Deserialize)]
struct TokenBalanceRequest {
    token: String,
    from_slot: Option<u64>,
    rpc_url: Option<String>,
}

async fn analyze_token_holders(
    axum::extract::Json(req): axum::extract::Json<TokenRequest>,
) -> impl IntoResponse {
    match solana_bot::token_analysis::analyze_token(&req.token).await {
        Ok(analysis) => {
            let response = serde_json::json!({
                "status": "success",
                "data": {
                    "token": req.token,
                    "analysis": {
                        "liquidity_pools": analysis.liquidity_pools.total_percentage,
                        "cex": analysis.cex.total_percentage,
                        "individual_traders": analysis.individual_traders.total_percentage,
                        "kol_traders": analysis.kol_traders.total_percentage,
                        "suspect_insiders": analysis.suspect_insiders.total_percentage,
                        "others": analysis.others.total_percentage,
                        "suspect_insiders_sol_balance": analysis.suspect_insiders_sol_balance
                    }
                }
            });
            (StatusCode::OK, axum::Json(response)).into_response()
        }
        Err(e) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error analyzing token: {}", e)
            });
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                axum::Json(error_response),
            )
                .into_response()
        }
    }
}

async fn get_suspect_wallets(
    axum::extract::Json(req): axum::extract::Json<TokenRequest>,
) -> impl IntoResponse {
    match solana_bot::token_analysis::get_suspect_wallets(&req.token).await {
        Ok(wallets) => {
            let response = serde_json::json!({
                "status": "success",
                "data": {
                    "token": req.token,
                    "suspect_wallets": wallets.iter().map(|w| w.to_string()).collect::<Vec<String>>()
                }
            });
            (StatusCode::OK, axum::Json(response)).into_response()
        }
        Err(e) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error analyzing token: {}", e)
            });
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                axum::Json(error_response),
            )
                .into_response()
        }
    }
}

/// Helper function to call the Go CalculateTokenBalanceHistory function
async fn call_go_balance_history(
    token_mint: &str,
    wallets: &[String],
    from_slot: u64,
    rpc_url: Option<&str>,
) -> Result<TokenBalanceResult, anyhow::Error> {
    // Get the Go binary path from environment variable or use default
    let go_binary_path = std::env::var("GO_TOKEN_BALANCE_BINARY").unwrap_or_else(|_| {
        "/Users/<USER>/git/kg-solana-data/data-writer/cmd/token-balance/token-balance"
            .to_string()
    });

    // Verify the binary exists
    if !std::path::Path::new(&go_binary_path).exists() {
        return Err(anyhow::anyhow!(
            "Go binary not found at: {}",
            go_binary_path
        ));
    }

    // Create a temporary file with wallet addresses
    let temp_file = format!("/tmp/wallets_{}.txt", std::process::id());
    std::fs::write(&temp_file, wallets.join("\n"))?;

    // Build the command to call the Go binary
    let mut cmd = Command::new(&go_binary_path);
    cmd.arg("--token-mint")
        .arg(token_mint)
        .arg("--wallets-file")
        .arg(&temp_file)
        .arg("--from-slot")
        .arg(from_slot.to_string())
        .arg("--output")
        .arg(format!("/tmp/balance_output_{}.json", std::process::id()));

    // Set RPC URL environment variable if provided
    if let Some(url) = rpc_url {
        cmd.env("RPC_URL", url);
    }

    // Set required database environment variables
    // These should be set in the environment where the server runs
    let db_vars = [
        (
            "DB_HOST",
            std::env::var("DB_HOST").unwrap_or_else(|_| "localhost".to_string()),
        ),
        (
            "DB_PORT",
            std::env::var("DB_PORT").unwrap_or_else(|_| "5432".to_string()),
        ),
        (
            "DB_USER",
            std::env::var("DB_USER").unwrap_or_else(|_| "solana_data".to_string()),
        ),
        (
            "DB_PASSWORD",
            std::env::var("DB_PASSWORD").unwrap_or_else(|_| "".to_string()),
        ),
        (
            "DB_NAME",
            std::env::var("DB_NAME").unwrap_or_else(|_| "solana_data".to_string()),
        ),
        (
            "DB_SSL_MODE",
            std::env::var("DB_SSL_MODE").unwrap_or_else(|_| "disable".to_string()),
        ),
    ];

    for (key, value) in db_vars {
        cmd.env(key, value);
    }

    // Execute the command
    let output = cmd.output()?;

    // Clean up temporary file
    let _ = std::fs::remove_file(&temp_file);

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow::anyhow!("Go binary failed: {}", stderr));
    }

    // Read the output JSON file
    let output_file = format!("/tmp/balance_output_{}.json", std::process::id());
    let json_content = std::fs::read_to_string(&output_file)?;

    // Clean up output file
    let _ = std::fs::remove_file(&output_file);

    // Parse the balance points
    let balance_points: Vec<BalancePoint> = serde_json::from_str(&json_content)?;

    // For now, we'll create a simplified result structure
    // In a real implementation, you might want to capture more data from the Go function
    Ok(TokenBalanceResult {
        balance_points,
        total_current_balance: 0, // This would need to be calculated or returned by the Go function
        num_intervals: 0,         // This would need to be calculated or returned by the Go function
    })
}

async fn get_suspect_wallets_balances(
    axum::extract::Json(req): axum::extract::Json<TokenRequest>,
) -> impl IntoResponse {
    // Convert simple request to enhanced request with defaults
    let enhanced_req = TokenBalanceRequest {
        token: req.token,
        from_slot: None,
        rpc_url: None,
    };
    get_suspect_wallets_balances_enhanced(axum::extract::Json(enhanced_req)).await
}

async fn get_suspect_wallets_balances_enhanced(
    axum::extract::Json(req): axum::extract::Json<TokenBalanceRequest>,
) -> impl IntoResponse {
    // First, get the suspect wallets
    match solana_bot::token_analysis::get_suspect_wallets(&req.token).await {
        Ok(wallets) => {
            if wallets.is_empty() {
                let response = serde_json::json!({
                    "status": "success",
                    "data": {
                        "token": req.token,
                        "suspect_wallets": [],
                        "balance_history": []
                    }
                });
                return (StatusCode::OK, axum::Json(response)).into_response();
            }

            // Convert wallets to strings
            let wallet_strings: Vec<String> = wallets.iter().map(|w| w.to_string()).collect();

            // Set default from_slot if not provided (e.g., 30 days ago in slots)
            let from_slot = req.from_slot.unwrap_or(250_000_000); // Default starting slot

            // Call the Go function to get balance history
            match call_go_balance_history(
                &req.token,
                &wallet_strings,
                from_slot,
                req.rpc_url.as_deref(),
            )
            .await
            {
                Ok(balance_result) => {
                    let response = serde_json::json!({
                        "status": "success",
                        "data": {
                            "token": req.token,
                            "suspect_wallets": wallet_strings,
                            "balance_history": {
                                "balance_points": balance_result.balance_points,
                                "total_current_balance": balance_result.total_current_balance,
                                "num_intervals": balance_result.num_intervals,
                                "from_slot": from_slot
                            }
                        }
                    });
                    (StatusCode::OK, axum::Json(response)).into_response()
                }
                Err(e) => {
                    log::error!("Failed to get balance history: {}", e);
                    let error_response = serde_json::json!({
                        "status": "error",
                        "message": format!("Error calculating balance history: {}", e)
                    });
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        axum::Json(error_response),
                    )
                        .into_response()
                }
            }
        }
        Err(e) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error analyzing token: {}", e)
            });
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                axum::Json(error_response),
            )
                .into_response()
        }
    }
}

async fn health_check() -> &'static str {
    "OK"
}

#[tokio::main(flavor = "multi_thread", worker_threads = 4)]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();
    log::info!("Starting token analysis server...");

    // Create the router
    let app = Router::new()
        .route("/analyze", post(analyze_token_holders))
        .route("/analyze/suspect_wallets", post(get_suspect_wallets))
        .route(
            "/analyze/suspect_wallets_balances",
            post(get_suspect_wallets_balances),
        )
        .route(
            "/analyze/suspect_wallets_balances_enhanced",
            post(get_suspect_wallets_balances_enhanced),
        )
        .route("/health", get(health_check));

    // Start the server
    let addr = SocketAddr::from(([0, 0, 0, 0], 8088));
    log::info!("Server listening on {}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

use anyhow::{anyhow, Result};
use chrono::Utc;
use dotenv::dotenv;
use log::{error, info};
use serde_json::Value;
use solana_bot::{analyze_trade, PoolBuyInfo};
use solana_client::{
    rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient},
    rpc_config::RpcTransactionConfig,
};
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status::{
    option_serializer::OptionSerializer, EncodedTransaction, UiMessage, UiTransactionEncoding,
};
use std::{
    collections::{HashMap, HashSet},
    env,
    io::Write,
    process::Command,
    str::FromStr,
    sync::{Arc, Mutex},
    time::{Duration, SystemTime, UNIX_EPOCH},
};
use tokio::time::sleep;

// Structure to hold transaction data
#[derive(<PERSON><PERSON>, Debug)]
#[allow(dead_code)]
struct Transaction {
    tx_hash: String,
    block_time: i64,
    signer: String,
    sol_value: f64,
}

// Shared state between producer and consumer
struct SharedState {
    transactions: Vec<Transaction>,
    seen_tx_hashes: HashSet<String>,
}

impl SharedState {
    fn new() -> Self {
        Self {
            transactions: Vec::new(),
            seen_tx_hashes: HashSet::new(),
        }
    }
}

async fn fetch_transactions(page: u32) -> Result<Value> {
    let url = format!(
        "https://api-v2.solscan.io/v2/account/transaction?address=6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P&page_size=40&instruction=buy&page={}",
        page
    );

    for attempt in 1..=10 {
        let cf = "Nl2nosgF82CfuUIZxfEeYQelMtR.tKEKqkBKsgyEL3w-**********-*******-XmnOCe6CxBZMaI9OTyHFYXtbgyci6k1Hxa.KaGqA24Rkoascb80E3c0jFAP5Zm6uy7BIhViDS9RQLKaHUZmwt9hCVmmUFTbnNiGMvPYqfvqydqkVBu8jf1S.OJVGI8UJGKCUE0.jkkvY9xUlBw0YmRUr1Utctlu1K3.seGZu_OeFzI5yT8EqGdg2R0xoJTLJVGpHixJlywLdry4SJ9KaHSDnhJyiFmpRPiwc6.znXCImjrwZsVVx4OsLNlpNpIfqPSrpfz9UMdfaIPCs8dwWOeM.GDmYiFUIcUPXmKF.dE3C5iYsI0L.RWu3ZpkX.LaDlGDOr46s5W9060K_8Q8XtA";
        let output = Command::new("curl")
            .arg(&url)
            .arg("-H").arg("accept: application/json, text/plain, */*")
            .arg("-H").arg("accept-language: en-US,en;q=0.9")
            .arg("-H").arg("cache-control: no-cache")
            .arg("-b").arg(format!("_ga=GA1.1.*********.**********; cf_clearance={}; _ga_PS3V7B7KV0=GS1.1.**********.153.1.**********.0.0.0", cf))
            .arg("-H").arg("dnt: 1")
            .arg("-H").arg("origin: https://solscan.io")
            .arg("-H").arg("pragma: no-cache")
            .arg("-H").arg("priority: u=1, i")
            .arg("-H").arg("referer: https://solscan.io/")
            .arg("-H").arg("sec-ch-ua: \"Chromium\";v=\"133\", \"Not(A:Brand\";v=\"99\"")
            .arg("-H").arg("sec-ch-ua-mobile: ?0")
            .arg("-H").arg("sec-ch-ua-platform: \"macOS\"")
            .arg("-H").arg("sec-fetch-dest: empty")
            .arg("-H").arg("sec-fetch-mode: cors")
            .arg("-H").arg("sec-fetch-site: same-site")
            .arg("-H").arg("sol-aut: fG8pAa8K8Vd0xB9dls0fK-wnkFqE55ihhrRuBQYG")
            .arg("-H").arg("user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36")
            .output()?;

        if output.status.success() {
            let text = String::from_utf8(output.stdout)?;
            if let Ok(json) = serde_json::from_str(&text) {
                return Ok(json);
            }
        }

        if attempt < 10 {
            sleep(Duration::from_secs(1)).await;
        }
    }

    Err(anyhow!("Failed to fetch transactions after 10 attempts"))
}

fn process_transaction(tx: &Value, seen_tx_hashes: &mut HashSet<String>) -> Option<Transaction> {
    // Get txHash and skip if seen
    let tx_hash = tx["txHash"].as_str()?.to_string();
    if seen_tx_hashes.contains(&tx_hash) {
        return None;
    }
    seen_tx_hashes.insert(tx_hash.clone());

    // Check sol_value
    let sol_value = tx["sol_value"].as_str()?;
    let value = sol_value.parse::<f64>().ok()?;
    if value < 900_000_000.0 || value > 2_100_000_000.0 {
        return None;
    }

    // Check for sell instructions
    if let Some(instructions) = tx["parsedInstruction"].as_array() {
        for instruction in instructions {
            if instruction["type"].as_str() == Some("sell") {
                return None;
            }
        }
    }

    // Get signer
    let signer = tx["signer"].as_array()?[0].as_str()?.to_string();
    let block_time = tx["blockTime"].as_i64()?;

    Some(Transaction {
        tx_hash,
        block_time,
        signer,
        sol_value: value,
    })
}

async fn tx_producer(state: Arc<Mutex<SharedState>>) -> Result<()> {
    info!("Starting transaction producer...");

    // Initial fetch of pages 1-50
    for page in 1..=50 {
        let json = fetch_transactions(page).await?;
        if let Some(transactions) = json["data"]["transactions"].as_array() {
            let mut state = state.lock().unwrap();
            for tx in transactions {
                if let Some(transaction) = process_transaction(tx, &mut state.seen_tx_hashes) {
                    state.transactions.push(transaction);
                }
            }
        }
    }

    // Continuous monitoring loop
    let mut current_page = 1;
    loop {
        let json = fetch_transactions(current_page).await?;
        let mut found_new = false;
        let mut found_seen = false;

        if let Some(transactions) = json["data"]["transactions"].as_array() {
            let mut state = state.lock().unwrap();
            for tx in transactions {
                let tx_hash = tx["txHash"].as_str().unwrap_or_default();
                if state.seen_tx_hashes.contains(tx_hash) {
                    found_seen = true;
                    break;
                }

                if let Some(transaction) = process_transaction(tx, &mut state.seen_tx_hashes) {
                    found_new = true;
                    state.transactions.push(transaction);
                }
            }
        }

        if found_seen {
            current_page = 1;
            sleep(Duration::from_secs(1)).await;
        } else if !found_new {
            current_page = 1;
            sleep(Duration::from_secs(1)).await;
        } else {
            current_page += 1;
            if current_page % 10 == 0 {
                info!("Fetching page {}", current_page);
            }
        }
    }
}

async fn tx_consumer(state: Arc<Mutex<SharedState>>, client: Arc<RpcClient>) -> Result<()> {
    info!("Starting transaction consumer...");

    loop {
        sleep(Duration::from_secs(5)).await;

        // Get all transactions and clear the queue
        let transactions = {
            let mut state = state.lock().unwrap();
            std::mem::take(&mut state.transactions)
        };

        if transactions.is_empty() {
            continue;
        }

        // Get unique signers
        let unique_signers: HashSet<_> = transactions.iter().map(|tx| tx.signer.clone()).collect();

        // Convert signers to pubkeys
        let pubkeys: Vec<_> = unique_signers
            .iter()
            .filter_map(|addr| Pubkey::from_str(addr).ok())
            .collect();

        // Get balances
        let balances = if !pubkeys.is_empty() {
            client
                .get_multiple_accounts(&pubkeys)?
                .into_iter()
                .zip(unique_signers.iter())
                .filter_map(|(acc_opt, signer)| acc_opt.map(|acc| (signer.clone(), acc.lamports)))
                .collect::<HashMap<_, _>>()
        } else {
            HashMap::new()
        };

        // Process transactions
        for tx in transactions {
            if let Some(&balance) = balances.get(&tx.signer) {
                // Check if balance is less than 0.5 SOL
                if balance >= 500_000_000 {
                    continue;
                }

                // Get signer's pubkey
                let signer_pubkey = Pubkey::from_str(&tx.signer)?;

                // Get recent transactions for the signer
                let config = GetConfirmedSignaturesForAddress2Config {
                    before: Some(Signature::from_str(&tx.tx_hash)?),
                    until: None,
                    limit: Some(3),
                    commitment: Some(CommitmentConfig::confirmed()),
                };

                let signatures =
                    client.get_signatures_for_address_with_config(&signer_pubkey, config)?;

                // Check if one of the recent transactions is a SOL transfer to this signer
                let mut found_incoming_sol = false;
                let mut incoming_sol = 0.0;
                for sig in signatures {
                    let signature = Signature::from_str(&sig.signature)?;
                    let tx = match client.get_transaction_with_config(
                        &signature,
                        RpcTransactionConfig {
                            commitment: Some(CommitmentConfig::confirmed()),
                            encoding: Some(UiTransactionEncoding::Json),
                            max_supported_transaction_version: Some(0),
                        },
                    ) {
                        Ok(tx) => tx,
                        Err(e) => return Err(anyhow::anyhow!(e)),
                    };
                    let meta = tx.transaction.meta.unwrap();
                    let tx_json = match tx.transaction.transaction {
                        EncodedTransaction::Json(tx) => tx,
                        _ => return Err(anyhow::anyhow!("Unexpected transaction encoding")),
                    };
                    let msg = match tx_json.message {
                        UiMessage::Raw(msg) => msg,
                        _ => return Err(anyhow::anyhow!("Unexpected transaction encoding")),
                    };
                    // Check if transaction timestamp is earlier than 24 hours ago
                    let three_hours_ago =
                        SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs() - 24 * 60 * 60;
                    if (tx.block_time.unwrap_or(0) as u64) < three_hours_ago {
                        continue;
                    }

                    // Check if this is a successful transaction
                    if !meta.status.is_ok() {
                        continue;
                    }

                    let mut account_keys = msg.account_keys.clone();
                    if let OptionSerializer::Some(loaded_addresses) = meta.loaded_addresses.as_ref()
                    {
                        account_keys.extend(loaded_addresses.writable.clone());
                        account_keys.extend(loaded_addresses.readonly.clone());
                    }

                    let mut simple_transfer = true;
                    let mut transfer_cnt = 0;
                    for ix in &msg.instructions {
                        // Skip if it's compute budget instruction
                        let program = &account_keys[ix.program_id_index as usize];
                        if program != &solana_sdk::compute_budget::id().to_string()
                            && program != &solana_sdk::system_program::id().to_string()
                        {
                            simple_transfer = false;
                            break;
                        } else if program == &solana_sdk::system_program::id().to_string() {
                            transfer_cnt += 1;
                        }
                    }
                    if !simple_transfer || transfer_cnt > 1 {
                        continue;
                    }

                    let pre_balances = &meta.pre_balances;
                    let post_balances = &meta.post_balances;
                    let signer_index = account_keys
                        .iter()
                        .position(|key| *key == signer_pubkey.to_string())
                        .unwrap_or(0);
                    let balance_change =
                        post_balances[signer_index] as i64 - pre_balances[signer_index] as i64;

                    if balance_change > 500_000_000 && balance_change < 2_000_000_000 {
                        found_incoming_sol = true;
                        incoming_sol = balance_change as f64 / 1_000_000_000.0;
                        break;
                    }
                }

                if found_incoming_sol {
                    let buy_info = match analyze_trade(&client, &tx.tx_hash) {
                        Ok(trade) => match trade {
                            PoolBuyInfo::PumpFun(pump_trade) => pump_trade,
                            _ => continue,
                        },
                        _ => continue,
                    };
                    info!(
                        "Potential target found! Time: {}, Hash: {}, Signer: {}, Balance: {} SOL, Incoming: {} SOL, Buying {}",
                        tx.block_time,
                        tx.tx_hash,
                        tx.signer,
                        balance as f64 / 1_000_000_000.0,
                        incoming_sol,
                        buy_info.token_mint,
                    );
                }
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();

    info!("Starting pump tracking bot...");

    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let rpc_client = Arc::new(RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    ));

    let state = Arc::new(Mutex::new(SharedState::new()));
    let producer_state = state.clone();
    let consumer_state = state.clone();

    // Spawn producer and consumer tasks
    let producer = tokio::spawn(async move {
        if let Err(e) = tx_producer(producer_state).await {
            error!("Producer error: {}", e);
        }
    });

    let consumer = tokio::spawn(async move {
        if let Err(e) = tx_consumer(consumer_state, rpc_client).await {
            error!("Consumer error: {}", e);
        }
    });

    // Wait for both tasks
    tokio::try_join!(producer, consumer)?;

    Ok(())
}

use anyhow::{anyhow, Result};
use dotenv::dotenv;
use log::{error, info};
use solana_bot::{
    telegram::TelegramBot,
    telegram_handlers::{
        handle_message, handle_new_chat_members, handle_referral_code, handle_start, REFERRAL_STORE,
    },
    token_monitor::{monitor_token_balances, monitor_trades},
    ReferralStore, TELEGRAM_BOT,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use std::collections::HashMap;
use std::env;
use std::sync::Arc;
use teloxide::prelude::*;
use teloxide::utils::command::BotCommands;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::init();

    info!("Starting Telegram bot...");

    // Initialize Telegram bot
    let bot_token = env::var("TELEGRAM_BOT_TOKEN").expect("TELEGRAM_BOT_TOKEN must be set");
    let channel_id = env::var("TELEGRAM_CHANNEL_ID")
        .expect("TELEGRAM_CHANNEL_ID must be set")
        .parse::<i64>()
        .expect("TELEGRAM_CHANNEL_ID must be a valid integer");
    let telegram_bot = TelegramBot::new(&bot_token, channel_id);
    TELEGRAM_BOT
        .set(Arc::new(telegram_bot))
        .expect("Failed to set Telegram bot");
    let bot = Arc::new(TelegramBot::new(&bot_token, channel_id));

    // Initialize RPC client
    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url.to_string(),
        CommitmentConfig::confirmed(),
    ));
    let seen_txs = Arc::new(tokio::sync::Mutex::new(HashMap::<String, bool>::new()));

    // Clone Arc references for the monitoring task
    let bot_clone = bot.clone();
    let client_clone = client.clone();

    // Spawn the monitoring task
    tokio::spawn(async move {
        if let Err(e) = monitor_trades(bot_clone, client_clone, seen_txs).await {
            error!("Error in monitoring task: {:?}", e);
        }
    });

    // Create a separate task for monitoring token balances and sending API updates
    tokio::spawn(async {
        if let Err(e) = monitor_token_balances().await {
            error!("Error in token balance monitoring task: {:?}", e);
        }
    });

    // Load referral store
    if let Ok(store) = ReferralStore::load() {
        *REFERRAL_STORE.lock().await = store;
    }

    // Start listening for messages
    let bot_handler = Bot::new(&bot_token);

    let handler = dptree::entry()
        .branch(
            Update::filter_message()
                .filter_command::<Command>()
                .endpoint(handle_start),
        )
        .branch(
            Update::filter_message()
                .filter(|msg: Message| {
                    msg.chat.is_private()
                        && msg.text().is_some()
                        && !msg.text().unwrap().starts_with('/')
                })
                .endpoint(handle_referral_code),
        )
        .branch(
            Update::filter_message()
                .filter(|msg: Message| msg.new_chat_members().is_some())
                .endpoint(handle_new_chat_members),
        )
        .branch(
            Update::filter_message().endpoint(move |bot: Bot, msg: Message| {
                let client = Arc::clone(&client);
                handle_message(bot, msg, client)
            }),
        );

    Dispatcher::builder(bot_handler, handler)
        .enable_ctrlc_handler()
        .build()
        .dispatch()
        .await;

    Ok(())
}

#[derive(BotCommands, Clone)]
#[command(rename_rule = "lowercase")]
enum Command {
    #[command(description = "Start the bot")]
    Start,
}

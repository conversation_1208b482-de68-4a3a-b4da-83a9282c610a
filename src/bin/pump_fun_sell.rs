use anyhow::{anyhow, Result};
use clap::Parser;
use dotenv::dotenv;
use log::info;
use solana_bot::{
    config::TradingPool, pump_fun::PumpFunPool, simulate_transaction, wallet::WalletContext, PriceProvider,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use std::{env, str::FromStr, sync::Arc};

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// RPC URL (overrides RPC_URL environment variable if provided)
    #[arg(short, long)]
    rpc_url: Option<String>,

    /// Token mint address
    #[arg(short, long)]
    token_mint: String,

    /// Pool address
    #[arg(short, long)]
    pool: String,

    /// Amount of tokens to sell (use 'all' to sell all tokens)
    #[arg(short, long)]
    amount: String,

    /// Jito tip account
    #[arg(long, default_value = "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5")]
    jito_tip_account: String,

    /// Jito tip amount in SOL
    #[arg(short = 'j', long, default_value = "0.0001")]
    jito_tip_amount: f64,

    /// Priority fee in micro-lamports
    #[arg(short = 'f', long, default_value = "1000000")]
    priority_fee: u64,

    /// Slippage percentage
    #[arg(short, long, default_value = "5.0")]
    slippage: f64,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenv().ok();
    env_logger::init();

    let args = Args::parse();

    // Parse arguments
    let token_mint = Pubkey::from_str(&args.token_mint)?;
    let pool_pubkey = Pubkey::from_str(&args.pool)?;
    let jito_tip_account = Pubkey::from_str(&args.jito_tip_account)?;

    // Initialize wallet context
    let wallet = Arc::new(WalletContext::new()?);
    info!("Wallet public key: {}", wallet.payer());

    // Get RPC URL from args or environment
    let rpc_url = match args.rpc_url {
        Some(url) => url,
        None => env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?,
    };
    info!("Using RPC URL: {}", rpc_url);

    // Convert SOL amounts to lamports
    let jito_tip_lamports = (args.jito_tip_amount * 1_000_000_000.0) as u64;

    // Create RPC client
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    ));

    // Create PumpFunPool
    let pool = PumpFunPool { token_mint };

    // Get current price
    let price = pool.get_price(&client, &pool_pubkey).await?;
    info!("Current price: {} SOL per token", price);

    // Get token balance
    let token_account = get_associated_token_address(&wallet.payer(), &token_mint);
    let token_balance = client
        .get_token_account_balance(&token_account)?
        .amount
        .parse::<u64>()
        .map_err(|e| anyhow!("Failed to parse token balance: {}", e))?;

    info!(
        "Current token balance: {}",
        (token_balance as f64) / 1_000_000.0
    );

    // Determine amount to sell
    let token_amount_to_sell = if args.amount.to_lowercase() == "all" {
        token_balance
    } else {
        (args.amount.parse::<f64>()? * 1_000_000.0) as u64
    };

    if token_amount_to_sell > token_balance {
        return Err(anyhow!(
            "Not enough tokens. You have {} but trying to sell {}",
            (token_balance as f64) / 1_000_000.0,
            (token_amount_to_sell as f64) / 1_000_000.0
        ));
    }

    // Calculate expected SOL amount (price is already normalized for decimals)
    let expected_sol_amount = ((token_amount_to_sell as f64) / 1_000_000.0) * price;

    // For selling, we fix the input token amount and decrease output SOL amount as slippage
    let min_sol_amount =
        ((expected_sol_amount * (1.0 - args.slippage / 100.0)) * 1_000_000_000.0) as u64;

    info!(
        "Selling {} tokens for approximately {} SOL",
        (token_amount_to_sell as f64) / 1_000_000.0,
        expected_sol_amount
    );
    info!(
        "Minimum SOL amount with {}% slippage: {} SOL",
        args.slippage,
        (min_sol_amount as f64) / 1_000_000_000.0
    );

    // Build and send transaction
    let transaction = pool
        .build_swap_transaction(
            &client,
            wallet.signer(),
            &pool_pubkey,
            token_amount_to_sell, // Fixed token amount to sell
            min_sol_amount,       // Minimum SOL to receive (with slippage)
            &token_mint,
            false, // is_buying = false (selling)
            &jito_tip_account,
            jito_tip_lamports,
            args.priority_fee,
            0, // self_send_amount
        )
        .await?;

    match simulate_transaction(&client, &transaction) {
        Ok(_) => {
            info!("OK! Transaction simulation successful");
        }
        Err(e) => {
            info!("Failed to simulate transaction: {:?}", e);
            return Err(anyhow!("Failed to simulate transaction: {:?}", e));
        }
    }

    // Send transaction
    let signature = client.send_and_confirm_transaction(&transaction)?;
    info!("Transaction sent! Signature: {}", signature);

    Ok(())
}

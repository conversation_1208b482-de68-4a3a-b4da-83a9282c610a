use anyhow::Result;
use clap::Parser;
use dotenv;
use solana_bot::token_analysis;
use std::env;

#[derive(Parse<PERSON>, Debug)]
#[command(
    author = "KryptoGO",
    version,
    about = "Analyze Solana token holders and categorize them"
)]
struct Args {
    /// Token address to analyze
    #[arg(short, long, required = true)]
    token: String,

    /// Solana RPC URL
    #[arg(long, default_value = "https://api.mainnet-beta.solana.com")]
    rpc_url: String,
}

async fn analyze_token(token: &str, rpc_url: &str) -> Result<()> {
    // Set RPC URL for the library to use
    std::env::set_var("RPC_URL", rpc_url);

    // Get complete token analysis from library
    let analysis = token_analysis::analyze_token(token).await?;

    // Print results
    println!("\nToken Analysis Results:");
    println!("======================");
    println!(
        "Liquidity Pools: {:.2}%",
        analysis.liquidity_pools.total_percentage
    );
    println!("CEX: {:.2}%", analysis.cex.total_percentage);
    println!(
        "Individual Traders: {:.2}%",
        analysis.individual_traders.total_percentage
    );
    println!("KOL Traders: {:.2}%", analysis.kol_traders.total_percentage);
    println!(
        "Suspect Insiders: {:.2}%",
        analysis.suspect_insiders.total_percentage
    );
    println!("Others: {:.2}%", analysis.others.total_percentage);

    // Print addresses for each category
    println!(
        "\nLiquidity Pools addresses: {:?}",
        analysis.liquidity_pools.addresses
    );
    println!("CEX addresses: {:?}", analysis.cex.addresses);
    println!(
        "Individual traders addresses: {:?}",
        analysis.individual_traders.addresses
    );
    println!(
        "Suspect insiders addresses: {:?}",
        analysis.suspect_insiders.addresses
    );

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    dotenv::dotenv()?;

    let args = Args::parse();
    let rpc_url = env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
    analyze_token(&args.token, &rpc_url).await?;
    Ok(())
}

use anyhow::anyhow;
use chrono::Utc;
use dotenv::dotenv;
use env_logger;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::info;
use solana_bot::wallet::WalletContext;
use solana_bot::{
    get_raydium_pool_address_from_api, get_raydium_pool_implementation, simulate_transaction,
    RaydiumPoolType,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use std::sync::Arc;
use std::{env, io::Write};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();
    info!("Starting Solana token buyer...");

    // Get command line arguments
    let args: Vec<String> = env::args().collect();
    if args.len() != 3 {
        return Err(anyhow!(
            "Usage: {} <token_mint_address> <sol_amount>",
            args[0]
        ));
    }

    let token_mint = Pubkey::from_str(&args[1])?;
    let buy_sol_amount = args[2].parse::<f64>()?;

    // Initialize wallet and RPC
    let wallet = Arc::new(WalletContext::new()?);
    info!("Wallet public key: {}", wallet.payer());

    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url.to_string(),
        CommitmentConfig::confirmed(),
    ));

    // Initialize Jito client
    let jito_sdk = Arc::new(JitoJsonRpcSDK::new(
        "https://mainnet.block-engine.jito.wtf/api/v1",
        None,
    ));
    let random_tip_account = jito_sdk.get_random_tip_account().await?;
    let jito_tip_account = Pubkey::from_str(&random_tip_account)?;

    let pool_pubkey = get_raydium_pool_address_from_api(&token_mint).await?;
    // Get pool info and execute swap
    let pool_type = RaydiumPoolType::OpenBook;
    let pool_impl = get_raydium_pool_implementation(&client, &pool_pubkey, &pool_type).unwrap();
    info!("Found Raydium pool: {}", pool_pubkey);

    let swap_tx = pool_impl
        .build_swap_transaction(
            &client,
            wallet.signer(),
            &pool_pubkey,
            (buy_sol_amount * 1e9) as u64,
            0,
            &token_mint,
            true,
            &jito_tip_account,
            1_400_000,
            4_000_000, // 4 lamports priority fee
            0,
        )
        .await?;

    match simulate_transaction(&client, &swap_tx) {
        Ok(_) => {
            info!("OK! Transaction simulation successful");
        }
        Err(e) => {
            info!("Failed to simulate transaction: {:?}", e);
            return Err(anyhow!("Failed to simulate transaction: {:?}", e));
        }
    }

    // info!("Sending swap transaction...");
    // let uuid = match send_tx_via_jito(&swap_tx, &jito_sdk).await? {
    //     Some(uuid) => uuid,
    //     None => {
    //         return Err(anyhow!("Failed to send transaction"));
    //     }
    // };

    // info!("Buy order sent! Bundle UUID: {}", uuid);
    Ok(())
}

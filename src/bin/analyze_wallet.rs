use anyhow::Result;
use csv::ReaderBuilder;
use dotenv::dotenv;
use solana_bot::wallet;
use std::collections::HashSet;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::init();

    let wallets = {
        let mut wallets = HashSet::new();
        let file_path = "./data/merged_wallet_data.csv";
        let file = std::fs::File::open(file_path)?;
        let mut rdr = ReaderBuilder::new().has_headers(true).from_reader(file);

        for result in rdr.records() {
            if let Ok(record) = result {
                if let Some(addr) = record.get(0) {
                    // Skip addresses with 0x prefix (likely Ethereum addresses)
                    if !addr.starts_with("0x") && !addr.is_empty() {
                        wallets.insert(addr.to_string());
                    }
                }
            }
        }
        wallets
    };
    println!("Analyzing {} wallets", wallets.len());
    let result = wallet::analyze_sol_wallets_batch(&wallets).await?;

    // Extract the results for the "sol" chain
    let (signal_wallets, bot_wallets) = result;

    println!(
        "\nAnalysis complete. Found {} profitable wallets for signal, {} for bot:",
        signal_wallets.len(),
        bot_wallets.len()
    );

    println!("Wallets for signal:");
    for wallet in &signal_wallets {
        println!("  {}", wallet);
    }

    println!("\nWallets for bot:");
    for wallet in &bot_wallets {
        println!("  {}", wallet);
    }

    Ok(())
}

#[allow(dead_code)]
const GEM_CHALLENGE_ADDRESSES: [&str; 395] = [
    "4chn22VoRYH81ZQnGJcaRA1LDgdViaaAK8BS4W277777",
    "9VeU65v3JG977Z6hC5iNVQHjK1BfMPTZvDA7QU6kthvY",
    "GBkb2WBEyeUMuvoKkwqqFGAP2m7WnzBDWYqztP2jME4i",
    "wbsUXURMM5cCH1XbskF2mPGx8SSGBgx3sM2kQuoABAn",
    "6QQmC4uWFjzGXbYkdSQB9ApaC28xwK3pdmhWejyuPXjq",
    "edWs6v4gDKXzkwRcn8R1QDwpEoWJ2WoLh3MN3xj76Kd",
    "C79KBjDmGWXK5LC3iWn4qwdWusQFxkqZaUYsvmauhqYE",
    "8n4fXBNzJVk4JtXWnodn7TwtEmTJ3dGDVzzJYCRNjhK1",
    "9TfsmWAvusCDKcGHaHXvFS7Jdv21RZcPKhoKJHWTA8qa",
    "EPpAy19LQLeR6vF24esghFrHpHxP5zuSYfrUZcvP8C3r",
    "********************************************",
    "9ojeD7Cso3vWn1Dbvaf8ZHuvcBrueihScGPeCAgNSakG",
    "A8CQVwoP5dyb3qmrG8YeZvD5jsrqF5UL8aruLjR6qWbH",
    "F6t9GCweJ9UiKiFxP5tv1NciVCTJCQnosMADSNBbcfni",
    "9PY1LvNMji7zCG3FitiwshAdyeVaqYhUW3D3uucrewvz",
    "64s3TaShJemKp9YDYghBwpaDow64vevukh9Apbcw7Jof",
    "8UitW7ALToN45jCLQF1nSq2GH8eo4C6DsVRrJ5G9F8pu",
    "DndFyqpAbxEqx6Sqe4eQ6DnAnDot3Mjias1mvftXXrKD",
    "7zrzCbPoNoLeu1KoNfHBac9zjqvMDUF7GwpogriwvuWX",
    "DGXER59JXizUnmpJX3X2oX3WpzZzLqaVxdYbNVBExkKs",
    "GBAiUhLsY95uqBysyST5oeJzyd2ELqNawwGX1a72B3Xe",
    "CxePXzQqo1gxy8RAyCriS9RZKZdaxG3ZGpEQFp48xchU",
    "7FZ2H76AS64CPaR5VhLQMGaNEpG3UKLAWT2YPfgqwBBs",
    "7qqhNZQPUV6LGr73v4Ji47x9rRpQKb4oU5vVw7LCtu1r",
    "FsxiPhfgmheSYWnLuLYrA84k1u23p8wNofSKKbnTqiFp",
    "HY7btEdm2x2neFv4sswfGdm8KSR31wDiYr7D7DjD1VqL",
    "3JiVuZKwToUxNsZdQZ6MRNCfR5vT1k7vJ6NLqGtMvZDw",
    "DskSv3kXmdAzsU1sYpGfddi6GGf5SEtJmreNC8352uTy",
    "BQVVqeWLwg2qA4EcJLrkTwBoqvkg8GQytZQAug6gQGAM",
    "3A66ZaA2NJifJNnCH2JaSoxajJseh7VRUoBsZFH4k97o",
    "BPYWBFLFsbWBJa4Q95e4A1hx9xgxGs6N8Sbs2ok5RgND",
    "2fziKaSbzVpKWkmQhqCpsuPjp4db4ELuUkQSjjuJnf6u",
    "CNUrF17w7eem4Vi9iMmW4urp611wLjKDoa78ew99CXya",
    "6kBJ4mn7SukBWBTXRy4MKnndnvJwrjcuUC9bV6CxEpxG",
    "ELogMsMC1STrCZpyr4CCXt9b5fUi1vsBdnHjVqGukGNQ",
    "57wkXiGdTmv2aYgeKnXkia3Dbq9Sa8soPM6nRmL2TABB",
    "Er5J82JV9KSAegxKchU56ydCkC5qipGNKFzceCK3S5Uf",
    "G5xnKxGJu5GT2ipjcZK5ZTHzqCFMXwhtDsSyZNCf2yPP",
    "7KiJSeVECt1RbsvvnRrgxwkTdKqC3pc7DV7VL3KqvyUU",
    "EjWELmVdRA23RJARegc6zpZY6kDGahvpS3Dz386E1g7q",
    "9Ut4df1UimsRafqbdBDdLYPp2ZqTSKvMFXqRrfCDDUPA",
    "8mYY7eZRJQbxfhbPx6pJAQspB1R6K6LJ6wmX4X4a7wHk",
    "BJSCxuDbiu5qbN2KFSq3q7UmYeajfR1nCEgAPvznvYD9",
    "AMMo8Zc1XN8aFHZxpP3S7JMAbpbkgKkSF3h87fMe8G5g",
    "3AXWLbkQu2pz5WLCBsk66BGKEKhSkrqvNcTJeGKurVKn",
    "HdtVQMLnJ38wFh4CWE62iCGVUWfeWeYdaQGysH8rsnR8",
    "3Zjvd1Cz2V62DTDDNboupSQcy25irbPQ2Ti4ceTnux9G",
    "HBBBVpDJ85a3v5JquGKCJAVJYRj24Rrdjc6uXHjYLLEw",
    "EpKt1QdsKYrDDvmhfMCCSPDzu1DCJB1QkFQCBmo5umDM",
    "6EKgnF3EEi3VXHw2np9wxxgPkc9CEQh2bedkqMWdBHC2",
    "BHzFj3vYPR1PzvqWdRbT7dXzdxd3qLokhDkezEtA1PFi",
    "EFzuebAYQo9CnfN7gXPTH14kkJ9eaaMNk262JViGKYHn",
    "F8thwuukc5hA8CswhuAx6N2T143j2B1MXXMrySZE7n4",
    "2YrxyGb8ptHJsVRVMPTNWCq5SsoaVGjE1NTYvNzuuggm",
    "Ai5nDhQ6f5T5iPh6vP8w3RgUFvA9xjbq9LLTMA2E4cSD",
    "Atw43y8UAy8twn41rzMhMAiYrWFYydkP3pfgqfTBNEuu",
    "AY19bbzzeBVfiF7uZkfxEizGUBwQsitWDht2t9tMYt69",
    "FkwA8kKCanrVB2foRxMQnzAPrZLe2M1k2aXzcuTcz888",
    "6v4z3hX6HjqwHLNWTuc7PbrtW68vV6ygBrTTps9f2ME2",
    "2YTDgakJ4fNknVGnnf6ChgbmmJHcX2RuFX34UuQTgJ3V",
    "xxtY3VgEDNUodH1fMcKK6NdhgWXw9EiwL1RBPYem9hA",
    "AHNKxXG4GcuNoi3cj86sjNfs6rXvLxyXo34KbkaQ7sZh",
    "HkF2v6RdBEQeNxPavNJDhNBGzns5qr8jUeG7PdKm7gSu",
    "45gTmXx8z7aRVNtKsDtvZor3mta6iPTvM5iZJSJHC4se",
    "Adp2dsqu7bdBRoEUtLZuEbBkywdcsM7ejwC1YhWBtXVX",
    "HDagdb97U1Mjs8VgPToopu3X457zsFUjt4uCSbhgermy",
    "xxxxxm2ELrvk1L738ex7f3VP95dybqncJUwRXds9Y9Y",
    "CcGvk374FUBUDmbfnG1vZYWmFKCM5t3koxtjCnV7mPft",
    "FQdBr2a67wz8QMwxyVuSkeRR6NtUVohGFyXYVdQLgb8U",
    "8964q22oDCGYcDagt5Eo4a7XSRf1eyog8WekBjfmUbx3",
    "CVZ7TvZFvCHukKRXC4ZStGES3L52DcXr8tkUS4pD86av",
    "J8KZNLtoQU4ppntmEUaammgdN6X4RpuG2XRGuAjU5wK2",
    "8AWLcG79MPnt4bSQL5y6MDpPvjtBqg9s7S9BdPeiGt3v",
    "3nZstduHKUmpJUoFMRpPDwYhKndCxZ8hrNSPXuKTA4Ue",
    "FTRavshbG4yfshJxw9mYkzz2XDSjcYhJJeUWEqCwbpPh",
    "9HPCMCspsKwic4nZ5kCWeVk2V33mZfijk951JokCRdp",
    "9x74KyCKZbF3XGbrNeyycw2qnUC6YBUxcxAFGQKnrJx7",
    "FaVKtYPch95YFG1sD3G4kiR7BW4HKChbT2wLnH8UFoq8",
    "DKj4ivLSSht3cnN3tKcBKfEkpttUP4aYXhLYEKjyGFzx",
    "338av9F3V59Ze9xM9b2xgyfe7FMnxrYa2FJVDc3D3888",
    "2pTmDjz6Hng5ntmhUEPDZ7q4N8huNSUDbRuUFhjLPJKL",
    "2FFVRPcQYrdnzTt5M8tZWNZGceQsofYhcGih2S3YjAqg",
    "PG1LYphq5RXSGEvDzHTv8eKQhy2b1znBuCFh5tZ4pjL",
    "7BRYsN1ymDiK6rgQ5U5GDY3HeL2ePQbooVYqTsPstCJr",
    "4DTqCzAd3PYApcoPQnQsVhExtfPtJ9ZDw2KXpKx7RmNA",
    "HmDxUUv7vYExPa7QFswbiSVPnStigQALZs9JQxBv5z78",
    "DqvqhUr9oTTXFvrNkQEgntJamrZBoAJoAp2rQbqYyVsx",
    "BeqWxjPhcc4ZyMksWJ4ZrGXvwuUrYmstn6caHKMSMGfT",
    "54NwDZPTQqxajKq1mbLYKvewYqpuEqgBY4fBKxpphqte",
    "8ziExw5aE2ncHrmdSswFue6kbtHhoDcH91ibiAoejzuF",
    "2L2C2P5RTUkGvZSos6jgnv3L5RWyjRQ7BXVSXYVcpkNd",
    "HeV95UE98PpnQXHLRpWjSFcLbwJegqWY5XQf9F4BU5wS",
    "4f4d7wyNbEK3LtNUS95wWvgxqtzE6gCLtXfBbZBJMWyo",
    "ELHBPXGiZgGyiuyj4kEBwcAS5stqbFPUB2LoAgZLvPvV",
    "7Xg91Dc25PTHkAq29r9QN2B6S29kk5kHkcYQeDV6pUn5",
    "9f9BfrzJW7tDiA7KggnMA9oiWaqRcSDxgcNemo4o6uRL",
    "xiEHYwwdP1DwTqndxBwAy7eSrH4jcLiNaiWNhSam9tX",
    "5J2DbT5jFu4CToX6Vj4vohJuEf8gYVqsQgSaAv79GVdE",
    "CJYVFVtFe13NApjRcmV3X74RQuqCE4sX8YbaGjGc9F8z",
    "3QACqQguqjHGF6UGL123tcbUdZyj4eG3nK7UjPsdLSLr",
    "9kHSoYDB6U9H7v37w3KUiFvA7DAJrSjQERZgfpcnupC",
    "44rFKHhoHmiV1xZHrNcJP5twMH57C5jegGXdHpiiDvSX",
    "HebyShd4EpbuNpdxongnV3mXTkcRCredV55Wsih9HLrD",
    "Ayy991zv6T3JSpm65W8AktuF7kC29MXZamr75K8wbQvk",
    "GCVffvYHtD2GTNA9QT6EWnNS2QV71Zu8F5qqqSjtAWQ8",
    "oN2hTr71rjeUGRRkZjvtxfeoaoF2ETCYGuJ9jQH1Hyq",
    "4Etg4WAcSxvjXM5NZibb3a9Xip6LybnPTN2aF3CekJnK",
    "BoeUmd1gQAb3SoHbbdexN3xN3rn6NarBuz9H3U4RB96q",
    "AHwmhLNSxtghERYHAHGuQh8x5tAJLgsuNVN5aEFBy5Ho",
    "6wMfwtWAnQDLRiVGAW9WaHohLLpHoeuVTyh53pBgSUag",
    "8ZbV14WBSbThCDFBcnxgeb3bYf5MaTJhuAYmohmT6RYi",
    "9BqeVgZhGsahuKT4TL9pqpmwACtRjewifsEmHXLViBaj",
    "DMGTiqYveVYzWShm3XukEZ9SGdYoUVKf3NiPWFT44QMq",
    "8KzjSriMUfNFB4zuzKXisBXrtBPALzdFmP43j76K2BGV",
    "EBsVGZ9KuC9Xc49fVrDNdcJNj6E4VzmzhrqwYVva4G6n",
    "mcBsT6KHkJRTxHZhrn3PsNgyCqCcrxT6fUpHMePM4WA",
    "7ApVW6RTzWUDpjUj2Q83owVepBtjU1HDnQXZBxZ6cG5o",
    "EMaRzx13Pa3fwgRx9VTSvtR8nBtCT9TVk5hXsUa2Yp1z",
    "99g2eWTbNE4XCWGZXtxdkcmC73HQFECFdRPR7L8HnJcY",
    "FVWNQ2TLs5c4RMVi169b24oRMZJmf5UcGNGFw1Ctr2y7",
    "********************************************",
    "49TR8nLyaASLW4tvADQ2SxhsawDNeeQ2fdxjNqPfYX7V",
    "8Nwmfv62baSof89rfbqJr4iVamfYrBuLTuGas6asMVgh",
    "49ygHwgjadoJ6RbYJfeVLoLdjFSSDoGzGQhJ1iDSQRZJ",
    "AJpyw3u88TpMbf1mgR5YqHYYJQZpGKEHHXBMvA5Nay6Z",
    "C5urJ6sVHKqqSFCBZ5avYk5usmDAt2SUHicwXgtUHSWP",
    "QJ8mdr2gT3rGBM5StbnzcV2iLEKN7DfEoSbdCK8AmgE",
    "CWKoLFcCKGUXEMoG3PQaRgNwfMmbtzfTHgGcvBVPCGAK",
    "43TXfb2ywQVrm13tXX2pha64qHf4Wy7cNA7hU6Qid1Jc",
    "J3DCLMCDuCXfunY63RZjvLT6Wwc1RCXiGXpSgAZgvgP",
    "5GsNfLVa7QSPmzWJbEfcMWUJkRYReBVqiwfGYnYAeDdS",
    "********************************************",
    "2mt1ts54Upugp71moL9dEs1YZBJeyhDsrQgfyTasrh8e",
    "FbqfsVp8oGGP5totezcAZwm3fXk95gsh9U523Pc9bRMm",
    "9gGnHPjH9ihwfMhL8aB7MZBYntYrFAS4VXxqqLxXpQP6",
    "Mer5gbyEDobHC9Zh5EuyN4QeUsyvkn9rr6EBs6mCwmc",
    "9jas8D7pYUp8yu6CXGrLmx3NHhMH3dnkQj4FJzjYNQnW",
    "EfntxMLR2xrV3gkMcX1kPv39LC9q6UgVewqUnNjzr2qD",
    "9YWQ8NKrA9hXbj9mNCYkAquGHHofPoLSQkEDJeSHLSZB",
    "DsqXpqFCVYZk145uG7jXC4nu6MdRM12EFFm6a6gsJZFm",
    "8Fs4pfi4a8tskMtYEUUJREH4bUzADVBH79vZipYiJJc9",
    "Dn62HkEeH8UR1UXGiDdDsZNLgj1bX8Rr2sASeKPsnvTe",
    "J1zyRe7chJwdW4s8Z6DVvacPfBEceaYCSaerLAQWD6NH",
    "AA6fzeLdLbFXesL9CZvJE3Xsk2Sjjf77vWwTLhQhGLv5",
    "6Bw4ZTYSvjyi2vFRUGkUsrpp9jcLUCJCV1Co3tGnActP",
    "8SBQEm3XhTWqeYtUNLZB8U4VQpRSRmLy7pUNYEJeP6UK",
    "Hn8J8xEWA4RR6BXRqnp63ioxMnjw3w6RZbB9TPJ9FYRP",
    "FboFXcAmdmis9C9AeCqnYp2nzob9W1hF4AUN6BFzVsh6",
    "Crsb6ZdFpT2vc29srFgL41F3mjzBaP5wTk1jqvTv4twq",
    "ogWanGYqGP2Aid7rxj4hzB11nP7wGkrRRYtMWNUHKLT",
    "XdNzz1GvYDF4xhhcbscKdu3eUaVZvBadt11EJwMUqkp",
    "rybKTG5tRzh2twCTcXW4C23EZRRoAF3VCcBPB9NZtBN",
    "BuXhF7LRAyPweq4H2YHwj6cis6sKnmapBoChupC1gN1G",
    "8pFdLsFGpbdpPLiN1HNJg33diYgAGJcTaPbieaKmX7DS",
    "D3H2d7vMnDDu7HrpX1CJa9Vn2dDtdzH9TGewcEtnPVtA",
    "H6YdghWLfcPNiRst3vDATSmUYW8WR2GDAyTwA8G2ioFE",
    "DrakebpGw8BcbsguJStX86xdDzHr3ejmx5ZW6wGHNsLn",
    "5jQMAvyjT2fRmShWpAjwjjpeRCh7bTvC9K8qteYa47tw",
    "45qxyU9Ymw8QyGAXwhbyepa8od3w1HwLq85eTNuemkzP",
    "74KAWpBfqzuRdMJ36WJCGXATx1uVpK5QgaRWFr6JoLha",
    "7nyx1fUjXoVfcMVTf3AXEL96W6PSHFkgbyXfsoj8TkjD",
    "4oHbGzaVQchGbReg2UKPYPMf9mda7xuSbXSk8XhJMhg9",
    "4a1QcxFuEkTh2HWS4UxUB75BeCvYmm8m4ocaNwfzoRxy",
    "3P4FU3Ke3uDyfYM7qc3FtJ6b2ivGuXMU5npEMRqwne9P",
    "FJUywzxYTn2TZfGspqxEZyc7N2UAkWjWTedJ4PyQuhmV",
    "5oPEs1bswxZAap5K7hxQHTM2LAYvgAggCzgo13vQpYDS",
    "2Atfa6m3hMVCBE4PXK2TsgVszeFehFHUcSUwCkewLKM3",
    "DRBfn72GxQTsovu6Puev5w2bLbjBdRA6j3e7xas185DV",
    "Cyuv5TU6BAgafwQ15pVDcHqpmCf4kmJmL3rSfNu78zwW",
    "GZienXUKkTnDJo6JjoBQ9ZDqQ352QmhTjvivwry2s6p8",
    "24SgXk4c1WyCAhzP7yjFPGVjMFWL4jmp8NMHV6EfjJNj",
    "HwCRdPQG6fHApFb18BuSqhiRCeJUFxVDMC5H3dU5dyFx",
    "9pJd6nNfTRYpaG1HNGwxRn6zRu3BN2Sd9wvUpPG5H81K",
    "GEpkCiH6kexybkEDgq51F37i6zLCFKmyXnAJiBj5SbTg",
    "6qw3X5MoiaiHuCgP4MXqkQpaxr73LjNvDJmv9QUu1G81",
    "GiWJbkcVeeghBwutCiP7rGHCN5o9VofWcMtLK3w9sUsn",
    "EMYuUcGPzSsCv2CZQwo3StqH6zJwjUEKSQxT7qHRamzU",
    "GT5G8egFFgww9t9upcNuSUD8fxu1yyK6bNFa1WM57Yrb",
    "hfRQY82we4mnqpz2gvczDHrALLiB9v4Z3ccNH1pMyWF",
    "Fu2NRyXugU4m9w9rri9FbXnxmdaYULYFp1fd8d1ncZBE",
    "AqA9Lob5XosQYHYFVRZbiHNUoZcHydExtGLsfVBc4u3D",
    "DCymXbZxE7aF5MFVSHKXURicFxrMAmTRdBRhnxFfoFsL",
    "38jgFqz5xuLMiYCPqr4iC8FYN9c16AXQP33YQZm3U3e7",
    "BhD4Wm13RDqsQhyuAr3GKTpmddteond6tHpQR5q4D2xv",
    "FToAn3WgeQ45PYbxJiuahtqcNttpV1pMn33UTuxi4Ykv",
    "DJ5qsx7YYgUeSnU5sBYB3Nq4ct93rXBqk9mLz2th9vfG",
    "HRCqGz5pWpgiUaiEqeSQRaL3QbcjLp3tAC9AeqkptUY7",
    "C63evSiU1Czh1zpwpQnnsauRfVRToZJbLwsD5zGdA1bc",
    "BQzecFoZBD2ERRkntfBTYFicZQ3nxZ7bj3PYBnwEw9q3",
    "4FT9JMvmtVTfAvgrf4gA1EYhcwtkUKpi5s5fC94e2Y8G",
    "FMo2CBD6AcAYdp8vsgH2vDGVcsme39iLUzGyw4fqLFhQ",
    "E4nXWv6MQHH3nEoZKBKN4dzkQKoJT8ucENDCkWAwDUJQ",
    "8Y2arE66nSYLBDstnMN7wbvmaywfC73yUSBcmkeCNaDz",
    "WHpGxDiGLp86JJh2gghz1W6CzuvfwwD3Qqa3QBxshDJ",
    "Eq45hDNF3UWVCShEjP9yS6JdNAvpQjJHt8rkbtCZhzVx",
    "winYm5UfuRc1deRDBn1rsR3JRXxD91VkMCrXbgzvg4a",
    "rxLcE3dZm7sDLxJ6SBzHRmtV87zQtSbWdcrFrbuTX7W",
    "p77rbkh7kHS2ZxEZX1o328HegRp34HKMQmZrnE7HPCN",
    "77RkSLhxc5BAqbdyBtQBDLNLqU6pNxYVx2RZwfoEsL6h",
    "ETr99qr8G1KVvECMXMupUPv8JdMGjN86pJsSooxrBbV",
    "BdXwkazWNJS3W83Za7eaMFMKNbJj8CYeYPycTLDYSfnv",
    "CGbvNg5Wbvc6UXoAaXW8XBtXLyM424nEnUpbfRPv5tAn",
    "9DPdLrwbrPjasYvX4iHmAvjfScfu8sD4oAJ5vCLg5qvf",
    "888aao3KLaSf5qPKjyBGcQpHefA7XdTfNiHLDMJdaQ5F",
    "9KpzSFvaKHrigDWuRtKPA2esuMN7sqQQ9fGhWm8agqjT",
    "2DFYmFhqFfHjWGxhfGsLLhaM7jVTFZBRYMEDUeJ6EQKK",
    "DyhLwGkZ9eEPeoucoAv7LYizRLGRWEMrCQn1i87SZNJi",
    "DRqKTewPMeDAq3sgVFSC7iXU8PXP6bQcyWCkHFDm6sNx",
    "********************************************",
    "A21WKa1LG7j8KWHncGfBvrJVBa1bmNXvGGmXm7BhLobw",
    "9Ap7f2F3bgKvRxmunKRN3uX94ax2Qva92q4ARUnSQ2b3",
    "2Cqho1RW4RYpzXkCYPujRetbvqYZgWpthruSv2E7KtD1",
    "ALycTZAyW9DmxZ1EhPrmKAtZb8R8pUUQBb8GRg4YZwKt",
    "EZKRBN3gaiMEpsM7SvmE1Wo7pXX4Fedzn6syXFrnnray",
    "Q9yxTV9tdhfY8si99VzDCS43haVP2BipddZgM8eHKgW",
    "73UnW3k2prcL76FQezAtS9hEAFSKEfkxJyANJZcwzndh",
    "92DqcRBChjsCqSA6WERifPbRSbWeC9MF9xh1Hvkp75pv",
    "8QAAR2GAWn8DVibhDHTTkuGoG5KSYCi36QUSeuzmyDDc",
    "HrX3JCKYruz4MNz3pomFBqdWbkxGDWir84fEgFKkiiSQ",
    "J7LphzcSU2XwnMGeCJU7G75HejEfFpGgWBTe5mDDELrp",
    "7qi8RyWmYbv82yjixhj7pdpmfGhLJW8jo7gtitFBr3wj",
    "EJB6fxJEgfxwDqLXaggvWmX7DsdDErE6P8rc7Y3mW4W8",
    "4khv5YD6h3RFtQf486Zjmr9jzYG5sfEarxq12vuotjmK",
    "BSV29v5A1MCCFXVjnny8HXKJtWbQz4j4uhF59DjXEAJU",
    "DF7LkmrUyBqDKFEohrdSAAjkmSdPvmJVX13xGv72pGeG",
    "DUpm3DHQXxBPUYuw5DbsLD697xqdTyKCBAcPamj5KQqC",
    "DQfMGuhKfsiL6Enbt4Agc83CPjAbyjoPT6MmEJFYVoKK",
    "4KXJZVbpEFKNSeXjMpGVtHGkUFAorrbVkcotzsfGBQxi",
    "CFmkryvhmCQNkpq5aAX8CqXYCE8rFWoZh4hkdwsS66f5",
    "HsbcTwACoCcL2VLiWmEPMfDrCXrC9CL5MbikuMvo71JZ",
    "FjJXd5kbBeCAiUUDTAg8thUSWdzygenFugaBZkC2gMdP",
    "C3KStSyTZG9RZkto7bis4q3hMLJjeJiawuVQ6RgArGwH",
    "DMinSzmTpG4LLFrChsKryNrdn51jr77eKXHXHzdwuNQc",
    "Leo1EiHRLUGNrQEQCbDVz1pYRQZd761MNMXNBSB9Hrs",
    "2RMR1NaZ9XFS4oCfvEMaVEbCFQoQRsCRRPCAAEdEdKnA",
    "7ecf9qZ6zzDsoZpgFJPnSjSCAcQnG6X8KUnRwDE5HE12",
    "GZAqSttHjMXKqgXE8bv17ytmbgfaN3cNWbPkMJaHsBts",
    "DaB7XpWG2ho2WFJrXKAGLKAQT3NZdcwD4iA16HjDKxHh",
    "75CAjxib214dw5bukhwHxdd8sSqQEyHTJEtZwLBniEqF",
    "6ZY8jJ7n8GBYeevqJfpftpwDpUnXWbvKHyrtDUkqZHwG",
    "8fmYbZWAZB7qTZRhXLg1J9yNaHTCzVRNgVajHMp8ygti",
    "D6sjTpeVtsAi1QsSuntgYJoqhvtLeHGvkztP3ipKYPSq",
    "AGAzuZL6qWJokvBfgjTjcGDiA4cmV8p174F6QV3SushB",
    "3Mq3FFQzNBPJcYPkVuQzx3whVaDoCuFbm7C9cY2Fi6SN",
    "DUdhWFtQikf439j1XztD7ZgX9hVfpWXCURouWjeermJ",
    "3KsSqdQr1sknP8gvrbYQLE1nandb7TNzDDL8kcRTpnie",
    "94PS2ECdKGdTpmfqXEEkhr4qAcjKmD98Mfmu3qMxH8fC",
    "4beeA3W1bB2kVnZW5ei5gsSKBKgCm5YzuisHRRUhZWSB",
    "BCnn7iduqG1862Nz12ttDcqAsiJmkF496fEjuaTKTnrx",
    "4vwc6pfRSNYKMBLwdmiCuVXX4e8hHv1vJnZ3PF8J8Hq1",
    "2FyEdTHMmKJY74J3D6dxpXbBHiQbUJMFaVDPuxfzqZtk",
    "HkUpmpTWqasPkTu7M8NRSs9WvXJB6pLNtRTcXhHhqDBn",
    "8k6zo8FMLAfrvcPbCSqJMsq88GKfBg7AtshRrWc8q15N",
    "2RBh4qnmVcK8a4e1gffEJWTgDBvATH9tCCvhbeiZyDpd",
    "6nekc6PpzWsjJaT2KcQNn5899KooGiKiGyif9RpNCct1",
    "2HJsrXLnJnjv8pzQkcVJpGjZJoSKoZkNvc3BY5XJ9jL7",
    "5421XxP8p3QaKSJGa97E1ZrwRTZLhweZQgiPi7fw2AtV",
    "B5A895mPP2pQ39FCS7u2qpsscReuShsenLMmzDNVjhL6",
    "AJNCRPgtcXRViSZQjZyuzqfAAU4sgRXvPMHQiQuExr1E",
    "dNT7HnGwoyX1Dpmi7FhZ5cyhfbErZfwKQCyzSGxhhcb",
    "EATpsoLmqaSKwCguGZ1p9DmLPC9hbEUda2UPpQ7YZY3X",
    "BtMffVPsSLFjQFPonT7jEeW7aFvtwzx7i2xq5cAFXi19",
    "EKzfrJzLPDcEdAPN9LAiNKzd1swDioxsqnebPkewGqj8",
    "DwdHkDS2scALdwVBnvCcqLoQdWMjRBoHBbVtgToGKcjd",
    "H1v8ALtvS95pZZxYc69HPbEhuGHSLrxXbcibD3quEP9b",
    "EDAAuDoqqf9ru6ev75AXtbN3RzJozMzWMsfuH4AX1jjP",
    "5TZMXmvKDBQBGgp17N3FcF7ThiqE7VraKoxViMham3bz",
    "3YaTsYcxVaW64Td8E2nGoHBAxxtw4KxPH5JTQC6SnUbm",
    "BWc4F1DHYHL4qLk8qmUeuWMXxpGdxPza3tyqJ35fNx1",
    "FpByaRtW5GxAW4WHYQVCtCptpBdHB8b86CTztSB9HFFG",
    "4anNPzvawU4dumT6jMmh6j8vWAcj8E1cPZDTdUSxFuQy",
    "5weXHNxaJqvSAedqPseTXsP7mp7VTFA69jo7gujyEaQQ",
    "3fu7bj2A7vbRPvsaWtdMDmCyyMCc6iRpitFn2ifbhAHQ",
    "AFPS7wP5hbovoVYrnxwFpvHx1ZPgbvhhX7cTES6cRg3P",
    "7125KJWbMBUNsHHcxyT4y9BFTNU7x1VFfzvyqLkiN59a",
    "5qWteNhiwQr2JbrACkGmB5F7YHZf6MoHJeVnfoyw41fr",
    "8KdaK1xrCRkGZUxfAVynhrqRFZUSy8qnRo4mGKWxDDDX",
    "rQEtBU34KxRwJVWJQ1rGS8GinSnJ4ksqFCxVdni8F6N",
    "AkKDkhtGVXHuzDXKuq321i2MHTwio8AgFbRXb5RtwCqP",
    "7MXXD43DoZYgERKRg1jPCRWjY2zyzDRdSTyEvNQ4b9NV",
    "GMfm2CABppjnxkqXJpU5J157YE5Rvb4qeBVezyePP7Rx",
    "Cbxce69s3w1NKia3b1mvRvFp61Jbt2YycCH9R8jaoWph",
    "8YoVRVagWE3fnTPabcrWVcXfEyfbz4rTHYvzNe9fp5yR",
    "6H7BcqMtnNWKNmKBichCkkWMSs3u4pB4jYTQvWTZA1MN",
    "HVmDPCMv2ZQJU4X8UVUqfyqPVC7pmQRF9JU8323koSHb",
    "6TDPoEdGSnfAKiz1gUdJLCZjrAVWjbRNTK3dAjffvkrj",
    "6KaHd3v32niAeUn1SfQufbWkrrA1G4V2vdgSqDNECfaJ",
    "6t5CzPEwpdqEHcXmSN7eMz15KaFxmR8Fb2GNrqyG66Fo",
    "KuPUnVvLMkP3KdvsnbsRFudVpJ9F1wHC2oXqZkSSzhj",
    "6yzEqmWwjFgyqhrowGUfbDv3fD3Vh42B99oEPCZxjskf",
    "BoyLB66fhe5GwMrbYUMqsBMmeg8iR8SfKpdjnu7afHzr",
    "AmZyDinS3jsEsjHADW3J13QjZVQEAiQD5opMwPmK1E13",
    "2EkbabJpGZ5RccW2w4KSHxe9xnyhtr8jV41YnjByDJzr",
    "GodQF4Ad4ryqTMCWmmMLZXLWT2Big3PfyYrzPj6YbVAi",
    "4hjm3LeF7rvFJ9x24dzCEBsmex7a8cN55JLm6N6UAsvR",
    "BffUSz2SsuAvygNEtLuXbEpYxjkGRPDfcRK62eUpEbwv",
    "8557W6JziZ6jXxrbJtMBR7e1gk9r8tCn89tURfiEnmtJ",
    "GXUCBC1QsjASib4C8pVDcSHxuc1QD6BsKo9MKudJismQ",
    "2884bZ3aKLZD1Ed1pGox865M1VLsrhSiNRQDYHWnyqZF",
    "5umwgR3sDpL6zxLRQ9GcCthq94xjhg9mULL6TGHMZ537",
    "8wof7CVSPP9kkwhW1waP4cAhGzCZS5RS8tngq8de2yB8",
    "5Wo1i9KWzSP5Gku12K6eDcLXMch76fV6ZWNSukrxnJ52",
    "4aHYQY6o54URPGxA2LcH2X1sG1xJy1RraQMBSYBXEUdd",
    "nHtp5hL6ukTP55r5aJRzW1tvijm2AQHFUCXtLGGGzZG",
    "xxxxxt2X1cPj7qeCfPVgQMQd4q7YGeRHwuveC3XfpuF",
    "C6GxQ2yYoxryxuEo1RPzeDpbTRp5X6JCLiRYPwwn6ykx",
    "B11mFmUtjpD8QSrJ9ar4VwCV1iqqAYxUDpv8mNQuxjjV",
    "3Rods48wjSWKMvQmsrhHsszM59djDhtFiYsbufRM8888",
    "Gc4tnGYYd1p2F9EKUfKbJ82AJJJmq1uVy5yBHU9FUAjb",
    "8MZo92DwCqUADUMS78Li1wBzDNm27SwtbPaYjuMD78nc",
    "DZowg3K8E36FygEQTUWVMugsjZ83soCxNsZF3duUbuDe",
    "6Taee3NaM1HTkDh5ccU4wbrezktoYxr5xKmLtxCdzzW8",
    "HLuYHPTzu5jY5A6AuYxhDwXGsjPhWr1qPbo2CNdGqRp6",
    "2FJMknBhBzGJwE5eJm49MNucBWikK48zzGt32cgj6sk6",
    "J6bK4CZrU9Wu8KTZsc6spAPGMbokS6Ds7GH23Y7C2o4h",
    "Hdn5bDyteQRrAi1vyF2sgnuRmBqrYbJcdj4WBKdHTQeb",
    "DxzupwkgUpH9ca243BUyb1oScQm7HbddVE1SKaDnt8Xq",
    "HDq2rKPxSTpiNvSLw2iiv9QnAar7aFDYLMXvKKqfZrTF",
    "D1g7337N96jgpRMeG9rjgxpCazwgCYQkhvJQCVorNGAV",
    "4ChxC2TozaP7LvkEWyWoeTonHU58EjASgqSipbJHVRJ8",
    "PVPNpoeE3gM5QkCYvF6ZUQrvYjaV5gW5NEf3zVjz6xa",
    "CzxhB65NC8q3JBhAi65oFrraeFqizRsU9wuf1WzYptYB",
    "Cv3ZZKZjoxRXSTZNDTyhcvzp4TqVvNjcCXEH9VK6GFQR",
    "5b2BG66ZU218shagH8wPRdDhhyDfNFLUE8WaTPeMRxXa",
    "7ULkR9mDZXmVZbJRRp3PmuTbAi98ZG31sFhhDvc7R6bA",
    "4gePeWU8QZnR99oWeVpGErcgrWSofD23GvHrgfEVuvWE",
    "1dPeBA1od89NmpHx5U9zdFCAJyQq6Vh1XtLfBUytqFx",
    "BXZGM7q5cKdL1k1VgFqs2giP8dHooQv75G4uEUyXRDnH",
    "Zfm28n32EhCxkYrPj6wvYQyPLve6kgeZ4GJ1Rkhd8YS",
    "CM9XYrfwYCKYbr3b1UGM7rNuheRuP78CGfdFMphjMn8a",
    "F281Psi2MtXbQAHAyyUoCDQXup7UBd5WHcgZZ1Sic5qY",
    "Cdz5vJAQ6eCpt1RvzW5E6m8Lakg6i5G2WX6jcNGdCVQF",
    "G6mHAPPZjCuAVx7gs23ZEBbcrV7qeJhKv8NxP3tCz4mC",
    "Fo3CzaMjD1byUgi77GSJM5cZ8APacWVidEq9c8eK4RWx",
    "B8RW8q5W2CEKQemGdCehaQ8rKygdVVYA3MdBSR5YWYzP",
    "Hb1eMtVN7a69NF2yP2MkwDFTnSeZfmAkwzMcLJHpiMFY",
    "EH1y1RDN8pKHpEtswu8vqim5TbRVrksTeoB3dSzE5ezg",
    "FVnanw3v59Jt2J8XWPxQgJxXSpMK8HhqzQNtT1Ysgwcd",
    "3RsPgFFd69P1q4eWiccH2wsGAaEr7GEzjmF1GVLBK2Z2",
    "Eruto4uG8AazmYgZLBjsmEdksAc6X8sXLgNGoGkcy3oj",
    "GysstA597MFko4vBWuv43PhxczGtPJxtsFJExkKUoDZu",
    "9a4v3E2aoi8dHsw6rsT2o6nqVWqAKpYkscMoreaAyvn6",
    "ApUTX9tcZMzQwN4dn47PipkRkycuZZ4LtJjoxDcHitfh",
    "FZrVGY9Csi9igTM2sYxWAQU8F1uNrEeTF5rBJ4wmCCpf",
    "94SoeSNoDQfYB88hiXrA9xfcmSqSzHGyemn96h7PaFQj",
    "3fyG3fhqDGEwGqYw2uk785xtscBMhBjeatHJe8WBTSEq",
    "9upbSVpVgmpRAuLwSUU6nKMJTFu1wDWugDkZFtg9GqUr",
    "3ke2XiBTJ47sscZEuom9Fyvr8HBkY9UsNH1DewVReeN1",
    "FdgRE6b8XGwRcM7Y1gNukzbRpjgn4C6fCYV3nxhkVpCt",
    "GXJ8ECCsjFQ9Fv1pDoQJJapXwUtnzkajsZ6kkP6Fxa8v",
    "kaLqohEydzcPNBRBuNXPtaMVGjKfSN9Tub7riEMGjkF",
    "HZRVYniV74BWHfqVBQtkfVqx7guAfbMCpHKqFGRbXX4D",
    "D1zUtx3hZKyK95L9Fp5rcpf5zEj8dUiG11WVhuC5yKFQ",
    "EaN16tXB6qbFyrZkLR3oXhombsjRTppB1CSVzRktm6VU",
    "3QJqJZ9rLQS7q2CDwx312JUi3W5JteiohrNMDCPH3LDA",
    "5atBsnu9QwEUXggQfbMFSZJ4cWZsBdoXkLVTfwhC9UWH",
    "BmT41XpAySSTxM66fvLFz7LK1Yg1EqN7e6nhnGwaq7ug",
    "58kZiqu9EPbUDbLcSBdTE8jY7Yu9w2eoR52a6bGZ1s6B",
    "HmXPnNqbpYidS5bKYnTdWxChu4asq8SXdADzGGKf4kPE",
    "9sKYPPZGnFzq1NJHVTp8gZMYzYK7RAR32aDj2Po3gMhN",
    "G1n3T8SCxCQQCsdqmNUHYVbgns5rGhEbTZE8KD9bwLVF",
    "2mpomgkUecx3hay4WnnJqyH7NhT5svhec2MuM6HPCAYm",
    "7yxuCxa8MXqf3fDAu4j5FKG9e25j7czKDKBn3ADp3c8L",
    "3MEcTmcxzsuxFMijPNPhHop9UAnCuELHrXYXTgNrxbpN",
    "5fnEVvPmpXTkvHWdm8ZBPzEioLMyCnWXfJAZWBMSrvwB",
    "2ZGtuGrNVksDogdVg2WEDgh6DoUQrYja88sh9jvyb7Nf",
    "DbwCRGqy1Px6cPYB6EQxV6aR9yYQSNaTsmazZas5PNAf",
    "3DZvWL3mpZdQ1QMZWspq5Pws6dawrepR5eL2T8htJp9A",
    "8wvzGaxEdxSWTZox14YZCuusKj9PE4x3ajcyASZU3bAo",
    "Eov4sNbxes29hJvzWdfMGSkfnFjq9w6HkEKENNYBMiRm",
    "8ikfMoLR5R14eD5mUb3mVreccTtWC1SbQ3khSkbV4suq",
    "DLasLkjdnM1fN1z6rVVf4AHZtbmgg4oDoYGakbgmtkaa",
    "549KWTbmfHdpJegybYKRYVxxHew6azvRbKiwJaW6HTcb",
    "HWsnxSogfLMPhZFnR4iBDfJG2YyYQRDGvXFDFxgwXPRp",
    "8p7rKehenUgUEvVNUbixaxwhX7b2JUhvCM5BbGX5k4Yr",
    "7qfns4GYCRw1rJggMc2Q9LsiCqFBGRS3AymW1zNuky4W",
    "CLSPFraoyVi6jseeJYnHN7XzDJXz5HCwpM6vfYLgBXjZ",
    "C4H5PsQiCE6FZYFwqb8dLv3GUmwoD8uBf6WRzragWh1b",
    "6y9NuZN66Ua3EjymPsqgm7dLJbzb4GEjnWimHDBBbJ6V",
    "8L9e5stMDqNUnYo9GUmLKkWeYFEp2k68mtxZwb1mMSc6",
    "4abh2iwZkwQDTHmQpAbqgKzdATgUCWYUDBEpwom1Y7KN",
    "AKTWsoUZGLFXiPuBSSK2cPnv3w66dAByMUx4yp9HG8YS",
    "FEkDnaP3wCHScBEtDRc4TEJjQpEofjVHLVwgRGoo3Kgk",
    "Biavt8bVq6jFtjwSYoany1gbu5vyYBEMjUoSMDjN8hHJ",
    "7UGodP1H7juSaYpWJaGDKWPpraS3YpyeTPEqfgntkPL6",
    "6KyCw6uLwLspLhK57iKp8mR2bfKJ4HFJ6xHmzn7tmanJ",
    "5Eg9nspTdUA7ZKuQLL99q4FJ6Asip7tmsVSphNprmYRk",
    "2xHaw6fKTX5LXAks8YJzH87WbjS27iap9g5gPdU31XS3",
    "EAkUfcTiZhqVj7XGv1yPwGPcjYqFqxeKgobdVQsrSYRX",
    "4FAML5u4Sa46P6RxpZ8SAyv74Jh1Bdg8HQBWMp1c92hf",
    "BxndHqBGKVmcdaaDicmD1jkxUAg9CSBcBHZ7RARHtvuw",
    "88fxk6wV4hUToT8wcJD6irzqoyDN3zFU4bFy9TkU33DS",
    "2LXBveNJx2arUMtdqGEkNFfVYCLmBzavgmGWjDzw7gXa",
    "8js67hWCe5ibKdv1nFYx3C7JRW7KXceRxmV9ksCn9sNc",
    "4YRZkvN1WhKyPRuYZsgdkuPfqBd5PHyvrwr9evrvC6Ke",
];

#[allow(dead_code)]
const HARD_CODED_WALLETS: &[&str] = &[
    "2DvRjoHEMxV18BRLjaox26QXtWFyQ4DLhvp99Bh2LDWy",
    "2Gx1kgjifDETxYwzF5g8EiPpSQuscjmLJjm6NyQrs3RD",
    "2Nucs8jQUT3L7r9vuNTNqqMwuqzMFwn96ZzreFsYQWFx",
    "2UWZ3gbc5f1qkbR4iunrhJkiD6A1yoN2UVUx4zFaHcYF",
    "2rjqaHF1s3nxhGtgQePL9QLcUQzhtZ9c5rQgaNVf2eGk",
    "********************************************",
    "********************************************",
    "********************************************",
    "********************************************",
    "********************************************",
    "********************************************",
    "********************************************",
    "********************************************",
    "49o2UDTaER9GJoXK5TPj4iX8vWBa2mpcScLgqpMPk3Cy",
    "4H8ufwUMENCi618RkE3TEP9urBEvF3QGVbehAohiZLSf",
    "4MmCH8NeeiPyZgcC134TYn6X2kfM2DEfDWQruRuYh3Md",
    "4aNdza2uhwGzMcvki8yiYAFqhdMNzuCh3S6hHvTbhdBe",
    "4cXnf2z85UiZ5cyKsPMEULq1yufAtpkatmX4j4DBZqj2",
    "4i2BUHRjCQLnyjpFse82Lc3mRUJ3QpprXTuiht8QK9yS",
    "4o26ovFnYEAwbQFrbPadRp5JCS4Aiw1S1vWfCnAsYoMt",
    "5NkTEqeDE7M1RXpiy95fSMZqeMK1yEFcaHd16nxB13Qu",
    "5PbKHeoFrbtht7S27SzNhyUmJ3fGMXWqGxraPgm3wgmT",
    "5RVAcmwVdjumYGnqBKH9MvjQ1M2apkGhCNoHpvv1yNEj",
    "5feRexEkgDbbVishdSfEe2jDjWiGfFJRksymQiFhTVrm",
    "5o9TUc1XFGqW1w3jQG5AhJzh7DapiZXpWEpW2dyHfkSt",
    "5vugtktUaTYAYwpGkNCFnCNyFaxTgEDs2k5K5DFH3yG1",
    "6dcb4HkuHVxF7cpM56ZxcGWzYaBYCj9ruhtn6wGsDWVY",
    "73NHHaSiecie7n169wtEKyNCwmtsasbSu1Sv3uA4Xf9h",
    "7AbUnrE2so55k6fjnQnN6RCAU44jTe61ZHXkdyFKh7Pt",
    "7HGB2AQHKq2uycVXACxFf8TFTac2uXz4o1yRwTGP8U9c",
    "7VbEEWsV53G57CayZxHn7RKqAWfiCRXtuLVRXbc1MezV",
    "7jmN2DapS41S862LsZiTcegkaj3abUDQRx47odoGDUbv",
    "8B11iu3BYBfgrVyUNemucPQasd6Skb4rWF3Arpy7myBN",
    "8LNhQYQMJ6sDdBNqsAwrMunjF49xisBDo4P9GJDZZMz5",
    "8MwMF5sT7ABNGWK5qCVT4dmehr8dRNLRpPavAWKfook2",
    "8TwuYRsUFNRdu52eJncjt2bn3ftSidZfYrJvG2gTLtYJ",
    "8WMg3qXGp91SSRSC7SAcgqbcU1kMQhYcKfUREcvW5bWx",
    "8aqhcS8t6fzfehHrUiQh7CfM4RwNaqjobhApDAJPg3EY",
    "8gqkUgcfWo6mVU3mtg4J4ip9pkAkairzG2ajZ6td6qH",
    "8oP2cxJfjchnGc1Tmrxi2FR2SpuyDVKfobu771Qz6is9",
    "8pDK6mr4vKuaJnoX5Squ5h5TtcDM7z3ddDgAiqLSEqhh",
    "94W8uVezzptUBYpDotni9xiZCVZggrCYyVGGxcywbvKQ",
    "9AM8VP6egb5umnt4Pd89a1UuXGeNYCE6wDjaF3trvGQe",
    "9HSu6b451rgzcVwVeR8WJqkVgo9AKnPkqAu8MFRs9teK",
    "9XwoVwx2u1wanzVqovhg4p7X3w3AfrrGTaeGcXTwM2Vu",
    "9kPNRqHz87hRUuoMMQwgdgjyo8SbAwSuM6LxfqxvLEG1",
    "A8vgGPT2Vg2bQEGwkVVd6J5iJaKUBLxjtraEXt55cYwV",
    "AKBRim8GZt5gDVLW6LSqaLKmry2DB4pdGejFquhzHKEu",
    "AKhs6Ma5UE46oSJG9Ee3Yc1q5eaKh1NDRaKPCzB4F3k",
    "AMaUEPaZazR68iNKqnqWr2LyPUvgT2dfxfYRNszs3tN9",
    "AXz1EpEQQGFZu7cjj5rE4o9bg6DXbY6Y7ZqksQ3uNCHN",
    "AnmyXh7q6RUWJSeoqWqQT89qBP85f7R7XXtrvPSNLMvB",
    "AoCPHHpWkfA6oX1E4Qbc4TAkj9xzAeeVzc9igSNnPD68",
    "AvQEq7XeAEmTmwEP1S3515Hdpw59j584cy8Pgkx3NyEM",
    "AyGisHQnxuYUxvFrof3dqYoA3zZMUdnCiEW22gGXXZgZ",
    "BTANo6EjJ8DT5YzkBetMYe6XsN2M6ZpxvvKQGWYjgTj5",
    "C45YtPvEANHW7L9JzyEvLikmapeHwDA1sZDa3b2s6GNG",
    "CHozUZjKJPyZEFFuNHYyyzKiPKznxKJtYddoJktGmYZF",
    "Cbe2WNXvRwiAeXYaXe2pUwSmrWtjS1rcCY4ULg2F56LR",
    "Cc2RjD1eZBEDAPSVoFfuoN7EGaojYafd6Rf9P8mgB4ze",
    "CpGtMUy8qrLS5FE2o4TbryBrudJWPWyqtNxHHE5oWh3M",
    "CvLHr9x7owfXphpPHD5bEtAcN8XSnH2p8txmA7KkTFtB",
    "CxioaCiLLNAFTzwzmfkpjQzWZAU54cw8QVULyisA6ac5",
    "DRYiD3EGEuAabMEZyarCPw4Gu82aE91ACXcrB3CnAngX",
    "DfMxre4cKmvogbLrPigxmibVTTQDuzjdXojWzjCXXhzj",
    "DqqvEiiB73n1zXULMtUpWkFjgKUQ5zEfqBibsgReVoqj",
    "Dx7JRqNDLZMDmeg7teTMqPVZLEBNwbfSoiDvqCsAC9Q3",
    "ED8CQcdPeKxC6n1C1JQRJ3xb8ecfcNiJxuJC52VdcfDc",
    "EfiKUQy9xwiYJefSHLUf4HnpJaWtgGsYwFYEWkw5WDRS",
    "ExTQPNF2qy3QZ9k7zb8kHW3BDcdwt4X2YKz8CdQ8nUQt",
    "FGJbdqDXEunwjBEVEe7ArwYXHUoP28osKg3HcghR9psc",
    "FMMH9G26MGHFZT5f9CUHtwVpHX6q9haECVPPRW28jAj6",
    "FwFiqUShwb9ELPa6cYWbgndGeyRNFJnPefFJfVyAiG8B",
    "GEcj6McPJ3h23wX4xKUhf1hfM9ifQbz9WGQZuAYKf83d",
    "GW8s7MPgveVFc6RFMDsRQQBQbwbjLrLSCx9YnqubCifS",
    "GZwS1egG815eT3nxYiS6CxMctGBrJnZ6xsnXpHDKzxV",
    "GnbJfiCj1HeWgTPD3yhaHF8HGhAMSvNsG6MxffdT1kz1",
    "H1UsuH1T32cKbdWpnkuYg5DCFfSgxDj4WMLD9jAZPJuB",
    "HSnpqBEmwRxQszRJo9Ynb4agKXR3jm9tSSL2p2WGWiWr",
    "HTSXbAWgqqKb6z4ECV1LDjDvDgiPLY2p8iZg6FaG6Q1y",
    "J6BVWhCT27LSM7dC6QnYdJVCPrZuGd9KzCvYc5HdLm3y",
    "JCdSK7UfBiXxKnrgyrgsz7FuH7Jb5MgmtHvrYQ7EZKvn",
    "y4gQmPKwHGLf4kewUiofYGLA8Tjym2EnXCK1L69WumB",
    "yfoq4qzdg8YANSd4mCuHyftDpij7VjtprkhhRFeTyvx",
    "zR75f9izuddHXrejuibnqaDABN7EHsy8trsfVSfS7Bs",
];

use anyhow::{anyhow, Result};
use chrono::{FixedOffset, Utc};
use clap::{Arg, Command};
use dotenv::dotenv;
use log::{info, warn};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use std::{
    collections::HashMap,
    env, fs,
    fs::OpenOptions,
    io::Write,
    path::{Path, PathBuf},
    str::FromStr,
    time::Duration,
};
use tokio::time;

use solana_bot::token_utils::get_multiple_wallet_token_balances;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging and environment variables
    dotenv().ok();
    env_logger::init();

    // Parse command-line arguments using clap
    let matches = Command::new("Token Hodlers")
        .version("1.0")
        .about("Analyzes token holdings from multiple wallets")
        .arg(
            Arg::new("wallet_file")
                .help("File containing wallet addresses (one per line)")
                .required(true)
                .index(1),
        )
        .arg(
            Arg::new("token_address")
                .help("Token mint address")
                .required(true)
                .index(2),
        )
        .arg(
            Arg::new("monitor")
                .short('m')
                .long("monitor")
                .help("Continuously monitor balances every hour")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("output")
                .short('o')
                .long("output")
                .help("CSV output file (default: <token_address>_balances.csv)")
                .value_name("FILE"),
        )
        .arg(
            Arg::new("interval")
                .short('i')
                .long("interval")
                .help("Monitor interval in seconds (default: 3600)")
                .value_name("SECONDS"),
        )
        .get_matches();

    let wallet_file_path = matches.get_one::<String>("wallet_file").unwrap();
    let token_address = matches.get_one::<String>("token_address").unwrap();
    let monitoring_mode = matches.get_flag("monitor");
    let interval = matches
        .get_one::<String>("interval")
        .map(|i| i.parse::<u64>().unwrap_or(3600))
        .unwrap_or(3600);

    // Parse token address
    let token_mint = Pubkey::from_str(token_address)?;

    // Determine CSV output file
    let csv_file = if let Some(output_file) = matches.get_one::<String>("output") {
        PathBuf::from(output_file)
    } else {
        PathBuf::from(format!("{}_balances.csv", token_address))
    };

    // Initialize Solana RPC client
    let rpc_url =
        env::var("RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
    let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

    // Read wallet addresses from file
    info!("Reading wallet addresses from {}", wallet_file_path);
    let wallet_addresses = read_wallet_addresses(wallet_file_path)?;
    info!("Found {} wallet addresses", wallet_addresses.len());

    // Convert addresses to Pubkeys
    let wallet_pub_keys: Vec<Pubkey> = wallet_addresses
        .iter()
        .filter_map(|addr| match Pubkey::from_str(addr) {
            Ok(pubkey) => Some(pubkey),
            Err(err) => {
                eprintln!("Invalid wallet address {}: {}", addr, err);
                None
            }
        })
        .collect();

    info!(
        "Successfully parsed {} wallet addresses",
        wallet_pub_keys.len()
    );

    // Get token metadata first (once)
    let token_metadata = solana_bot::token_utils::get_token_metadata(&client, &token_mint).await;
    let (symbol, _) = match &token_metadata {
        Ok((symbol, decimals)) => {
            info!("Token Symbol: {}, Decimals: {}", symbol, decimals);
            (symbol.clone(), *decimals)
        }
        Err(e) => {
            warn!("Could not get token metadata: {}", e);
            ("UNKNOWN".to_string(), 0)
        }
    };

    // Initialize the CSV file if monitoring mode is enabled
    if monitoring_mode && !csv_file.exists() {
        let mut file = fs::File::create(&csv_file)?;
        writeln!(
            file,
            "timestamp,total_raw_balance,total_adjusted_balance,symbol"
        )?;
        info!("Created new CSV file at {}", csv_file.display());
    }

    if monitoring_mode {
        info!(
            "Starting monitoring mode for token {} ({})",
            symbol, token_address
        );
        info!("Interval: {} seconds", interval);
        info!("Data will be written to {}", csv_file.display());

        // Main monitoring loop
        let mut interval = time::interval(Duration::from_secs(interval));
        loop {
            interval.tick().await;
            // Use UTC+8 timezone
            let utc_now = Utc::now();
            let utc8_offset = FixedOffset::east_opt(8 * 3600).unwrap();
            let now = utc_now.with_timezone(&utc8_offset);

            match calculate_total_balance(&client, &wallet_pub_keys, &token_mint, &token_metadata)
                .await
            {
                Ok((total_balance, adjusted_balance)) => {
                    // Append to CSV
                    let mut file = OpenOptions::new()
                        .append(true)
                        .create(true)
                        .open(&csv_file)?;

                    writeln!(
                        file,
                        "{},{},{},{}",
                        now.format("%Y-%m-%dT%H:%M:%S"),
                        total_balance,
                        adjusted_balance,
                        symbol
                    )?;

                    // Succinct one-line output with UTC+8 time
                    println!(
                        "[{}] {} 莊家持倉: {:.2}%",
                        now.format("%Y-%m-%d %H:%M:%S"),
                        symbol,
                        adjusted_balance / 1e7
                    );
                }
                Err(e) => {
                    eprintln!("[{}] Error: {}", now.format("%Y-%m-%d %H:%M:%S"), e);
                }
            }
        }
    } else {
        // One-time balance check
        let (total_balance, adjusted_balance) =
            calculate_total_balance(&client, &wallet_pub_keys, &token_mint, &token_metadata)
                .await?;

        // Print results
        println!("Token: {} ({})", symbol, token_address);
        println!(
            "Total supply across all wallets: {} raw units",
            total_balance
        );

        if let Ok((symbol, decimals)) = token_metadata {
            println!("Token Decimals: {}", decimals);
            println!("Total supply (adjusted): {}", adjusted_balance);

            // Find top 10 holders
            let holders = get_top_holders(&client, &wallet_pub_keys, &token_mint, decimals).await?;

            println!("\nTop 10 holders:");
            for (i, (wallet, balance, adjusted)) in holders.iter().take(10).enumerate() {
                println!(
                    "{}. {}: {} ({} {})",
                    i + 1,
                    wallet,
                    balance,
                    adjusted,
                    symbol
                );
            }
        } else {
            // If metadata is not available, just show raw values
            // Find top 10 holders
            let holders = get_top_holders(&client, &wallet_pub_keys, &token_mint, 0).await?;

            println!("\nTop 10 holders:");
            for (i, (wallet, balance, _)) in holders.iter().take(10).enumerate() {
                println!("{}. {}: {}", i + 1, wallet, balance);
            }
        }
    }

    Ok(())
}

async fn calculate_total_balance(
    client: &RpcClient,
    wallet_pub_keys: &[Pubkey],
    token_mint: &Pubkey,
    token_metadata: &Result<(String, u8)>,
) -> Result<(u64, f64)> {
    // Process wallet addresses in chunks to avoid RPC limits
    const CHUNK_SIZE: usize = 100;
    let mut all_balances = HashMap::new();

    for (i, chunk) in wallet_pub_keys.chunks(CHUNK_SIZE).enumerate() {
        info!(
            "Processing chunk {} ({}-{})",
            i + 1,
            i * CHUNK_SIZE,
            i * CHUNK_SIZE + chunk.len()
        );

        match get_multiple_wallet_token_balances(client, chunk, token_mint).await {
            Ok(balances) => {
                all_balances.extend(balances);
            }
            Err(err) => {
                eprintln!("Error getting balances for chunk {}: {}", i + 1, err);
            }
        }
    }

    // Calculate sum of all balances
    let total_balance: u64 = all_balances.values().sum();

    // Calculate adjusted balance if we have decimals
    let adjusted_balance = if let Ok((_, decimals)) = token_metadata {
        let decimal_factor = 10u64.pow(*decimals as u32);
        total_balance as f64 / decimal_factor as f64
    } else {
        total_balance as f64
    };

    Ok((total_balance, adjusted_balance))
}

async fn get_top_holders(
    client: &RpcClient,
    wallet_pub_keys: &[Pubkey],
    token_mint: &Pubkey,
    decimals: u8,
) -> Result<Vec<(Pubkey, u64, f64)>> {
    // Process wallet addresses in chunks to avoid RPC limits
    const CHUNK_SIZE: usize = 100;
    let mut all_balances = HashMap::new();

    for (i, chunk) in wallet_pub_keys.chunks(CHUNK_SIZE).enumerate() {
        info!(
            "Processing chunk {} ({}-{})",
            i + 1,
            i * CHUNK_SIZE,
            i * CHUNK_SIZE + chunk.len()
        );

        match get_multiple_wallet_token_balances(client, chunk, token_mint).await {
            Ok(balances) => {
                all_balances.extend(balances);
            }
            Err(err) => {
                eprintln!("Error getting balances for chunk {}: {}", i + 1, err);
            }
        }
    }

    // Calculate decimal factor
    let decimal_factor = 10u64.pow(decimals as u32);

    // Convert to vector and add adjusted balance
    let mut holders: Vec<(Pubkey, u64, f64)> = all_balances
        .into_iter()
        .filter(|(_, balance)| *balance > 0)
        .map(|(pubkey, balance)| {
            let adjusted = balance as f64 / decimal_factor as f64;
            (pubkey, balance, adjusted)
        })
        .collect();

    // Sort by balance (descending)
    holders.sort_by(|a, b| b.1.cmp(&a.1));

    Ok(holders)
}

fn read_wallet_addresses<P: AsRef<Path>>(file_path: P) -> Result<Vec<String>> {
    let content = fs::read_to_string(file_path)?;

    // Split by newline and filter out empty lines
    let addresses: Vec<String> = content
        .lines()
        .map(|line| line.trim().to_string())
        .filter(|line| !line.is_empty())
        .collect();

    if addresses.is_empty() {
        return Err(anyhow!("No wallet addresses found in the file"));
    }

    Ok(addresses)
}

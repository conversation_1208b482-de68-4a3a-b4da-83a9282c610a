use chrono::Utc;
use log::{error, info};
use serde::Deserialize;
use solana_bot::get_new_raydium_pool_from_tx;
use solana_client::rpc_client::RpcClient;
use std::io::Write;
use std::process::Command;
use std::{collections::HashSet, env};

#[derive(Debug, Deserialize)]
struct Transaction {
    #[serde(rename = "blockTime")]
    block_time: i64,
    #[serde(rename = "txHash")]
    tx_hash: String,
}

#[derive(Debug, Deserialize)]
struct Transactions {
    transactions: Vec<Transaction>,
}

#[derive(Debug, Deserialize)]
struct ApiResponse {
    // success: bool,
    data: Transactions,
}

async fn fetch_transactions() -> Result<ApiResponse, Box<dyn std::error::Error>> {
    let output = Command::new("curl")
        .arg("https://api-v2.solscan.io/v2/account/transaction?address=675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8&page_size=10&instruction=initialize2")
        .arg("-H")
        .arg("accept: application/json, text/plain, */*")
        .arg("-H")
        .arg("accept-language: en-US,en;q=0.9")
        .arg("-H")
        .arg("cache-control: no-cache")
        .arg("-H")
        .arg("origin: https://solscan.io")
        .arg("-H")
        .arg("pragma: no-cache")
        .output()?;

    let text = String::from_utf8(output.stdout)?;
    let response: ApiResponse = serde_json::from_str(&text)?;
    Ok(response)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv::dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();

    let mut seen_txs = HashSet::new();
    let rpc_url = env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
    let rpc_client = RpcClient::new(rpc_url);

    // let mut interval = tokio::time::interval(Duration::from_secs(1));
    loop {
        // interval.tick().await;

        match fetch_transactions().await {
            Ok(response) => {
                for tx in response.data.transactions {
                    if seen_txs.insert(tx.tx_hash.clone()) {
                        let age = Utc::now().timestamp() - tx.block_time;
                        if age > 60 {
                            continue;
                        }
                        info!("New tx detected: {} ({}s ago)", tx.tx_hash, age);

                        if let Some((token_mint, pool_pubkey, is_pump_fun)) =
                            get_new_raydium_pool_from_tx(&rpc_client, tx.tx_hash)?
                        {
                            info!(
                                "New Raydium pool detected!\nMint: {}\nPool: {}\nPumpFun: {}",
                                token_mint, pool_pubkey, is_pump_fun
                            );
                        }
                    }
                }
            }
            Err(e) => error!("Error fetching transactions: {}", e),
        }
    }
}

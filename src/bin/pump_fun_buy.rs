use anyhow::{anyhow, Result};
use clap::Parser;
use dotenv::dotenv;
use log::info;
use solana_bot::{
    config::TradingPool, pump_fun::PumpFunPool, simulate_transaction, wallet::WalletContext, PriceProvider,
};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use std::{env, str::FromStr, sync::Arc};

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// RPC URL (overrides RPC_URL environment variable if provided)
    #[arg(short, long)]
    rpc_url: Option<String>,

    /// Token mint address
    #[arg(short, long)]
    token_mint: String,

    /// Pool address
    #[arg(short, long)]
    pool: String,

    /// Amount of SOL to spend
    #[arg(short, long)]
    amount: f64,

    /// Jito tip account
    #[arg(long, default_value = "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5")]
    jito_tip_account: String,

    /// Jito tip amount in SOL
    #[arg(short = 'j', long, default_value = "0.0001")]
    jito_tip_amount: f64,

    /// Priority fee in micro-lamports
    #[arg(short = 'f', long, default_value = "1000000")]
    priority_fee: u64,

    /// Slippage percentage
    #[arg(short, long, default_value = "5.0")]
    slippage: f64,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenv().ok();
    env_logger::init();

    let args = Args::parse();

    // Parse arguments
    let token_mint = Pubkey::from_str(&args.token_mint)?;
    let pool_pubkey = Pubkey::from_str(&args.pool)?;
    let jito_tip_account = Pubkey::from_str(&args.jito_tip_account)?;

    // Initialize wallet context
    let wallet = Arc::new(WalletContext::new()?);
    info!("Wallet public key: {}", wallet.payer());

    // Get RPC URL from args or environment
    let rpc_url = match args.rpc_url {
        Some(url) => url,
        None => env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?,
    };
    info!("Using RPC URL: {}", rpc_url);

    // Convert SOL amounts to lamports
    let jito_tip_lamports = (args.jito_tip_amount * 1_000_000_000.0) as u64;

    // Create RPC client
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    ));

    // Create PumpFunPool
    let pool = PumpFunPool { token_mint };

    // Get current price
    let price = pool.get_price(&client, &pool_pubkey).await?;
    info!("Current price: {} SOL per token", price);

    // Calculate expected token amount (price is already normalized for decimals)
    let expected_token_amount = (args.amount / price) * 1_000_000.0; // Convert to token units (6 decimals)
    let token_amount = expected_token_amount as u64;

    // For buying, we fix the output token amount and increase input SOL amount as slippage
    let max_sol_amount = ((args.amount * (1.0 + args.slippage / 100.0)) * 1_000_000_000.0) as u64;

    info!(
        "Buying approximately {} tokens for {} SOL",
        expected_token_amount / 1_000_000.0,
        args.amount
    );
    info!(
        "Maximum SOL amount with {}% slippage: {} SOL",
        args.slippage,
        (max_sol_amount as f64) / 1_000_000_000.0
    );

    // Build and send transaction
    let transaction = pool
        .build_swap_transaction(
            &client,
            wallet.signer(),
            &pool_pubkey,
            max_sol_amount, // Maximum SOL to spend (with slippage)
            token_amount,   // Fixed token amount to receive
            &token_mint,
            true, // is_buying
            &jito_tip_account,
            jito_tip_lamports,
            args.priority_fee,
            1_000_000, // self_send_amount
        )
        .await?;

    match simulate_transaction(&client, &transaction) {
        Ok(_) => {
            info!("OK! Transaction simulation successful");
        }
        Err(e) => {
            info!("Failed to simulate transaction: {:?}", e);
            return Err(anyhow!("Failed to simulate transaction: {:?}", e));
        }
    }

    // Send transaction
    let signature = client.send_and_confirm_transaction(&transaction)?;
    info!("Transaction sent! Signature: {}", signature);

    Ok(())
}

use anyhow::{anyhow, Result};
use chrono::Utc;
use dotenv::dotenv;
use log::{debug, error, info, warn};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use solana_bot::{
    analyze_trade, pump_tx::get_first_tx, retry_with_backoff, PoolBuyInfo, PUMP_FUN_PROGRAM,
};
use solana_client::{
    rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient},
    rpc_config::RpcTransactionConfig,
};
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status::{
    option_serializer::OptionSerializer, EncodedTransaction, UiMessage, UiTransactionEncoding,
};
use std::{
    collections::HashMap,
    env,
    io::Write,
    process::Command,
    str::FromStr,
    sync::{Arc, Mutex},
    time::{Duration, SystemTime, UNIX_EPOCH},
};
use tokio::time::sleep;

const MIN_SOL_AMOUNT: u64 = 20_000_000_000; // 30 SOL
const SCAN_INTERVAL: Duration = Duration::from_secs(2);
const MAX_TOKEN_AGE: Duration = Duration::from_secs(600); // 10 minutes
const RECENT_TX_WINDOW: Duration = Duration::from_secs(10); // 10 seconds

#[derive(Debug, Serialize, Deserialize, Clone)]
struct PumpToken {
    address: String,
    name: String,
    symbol: String,
    created_timestamp: i64,
    bonding_curve: Option<String>,
    bonding_curve_token: Option<String>,
    last_balance: Option<u64>,
}

#[derive(Debug, Deserialize)]
struct ApiResponse {
    data: ApiData,
}

#[derive(Debug, Deserialize)]
struct ApiData {
    rank: Vec<PumpToken>,
}

#[derive(Clone)]
struct TokenTracker {
    tokens: Arc<Mutex<HashMap<String, PumpToken>>>,
    client: Arc<RpcClient>,
}

impl TokenTracker {
    fn new(client: Arc<RpcClient>) -> Self {
        Self {
            tokens: Arc::new(Mutex::new(HashMap::new())),
            client,
        }
    }

    async fn fetch_new_tokens(&self) -> Result<()> {
        let url = "https://gmgn.ai/defi/quotation/v1/rank/sol/pump/1h?device_id=0d89bab0-6361-41dd-a41b-88f7e715e19f&client_id=gmgn_web_2025.0215.105604&from_app=gmgn&app_ver=2025.0215.105604&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en&limit=100&orderby=created_timestamp&direction=desc&new_creation=true&max_created=10m";

        let api_response = fetch_api_with_retry::<ApiResponse>(url).await?;
        let mut tokens = self.tokens.lock().unwrap();

        for token in api_response.data.rank {
            if tokens.contains_key(&token.address) {
                continue;
            }
            let age = Utc::now().timestamp() - token.created_timestamp;
            if age > 30 {
                continue;
            }
            debug!(
                "New token found: {}, {} ({}), {}s ago",
                token.symbol, token.name, token.address, age
            );

            // Get the first transaction
            let tx_sig = match get_first_tx(&self.client, &Pubkey::from_str(&token.address)?) {
                Ok(Some(tx_sig)) => tx_sig,
                Ok(None) => continue,
                Err(e) => {
                    error!("Error getting first tx: {}", e);
                    continue;
                }
            };
            let sig = Signature::from_str(&tx_sig)?;
            let tx = retry_with_backoff(3, Duration::from_millis(100), || {
                self.client
                    .get_transaction_with_config(
                        &sig,
                        RpcTransactionConfig {
                            commitment: Some(CommitmentConfig::confirmed()),
                            encoding: Some(UiTransactionEncoding::Json),
                            max_supported_transaction_version: Some(0),
                        },
                    )
                    .map_err(anyhow::Error::from)
            })
            .map_err(|e| {
                error!("Error getting transaction after 3 retries: {}", e);
                e
            })?;

            let tx_json = match tx.transaction.transaction {
                EncodedTransaction::Json(tx) => tx,
                _ => {
                    error!("Unexpected transaction encoding");
                    continue;
                }
            };

            let msg = match tx_json.message {
                UiMessage::Raw(msg) => msg,
                _ => {
                    error!("Unexpected transaction encoding");
                    continue;
                }
            };

            let mut found = false;
            let mut account_keys = msg.account_keys.clone();
            if let Some(meta) = tx.transaction.meta.as_ref() {
                if let OptionSerializer::Some(loaded_addresses) = meta.loaded_addresses.as_ref() {
                    account_keys.extend(loaded_addresses.writable.clone());
                    account_keys.extend(loaded_addresses.readonly.clone());
                }
            }
            for ix in msg.instructions {
                let program_id = Pubkey::from_str(&account_keys[ix.program_id_index as usize])?;
                if program_id.to_string() == PUMP_FUN_PROGRAM && ix.accounts.len() == 14 {
                    if account_keys[ix.accounts[0] as usize].to_string() != token.address {
                        continue;
                    }
                    let mut token = token.clone();
                    token.bonding_curve = Some(account_keys[ix.accounts[2] as usize].to_string());
                    token.bonding_curve_token =
                        Some(account_keys[ix.accounts[3] as usize].to_string());
                    token.last_balance = Some(0);
                    tokens.insert(token.address.clone(), token);
                    found = true;
                    break;
                }
            }
            if !found {
                warn!("Token info not found: {}", token.address);
            }
        }
        Ok(())
    }

    fn remove_old_tokens(&self) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;
        let mut tokens = self.tokens.lock().unwrap();
        tokens.retain(|_, token| now - token.created_timestamp < MAX_TOKEN_AGE.as_secs() as i64);
    }

    async fn monitor_curve_balances(&self) -> Result<Vec<String>> {
        let mut hot_tokens = Vec::new();
        let mut tokens = self.tokens.lock().unwrap();
        let accounts: Vec<_> = tokens
            .values()
            .filter_map(|t| t.bonding_curve.as_ref())
            .map(|addr| {
                Pubkey::from_str(addr)
                    .map_err(|e| {
                        error!("Can't parse bonding curve token address {}: {}", addr, e);
                        e
                    })
                    .unwrap()
            })
            .collect();

        if accounts.is_empty() {
            return Ok(hot_tokens);
        }

        // if accounts.len() > 300 {
        //     warn!("Accounts length: {}", accounts.len());
        // }
        let balances = if accounts.len() > 100 {
            accounts
                .chunks(100)
                .map(|chunk| self.client.get_multiple_accounts(chunk))
                .collect::<Result<Vec<_>, _>>()?
                .into_iter()
                .flatten()
                .collect()
        } else {
            self.client.get_multiple_accounts(&accounts)?
        };

        for (i, balance) in balances.iter().enumerate() {
            if let Some(account) = balance {
                if let Some(token) = tokens.values_mut().nth(i) {
                    let current_balance = account.lamports;
                    if current_balance >= token.last_balance.unwrap_or(0) + MIN_SOL_AMOUNT {
                        hot_tokens.push(token.address.clone());
                        debug!(
                            "Hot token detected: {} ({}). Balance increased from {} to {} SOL",
                            token.symbol,
                            token.address,
                            token.last_balance.unwrap_or(0) as f64 / 1e9,
                            current_balance as f64 / 1e9
                        );
                    }
                    token.last_balance = Some(current_balance);
                }
            }
        }

        Ok(hot_tokens)
    }

    async fn analyze_token_trades(&self, token_mint: &str) -> Result<()> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        let tokens = self.tokens.lock().unwrap();
        if let Some(token) = tokens.get(token_mint) {
            if let Some(curve_account) = &token.bonding_curve {
                let curve_pubkey = Pubkey::from_str(curve_account)?;
                let config = GetConfirmedSignaturesForAddress2Config {
                    before: None,
                    until: None,
                    limit: Some(100),
                    commitment: Some(CommitmentConfig::confirmed()),
                };

                let signatures = self
                    .client
                    .get_signatures_for_address_with_config(&curve_pubkey, config)?;

                for sig in signatures.into_iter().rev() {
                    if now - sig.block_time.unwrap_or(0) > RECENT_TX_WINDOW.as_secs() as i64 {
                        continue;
                    }
                    if let Ok(PoolBuyInfo::PumpFun(buy_info)) =
                        analyze_trade(&self.client, &sig.signature)
                    {
                        if buy_info.sol_amount >= MIN_SOL_AMOUNT {
                            info!(
                                "Large buy detected for {} ({}): {} SOL, pool balance: {} SOL, wallet: {}",
                                token.symbol,
                                token.address,
                                buy_info.sol_amount as f64 / 1e9,
                                buy_info.pool_sol_amount as f64 / 1e9,
                                buy_info.target_wallet,
                            );
                        }
                    }
                }
            }
        }

        Ok(())
    }
}

// Move this function out of the impl block to make it a free function
async fn fetch_api_with_retry<T: DeserializeOwned>(url: &str) -> Result<T> {
    let mut retries = 0;
    let max_retries = 5;

    // Get Cloudflare token for gmgn.ai
    let cf_token = match solana_bot::browser_automation::get_cf_clearance_gmgn().await {
        Ok(token) => token,
        Err(e) => {
            println!("Failed to get Cloudflare token for gmgn.ai: {}", e);
            return Err(anyhow!("Failed to get Cloudflare token"));
        }
    };

    while retries < max_retries {
        let output = Command::new("curl")
            .arg(url)
            .arg("-H")
            .arg("accept: application/json, text/plain, */*")
            .arg("-H")
            .arg("accept-language: en-US,en;q=0.9")
            .arg("-H")
            .arg("cache-control: no-cache")
            .arg("-H")
            .arg("dnt: 1")
            .arg("-H")
            .arg("pragma: no-cache")
            .arg("-H")
            .arg("priority: u=1, i")
            .arg("-H")
            .arg("referer: https://gmgn.ai/eth/token/******************************************")
            .arg("-H")
            .arg("sec-ch-ua: \"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"")
            .arg("-H")
            .arg("sec-ch-ua-mobile: ?0")
            .arg("-H")
            .arg("sec-ch-ua-platform: \"macOS\"")
            .arg("-H")
            .arg("sec-fetch-dest: empty")
            .arg("-H")
            .arg("sec-fetch-mode: cors")
            .arg("-H")
            .arg("sec-fetch-site: same-origin")
            .arg("-H")
            .arg(format!("user-agent: {}", cf_token.user_agent))
            .arg("-b")
            .arg(format!("_ga=GA1.1.2039738004.1741880928; cf_clearance={}; _ga_0XM0LYXGC8=GS1.1.1743928203.89.1.1743929262.0.0.0", cf_token.cf_clearance))
            .output()?;

        if !output.status.success() {
            return Err(anyhow::anyhow!(
                "curl command failed: {}",
                String::from_utf8_lossy(&output.stderr)
            ));
        }

        let text = String::from_utf8(output.stdout)?;
        match serde_json::from_str::<T>(&text) {
            Ok(response) => return Ok(response),
            Err(e) => {
                retries += 1;
                if retries >= max_retries {
                    return Err(anyhow!("Parse error after {} attempts: {}", max_retries, e));
                }
                sleep(Duration::from_millis(500 * (1 << retries))).await;
            }
        }
    }

    Err(anyhow!("Max retries reached"))
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();

    let rpc_url = env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL not set"))?;
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url,
        CommitmentConfig::confirmed(),
    ));

    let tracker = Arc::new(TokenTracker::new(client.clone()));

    // Spawn token monitoring thread
    let tracker_clone = tracker.clone();
    tokio::spawn(async move {
        loop {
            if let Err(e) = tracker_clone.fetch_new_tokens().await {
                error!("Error fetching new tokens: {}", e);
            }
            tracker_clone.remove_old_tokens();
            sleep(SCAN_INTERVAL).await;
        }
    });

    // Main balance monitoring loop
    loop {
        match tracker.monitor_curve_balances().await {
            Ok(hot_tokens) => {
                for token in hot_tokens {
                    let tracker = tracker.clone();
                    let token = token.clone();
                    tokio::spawn(async move {
                        if let Err(e) = tracker.analyze_token_trades(&token).await {
                            error!("Error analyzing trades for {}: {}", token, e);
                        }
                    });
                }
            }
            Err(e) => error!("Error monitoring balances: {}", e),
        }
        sleep(Duration::from_secs(1)).await;
    }
}

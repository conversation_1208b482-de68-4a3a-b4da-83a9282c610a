use anyhow::Result;
use dotenv::dotenv;
use solana_bot::{buy_records::ensure_cache_dir, wallet};
use std::collections::HashSet;
use std::env;
use std::thread::sleep;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::init();

    let args: Vec<String> = env::args().collect();

    if args.len() <= 1 {
        println!("Usage: {} <wallet_address1> [wallet_address2] ...", args[0]);
        return Ok(());
    }

    // Ensure cache directory exists
    ensure_cache_dir()?;

    let mut suspicious_wallets = HashSet::new();

    // Analyze each wallet address provided as argument
    for wallet_address in &args[1..] {
        println!("==== Analyzing wallet: {} ====", wallet_address);

        // Check if wallet is suspicious using the function from wallet.rs
        match wallet::is_suspicious_wallet("sol", wallet_address).await {
            Ok(true) => {
                suspicious_wallets.insert(wallet_address.clone());
            }
            Ok(false) => {
                println!("No suspicious activity found for wallet {}", wallet_address);
            }
            Err(e) => {
                println!("Error checking if wallet is suspicious: {}", e);
            }
        }

        sleep(Duration::from_secs(3));
    }

    // Print summary
    println!("\n==== Analysis Summary ====");
    if suspicious_wallets.is_empty() {
        println!("No suspicious wallets found.");
    } else {
        println!("Found {} suspicious wallets:", suspicious_wallets.len());
        for wallet in suspicious_wallets {
            println!("⚠️ {}", wallet);
        }
    }

    Ok(())
}

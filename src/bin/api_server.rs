// src/bin/api_server.rs
use actix_web::{web, App, HttpServer};
use dotenv::dotenv;
use solana_bot::api::{get_wallets, health_check, send_test_message, update_wallets};
use solana_bot::state::{AppState, RedisState};
use solana_bot::telegram::TelegramBot;
use std::env;
use std::sync::Arc;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv().ok();
    env_logger::init();

    let redis_state = Arc::new(RedisState::new(
        &env::var("REDIS_HOST")?,
        env::var("REDIS_PORT")?.parse()?,
    )?);

    // Initialize Telegram bot if credentials are available
    let telegram_bot = if let (Ok(token), Ok(channel_id)) = (
        env::var("TELEGRAM_BOT_TOKEN"),
        env::var("TELEGRAM_CHANNEL_ID").map(|id| id.parse::<i64>()),
    ) {
        if let Ok(id) = channel_id {
            Some(Arc::new(TelegramBot::new(&token, id)))
        } else {
            None
        }
    } else {
        None
    };

    let app_state = web::Data::new(AppState {
        redis: redis_state,
        telegram_bot,
    });

    HttpServer::new(move || {
        App::new()
            .app_data(app_state.clone())
            .service(get_wallets)
            .service(update_wallets)
            .service(health_check)
            .service(send_test_message)
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await?;

    Ok(())
}

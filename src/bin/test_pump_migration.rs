use anyhow::{anyhow, Result};
use dotenv::dotenv;
use solana_bot::is_pump_fun_migration_tx;
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use std::env;

const MIN_TOKEN_AMOUNT: u64 = 200_000_000_000_000; // 2e14

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::init();

    // Get RPC URL from environment or use default
    let rpc_url =
        env::var("RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
    let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

    // Test transaction signature
    let tx_sig =
        "5vuWnLhFioAb1VnRExkg4et4eADNHHm4cNK5uie5gfDMPB72z8z2gAqEcX9HuHX2QCd6FQ7sm235NR4wvbk4LVzb";

    println!("Testing transaction: {}", tx_sig);

    match is_pump_fun_migration_tx(&client, tx_sig, MIN_TOKEN_AMOUNT).await {
        Ok(Some((token_mint, bonding_curve))) => {
            println!("\n✅ Transaction is a pump fun migration!");
            println!("Token mint: {}", token_mint);
            println!("Bonding curve: {}", bonding_curve);
        }
        Ok(None) => {
            println!("\n❌ Transaction is NOT a pump fun migration");
        }
        Err(e) => {
            println!("\nError analyzing transaction: {:?}", e);
            return Err(anyhow!("Failed to analyze transaction: {}", e));
        }
    }

    Ok(())
}

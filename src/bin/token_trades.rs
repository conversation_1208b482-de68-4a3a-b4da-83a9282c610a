use anyhow::{anyhow, Result};
use chrono::{FixedOffset, NaiveDateTime, TimeZone};
use clap::Parser;
use log::debug;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::process::Command;

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// Token address to analyze
    #[arg(short, long)]
    token: String,

    /// Start time (format: YYYY-MM-DD HH:MM:SS)
    #[arg(short, long)]
    from: String,

    /// End time (format: YYYY-MM-DD HH:MM:SS)
    #[arg(long)]
    to: String,

    /// Minimum USD amount
    #[arg(short, long)]
    min_usd: i64,

    /// Event type (buy or sell)
    #[arg(short, long)]
    event: String,
}

#[derive(Debug, Deserialize)]
struct TradeResponse {
    // code: i32,
    // reason: String,
    // message: String,
    data: TradeData,
}

#[derive(Debug, Deserialize)]
struct TradeData {
    history: Option<Vec<Trade>>,
    next: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
struct Trade {
    maker: String,
    base_amount: String,
    quote_amount: String,
    amount_usd: String,
    timestamp: i64,
    event: String,
    tx_hash: String,
    price_usd: String,
    id: String,
    is_open_or_close: i32,
    token_address: String,
    maker_tags: Vec<String>,
    maker_token_tags: Vec<String>,
    quote_address: String,
    quote_symbol: String,
    total_trade: i32,
    balance: String,
    history_bought_amount: String,
    history_sold_income: String,
    history_sold_amount: String,
    realized_profit: String,
    unrealized_profit: String,
    maker_name: String,
    maker_twitter_username: String,
    maker_twitter_name: String,
    maker_avatar: String,
    maker_ens: String,
}

#[derive(Debug, Serialize)]
struct TradeSummary {
    address: String,
    total_usd: f64,
    total_trades: i32,
    total_profit: f64,
    trades: Vec<Trade>,
}

fn parse_datetime(dt_str: &str) -> Result<i64> {
    // Parse the datetime string in the local format
    let naive_dt = NaiveDateTime::parse_from_str(dt_str, "%Y-%m-%d %H:%M:%S")?;

    // Create a fixed offset for UTC+8 (28800 seconds)
    let tz_offset = FixedOffset::east_opt(8 * 3600).unwrap();
    // Interpret the naive datetime as local time (UTC+8)
    let dt = tz_offset
        .from_local_datetime(&naive_dt)
        .single()
        .ok_or_else(|| anyhow!("Invalid local datetime"))?;

    // Convert to UTC timestamp
    Ok(dt.timestamp())
}

async fn fetch_trades(
    token: &str,
    from_time: i64,
    to_time: i64,
    min_usd: i64,
    event: &str,
    cursor: Option<&str>,
) -> Result<TradeResponse> {
    debug!(
        "Fetching trades for token: {} cursor: {}",
        token,
        cursor.unwrap_or("None")
    );
    let mut url = format!(
        "https://gmgn.ai/vas/api/v1/token_trades/sol/{}?device_id=b117fa99-6839-41fc-80b3-ea223d5af82c&client_id=gmgn_web_20250510-918-0737323&from_app=gmgn&app_ver=20250510-918-0737323&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=zh-TW&fp_did=6f1f6aa8d7d90733fc55f6c3e032a5b7&os=web&limit=50&event={}&maker=&min_amount_usd={}&from={}&to={}",
        token, event, min_usd, from_time, to_time
    );
    // println!("{}", url);

    if let Some(cursor) = cursor {
        if cursor != "" {
            url.push_str(&format!("&cursor={}", cursor));
        }
    }

    let output = Command::new("curl")
        .arg(&url)
        .arg("-H")
        .arg("accept: application/json, text/plain, */*")
        .arg("-H")
        .arg("accept-language: zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-CN;q=0.6")
        .arg("-H")
        .arg("cache-control: no-cache")
        .arg("-H")
        .arg("dnt: 1")
        .arg("-H")
        .arg("pragma: no-cache")
        .arg("-H")
        .arg("priority: u=1, i")
        .arg("-H")
        .arg(format!("referer: https://gmgn.ai/sol/token/MVNsiiWE_{}", token))
        .arg("-H")
        .arg("sec-ch-ua: \"Not.A/Brand\";v=\"99\", \"Chromium\";v=\"136\"")
        .arg("-H")
        .arg("sec-ch-ua-mobile: ?0")
        .arg("-H")
        .arg("sec-ch-ua-platform: \"macOS\"")
        .arg("-H")
        .arg("sec-fetch-dest: empty")
        .arg("-H")
        .arg("sec-fetch-mode: cors")
        .arg("-H")
        .arg("sec-fetch-site: same-origin")
        .arg("-H")
        .arg("user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36")
        .output()?;

    let text = String::from_utf8(output.stdout)?;
    // println!("{}", text);
    match serde_json::from_str::<TradeResponse>(&text) {
        Ok(resp) => Ok(resp),
        Err(e) => {
            let preview = text.chars().take(50).collect::<String>();
            eprintln!(
                "Error parsing response: {}. Response preview: {}",
                e, preview
            );
            Err(anyhow!("Failed to parse API response: {}", e))
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    let args = Args::parse();

    // Validate event type
    if args.event != "buy" && args.event != "sell" {
        return Err(anyhow!("Event type must be either 'buy' or 'sell'"));
    }

    // Parse timestamps
    let from_time = parse_datetime(&args.from)?;
    let to_time = parse_datetime(&args.to)?;

    // Fetch all trades with pagination using empty event parameter
    let mut all_trades = Vec::new();
    let mut next_cursor = None;

    loop {
        let response = fetch_trades(
            &args.token,
            from_time,
            to_time,
            args.min_usd,
            "", // Empty event parameter to get all events
            next_cursor.as_deref(),
        )
        .await?;

        if let Some(history) = response.data.history {
            all_trades.extend(history);
        }

        next_cursor = response.data.next;
        if next_cursor.clone().unwrap_or("".to_string()) == "" {
            break;
        }
    }

    // Sort trades by timestamp in descending order to get the most recent trade first
    all_trades.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

    // Aggregate trades by address
    let mut trade_summaries: HashMap<String, TradeSummary> = HashMap::new();

    for trade in all_trades {
        let amount_usd: f64 = trade.amount_usd.parse().unwrap_or(0.0);
        let amount = if trade.event == "buy" {
            amount_usd
        } else {
            -amount_usd
        };

        // Parse profit values
        let realized_profit: f64 = trade.realized_profit.parse().unwrap_or(0.0);
        let unrealized_profit: f64 = trade.unrealized_profit.parse().unwrap_or(0.0);
        let total_profit = realized_profit + unrealized_profit;

        trade_summaries
            .entry(trade.maker.clone())
            .and_modify(|summary| {
                summary.total_usd += amount;
                summary.total_trades += 1;
                summary.trades.push(trade.clone());
            })
            .or_insert(TradeSummary {
                address: trade.maker.clone(),
                total_usd: amount,
                total_trades: 1,
                total_profit, // Only set profit for the first (most recent) trade
                trades: vec![trade.clone()],
            });
    }

    // Convert to Vec and sort by net flow value
    let mut summaries: Vec<TradeSummary> = trade_summaries.into_values().collect();
    summaries.sort_by(|a, b| b.total_usd.partial_cmp(&a.total_usd).unwrap());

    // Print results
    println!(
        "\nNet flow by wallet (sorted by {}):",
        if args.event == "buy" {
            "buy-sell"
        } else {
            "sell-buy"
        }
    );
    println!(
        "{:<50} {:<15} {:<10} {:<15}",
        "Address", "Net Flow (USD)", "Trades", "Total Profit (USD)"
    );
    println!("{:-<90}", "");

    for summary in summaries {
        println!(
            "{:<50} ${:<14.2} {:<10} ${:<14.2}",
            summary.address, summary.total_usd, summary.total_trades, summary.total_profit
        );
    }

    Ok(())
}

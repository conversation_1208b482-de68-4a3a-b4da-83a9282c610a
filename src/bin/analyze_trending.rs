use anyhow::Result;
use dotenv::dotenv;
use serde::{Deserialize, Serialize};
use solana_bot::{wallet, WalletStore};
use std::collections::{HashMap, HashSet};

#[derive(Debug, Deserialize)]
struct TrendingResponse {
    data: TrendingData,
}

#[derive(Debug, Deserialize)]
struct TrendingData {
    rank_6h: Vec<TrendingToken>,
    rank_24h: Vec<TrendingToken>,
}

#[derive(Debug, Deserialize)]
struct TrendingToken {
    #[allow(dead_code)]
    symbol: String,
    address: String,
    #[allow(dead_code)]
    price: serde_json::Number,
    #[allow(dead_code)]
    market_cap: f64,
    #[allow(dead_code)]
    volume: f64,
}

#[derive(Debug, Serialize, Deserialize)]
struct Trader {
    address: String,
    total_cost: f64,
    profit: f64,
    is_suspicious: bool,
    maker_token_tags: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct TradersResponse {
    code: i32,
    data: TradersData,
}

#[derive(Debug, Serialize, Deserialize)]
struct TradersData {
    list: Vec<Trader>,
}

async fn get_trending_tokens(chain: &str) -> Result<String> {
    let response = solana_bot::browser_automation::get_trending_tokens_with_cache(chain).await?;
    Ok(response)
}

async fn get_multiple_token_traders(
    chain: &str,
    token_addresses: &[String],
) -> Result<HashMap<String, Vec<Trader>>> {
    // Use the Python script to get traders for multiple tokens
    let response_map = solana_bot::browser_automation::get_multiple_token_traders_with_python(
        chain,
        token_addresses,
    )
    .await?;

    // Parse each response
    let mut result = HashMap::new();
    for (token_address, response_text) in response_map {
        match serde_json::from_str::<TradersResponse>(&response_text) {
            Ok(resp) => {
                result.insert(token_address, resp.data.list);
            }
            Err(e) => {
                println!(
                    "Failed to parse token traders response for {}: {}",
                    token_address, e
                );
                println!(
                    "Response: {}",
                    &response_text[..100.min(response_text.len())]
                );
                // Continue with other tokens even if one fails
            }
        }
    }

    Ok(result)
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::init();

    let args: Vec<String> = std::env::args().collect();
    let mut profitable_traders: HashMap<String, HashSet<String>> = HashMap::new();

    if args.len() > 2 {
        // If token addresses are provided as arguments
        println!(
            "Analyzing specific tokens on chain {}: {:?}",
            args[1],
            &args[2..]
        );
        let chain = args[1].clone();
        let profit_traders = profitable_traders
            .entry(chain.clone())
            .or_insert_with(HashSet::new);

        // Get traders for all tokens at once
        let token_addresses: Vec<String> = args[2..].to_vec();
        let traders_map = get_multiple_token_traders(&chain, &token_addresses).await?;

        for (token_address, traders) in traders_map {
            println!("Analyzing token: {}", token_address);

            // Filter traders with total_cost > 300 and profit ratio >= 300%
            let qualified_traders: Vec<_> = traders
                .iter()
                .filter(|t| {
                    t.total_cost > 200.0
                        && (t.profit / t.total_cost) >= 2.0
                        && !t.is_suspicious
                        && !t.maker_token_tags.contains(&"transfer_in".to_string())
                        && !t.maker_token_tags.contains(&"paper_hands".to_string())
                })
                .collect();

            println!("Found {} qualified traders", qualified_traders.len());

            for trader in qualified_traders {
                profit_traders.insert(trader.address.clone());
            }
        }
    } else {
        println!("Fetching trending tokens...");
        // Get trending tokens for both periods in a single call
        let response = get_trending_tokens("solana").await?;
        let trending_response: TrendingResponse = serde_json::from_str(&response)?;

        // Combine tokens from both periods, avoiding duplicates
        let mut seen_addresses = HashSet::new();
        let mut combined_tokens = Vec::new();

        // Process 6h tokens
        for token in trending_response.data.rank_6h {
            if !seen_addresses.contains(&token.address) {
                seen_addresses.insert(token.address.clone());
                combined_tokens.push(token);
            }
        }

        // Process 24h tokens
        for token in trending_response.data.rank_24h {
            if !seen_addresses.contains(&token.address) {
                seen_addresses.insert(token.address.clone());
                combined_tokens.push(token);
            }
        }

        println!("Found {} tokens", combined_tokens.len());

        // Limit to 10 tokens for debugging
        // let tokens_to_analyze = combined_tokens.into_iter().take(10).collect::<Vec<_>>();
        let tokens_to_analyze = combined_tokens;

        // Collect addresses for batch processing
        let token_addresses: Vec<String> = tokens_to_analyze
            .iter()
            .map(|token| token.address.clone())
            .collect();

        // Get traders for all tokens in one batch
        let traders_map = get_multiple_token_traders("solana", &token_addresses).await?;

        // Initialize the profitable_traders entry for solana
        let profit_traders = profitable_traders
            .entry("solana".to_string())
            .or_insert_with(HashSet::new);

        // Print details for each token and track profitable traders
        for token in tokens_to_analyze {
            // println!(
            //     "\nToken: {} ({}), price: {}, volume: {}",
            //     token.symbol, token.address, token.price, token.volume
            // );

            if let Some(traders) = traders_map.get(&token.address) {
                // Filter traders with total_cost > 300 and profit ratio >= 300%
                let qualified_traders: Vec<_> = traders
                    .iter()
                    .filter(|t| {
                        t.total_cost > 200.0
                            && (t.profit / t.total_cost) >= 2.0
                            && !t.is_suspicious
                            && !t.maker_token_tags.contains(&"transfer_in".to_string())
                            && !t.maker_token_tags.contains(&"paper_hands".to_string())
                    })
                    .collect();

                // println!("Found {} qualified traders", qualified_traders.len());

                for trader in qualified_traders {
                    profit_traders.insert(trader.address.clone());
                }
            }
        }
    }

    let mut profitable_traders = profitable_traders
        .get("solana")
        .unwrap_or(&HashSet::new())
        .clone();
    if profitable_traders.is_empty() {
        println!("No profitable traders found");
        return Ok(());
    }
    // println!("\nAnalyzing {} unique traders...", profitable_traders.len());
    let wallet_store = WalletStore::load("tracked_wallets.json")?;
    // for chain in ["sol", "bsc"] {
    for chain in ["sol"] {
        let tracked_pub_keys: HashSet<String> = wallet_store
            .get_chain_wallets(chain)
            .iter()
            .cloned()
            .collect();
        // Remove already tracked wallets from the profitable traders set
        profitable_traders = &profitable_traders - &tracked_pub_keys;
    }

    // Use the batch analysis function
    let all_results = wallet::analyze_sol_wallets_batch(&profitable_traders).await?;
    let (signal_wallets, bot_wallets) = all_results;
    println!("Traders for signal:");
    for trader in &signal_wallets {
        println!("  {}", trader);
    }
    println!("\nTraders for bot:");
    for trader in &bot_wallets {
        println!("  {}", trader);
    }

    Ok(())
}

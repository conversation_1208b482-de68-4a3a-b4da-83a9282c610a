use anyhow::{anyhow, Result};
use chrono::Utc;
use dotenv::dotenv;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{debug, error, info, trace};
use solana_bot::{
    acquire_trading_lock, active_trade::ActiveTrades, get_latest_token_buy_info,
    get_raydium_pool_pubkey_from_tx, PumpFunPool, TradingPool, PUMP_FUN_MIGRATION, WSOL_MINT,
};
use solana_bot::{
    get_latest_trades, get_potential_wallets, get_token_account_balance, process_trade,
    send_tx_via_jito, wallet::WalletContext, wallet_store::WalletStore, PoolBuyInfo,
};
use solana_bot::{get_raydium_pool_implementation, token_utils, RaydiumPoolType, TradeState};
use solana_client::rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient};
use solana_sdk::signature::Signature;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use std::cmp::max;
use std::collections::HashMap;
use std::io::Write;
use std::sync::atomic::AtomicBool;
use std::sync::{Arc, Mutex};
use std::{
    env,
    str::FromStr,
    time::{Duration, SystemTime},
};
use tokio::sync::Semaphore;
use tokio::time::sleep;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();
    info!("Starting Solana smart wallet trading bot...");

    // Initialize wallet and rpc
    let wallet = Arc::new(WalletContext::new()?);
    info!("Wallet public key: {}", wallet.payer());
    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url.to_string(),
        CommitmentConfig::confirmed(),
    ));
    let jito_sdk = Arc::new(JitoJsonRpcSDK::new(
        "https://mainnet.block-engine.jito.wtf/api/v1",
        None,
    ));
    let random_tip_account = jito_sdk.get_random_tip_account().await?;
    let jito_tip_account = Pubkey::from_str(&random_tip_account)?;

    // Shared state
    let active_trades = Arc::new(Mutex::new(ActiveTrades::new()));
    let seen_txs = Arc::new(Mutex::new(HashMap::<String, bool>::new()));
    let my_wallet_trading = Arc::new(AtomicBool::new(false));

    // Thread limiter for buying
    let semaphore = Arc::new(Semaphore::new(5)); // Limit to 5 concurrent trades

    // Main loop for finding trade opportunities
    let mut tracked_wallet_balances: HashMap<String, u64> = HashMap::new();
    let mut interval = tokio::time::interval(Duration::from_millis(3000));
    let mut last_loop_time = Utc::now();
    loop {
        let now = Utc::now();
        debug!(
            "Time since last log: {} ms",
            (now - last_loop_time).num_milliseconds()
        );
        last_loop_time = now;

        interval.tick().await;

        // Load tracked wallets
        let wallet_store = WalletStore::load("tracked_wallets.json")?;
        let tracked_pub_keys: Vec<Pubkey> = wallet_store
            .get_wallets()
            .iter()
            .filter_map(|addr| Pubkey::from_str(addr).ok())
            .collect();

        // Batch get SOL balances for all tracked wallets
        let last_wallet_balances = tracked_wallet_balances.clone();
        let (new_balances, potential_wallets) =
            match get_potential_wallets(&client, &tracked_pub_keys, &last_wallet_balances) {
                Ok((balances, wallets)) => (balances, wallets),
                Err(e) => {
                    error!("Error getting wallet balances: {:?}", e);
                    sleep(Duration::from_secs(1)).await;
                    continue;
                }
            };
        tracked_wallet_balances = new_balances;

        if potential_wallets.is_empty() {
            continue;
        }

        for target_wallet_str in potential_wallets.iter() {
            let target_wallet = Pubkey::from_str(&target_wallet_str).unwrap();

            // Get latest trades
            let trades =
                match get_latest_trades(&client, &target_wallet, &mut seen_txs.lock().unwrap()) {
                    Ok(trades) => trades,
                    Err(_) => continue,
                };

            for trade in trades {
                // Extract basic trade information using the utility function
                let (token_mint, sol_amount, pool_sol_amount, target_bought_amount) =
                    match token_utils::extract_pool_buy_info_details(&trade) {
                        Some(details) => details,
                        None => continue,
                    };

                // Check if buy amount and pool size meet our criteria using the utility function
                if !token_utils::should_process_trade(
                    &token_mint,
                    sol_amount,
                    pool_sol_amount,
                    WSOL_MINT,
                ) {
                    continue;
                }

                // Get token mint pubkey from string
                let token_mint_pubkey = Pubkey::from_str(&token_mint).unwrap();

                // Get token account to check how much they bought
                let token_amount = match token_utils::get_wallet_token_balance(
                    &client,
                    &target_wallet,
                    &token_mint_pubkey,
                ) {
                    Ok(amount) => amount,
                    Err(e) => {
                        error!("Failed to get token account balance: {:?}", e);
                        continue;
                    }
                };

                // Check if we're already trading this token
                let active_trades_locked = active_trades.lock().unwrap();
                let already_trading =
                    active_trades_locked.is_token_trading(&token_mint, &target_wallet.to_string());
                drop(active_trades_locked);

                // Try to acquire a thread slot with 1 second timeout
                let permit = match tokio::time::timeout(
                    Duration::from_secs(1),
                    semaphore.clone().acquire_owned(),
                )
                .await
                {
                    Ok(Ok(permit)) => permit,
                    _ => continue,
                };
                {
                    let mut active_trades_locked = active_trades.lock().unwrap();
                    active_trades_locked.add_wallet_to_token(
                        token_mint.clone(),
                        target_wallet.to_string(),
                        max(target_bought_amount, token_amount),
                    );
                }

                // Clone necessary Arc's for the new thread
                let client = client.clone();
                let wallet = wallet.clone();
                let jito_sdk = jito_sdk.clone();
                let active_trades = active_trades.clone();
                let my_wallet_trading_clone = my_wallet_trading.clone();
                let target_wallet_clone = target_wallet.clone();
                let processing_trade = trade.clone();
                tokio::spawn(async move {
                    let token_mint = match &processing_trade {
                        PoolBuyInfo::Raydium(info) => info.token_mint.clone(),
                        PoolBuyInfo::PumpFun(info) => info.token_mint.clone(),
                        _ => return,
                    };

                    // Execute buy phase
                    if let Some(trade_state) = process_trade(
                        processing_trade,
                        &target_wallet_clone,
                        &client,
                        &jito_sdk,
                        jito_tip_account,
                        &wallet,
                        my_wallet_trading_clone.clone(),
                    )
                    .await
                    {
                        trace!("Received trading state from process_trade");
                        {
                            let mut active_trades_locked = active_trades.lock().unwrap();
                            active_trades_locked.add_my_bought_info(
                                &token_mint,
                                &target_wallet.to_string(),
                                trade_state.amount,
                                trade_state.bought_sol,
                            );
                        }

                        // if there's other thread, no need to create another one so we can just return
                        if already_trading {
                            info!("Already trading token {}, skipping sell phase", token_mint);
                            drop(permit);
                            return;
                        }

                        // Create a separate tokio::spawn with only the data we need
                        // Use Arc clones for shareable data
                        let my_wallet_trading_clone2 = my_wallet_trading_clone.clone();
                        let active_trades_clone = active_trades.clone();
                        execute_sell_phase(
                            trade_state,
                            &client,
                            &wallet,
                            &jito_sdk,
                            jito_tip_account,
                            my_wallet_trading_clone2,
                            target_wallet_clone,
                            active_trades_clone,
                        )
                        .await;
                    } else {
                        // Get token mint string for error message
                        error!("Failed to execute buy phase for {}", token_mint);
                    }
                    {
                        let mut active_trades = active_trades.lock().unwrap();
                        active_trades
                            .remove_wallet_from_token(&token_mint, &target_wallet.to_string());
                    }

                    // Done with trade, release permit
                    drop(permit);
                });
            }
        }
    }
}

async fn execute_sell_phase(
    trade_state: TradeState,
    client: &RpcClient,
    wallet: &WalletContext,
    jito_sdk: &JitoJsonRpcSDK,
    jito_tip_account: Pubkey,
    my_wallet_trading: Arc<AtomicBool>,
    target_wallet: Pubkey,
    active_trades: Arc<Mutex<ActiveTrades>>,
) {
    let token_mint = trade_state.token_mint.clone();
    let mut pool_pubkey = trade_state.pool_pubkey;
    let bought_price = trade_state.bought_price;
    let mut sold_amount: u64 = 0;
    let bought_time = trade_state.bought_time;
    let mut is_pump = trade_state.is_from_pump;
    let mut is_pump_migrating = false;

    info!(
        "Current trade state: wallet: {}, token mint: {}, bought price: {:?} SOL, bought time: {:?}",
        target_wallet.to_string(), token_mint, bought_price, bought_time
    );

    let mut pool_impl: Box<dyn TradingPool> = if is_pump {
        Box::new(PumpFunPool {
            token_mint: Pubkey::from_str(&token_mint).unwrap(),
        })
    } else {
        get_raydium_pool_implementation(&client, &pool_pubkey, &RaydiumPoolType::OpenBook).unwrap()
    };
    let token_mint_pubkey = Pubkey::from_str(&token_mint).unwrap();
    let token_account = get_associated_token_address(&wallet.payer(), &token_mint_pubkey);

    let mut total_sold_sol = 0.0;

    for cnt in 0.. {
        if is_pump && !is_pump_migrating {
            // check if pump is migrating
            let pool = PumpFunPool {
                token_mint: Pubkey::from_str(&token_mint).unwrap(),
            };
            let pool_state = pool.get_pool_state(&client, &pool_pubkey).await;
            if let Err(e) = pool_state {
                error!("Failed to get pool state: {:?}", e);
                sleep(Duration::from_secs(3)).await;
                continue;
            }
            let pool_state = pool_state.unwrap();
            if pool_state.complete {
                is_pump_migrating = true;
                info!("Pump token {} is migrating to new pool!", token_mint);
                sleep(Duration::from_secs(5)).await;
                continue;
            }
        }
        if is_pump && is_pump_migrating {
            // check whether raydium pool is ready
            let mut latest_tx: Option<Signature> = None;
            let start_time = SystemTime::now();
            let mut found_raydium_pool = loop {
                // Check if we've been waiting too long (5 minutes)
                if SystemTime::now().duration_since(start_time).unwrap() > Duration::from_secs(300)
                {
                    error!(
                        "Not getting Raydium pool for {} after 5 minutes",
                        token_mint_pubkey
                    );
                    break false;
                }

                let latest_txs = match client.get_signatures_for_address_with_config(
                    &Pubkey::from_str(PUMP_FUN_MIGRATION).unwrap(),
                    GetConfirmedSignaturesForAddress2Config {
                        before: None,
                        until: latest_tx,
                        limit: Some(3),
                        commitment: Some(CommitmentConfig::confirmed()),
                    },
                ) {
                    Ok(txs) => txs,
                    Err(_) => {
                        sleep(Duration::from_secs(3)).await;
                        continue;
                    }
                };

                if !latest_txs.is_empty() {
                    latest_tx = Some(Signature::from_str(&latest_txs[0].signature).unwrap());
                } else {
                    sleep(Duration::from_secs(3)).await;
                    continue;
                }

                let mut found = false;
                for tx in latest_txs {
                    if let Ok(Some((raydium_pool_pubkey, _pool_wsol_account))) =
                        get_raydium_pool_pubkey_from_tx(&client, tx.signature, &token_mint_pubkey)
                    {
                        pool_pubkey = raydium_pool_pubkey;
                        found = true;
                        break;
                    }
                }
                if found {
                    break true;
                }
            };
            if !found_raydium_pool {
                error!(
                    "Not getting Raydium pool for {} after 5 minutes. Try another way...",
                    token_mint_pubkey
                );
                let buy_info = get_latest_token_buy_info(&client, &token_mint_pubkey).await;
                if let Ok(buy_info) = buy_info {
                    if let PoolBuyInfo::Raydium(info) = buy_info {
                        pool_pubkey = info.pool_pubkey;
                        found_raydium_pool = true;
                    }
                }
            }
            if !found_raydium_pool {
                error!(
                    "Not getting Raydium pool for {} after retry. Give up...",
                    token_mint_pubkey
                );
                break;
            }
            info!(
                "Migration Completed! Raydium pool for {}: {}",
                token_mint_pubkey, pool_pubkey
            );
            is_pump_migrating = false;
            is_pump = false;
            pool_impl =
                get_raydium_pool_implementation(&client, &pool_pubkey, &RaydiumPoolType::OpenBook)
                    .unwrap();
            continue;
        }

        // Get SOL price for USD calculation
        let sol_price = match solana_bot::get_sol_price().await {
            Ok(price) => price,
            Err(e) => {
                error!("Failed to get SOL price: {:?}", e);
                continue;
            }
        };

        // Get the current price of the token
        let current_price = match token_utils::get_current_token_price(
            &client,
            &token_mint_pubkey,
            &pool_pubkey,
            is_pump,
            false,
            sol_price,
        )
        .await
        {
            Ok(price) => price,
            Err(e) => {
                error!("Failed to get token price: {:?}", e);
                continue;
            }
        };

        let price_multiplier = current_price / bought_price;
        let elapsed_since_buy = SystemTime::now()
            .duration_since(bought_time)
            .unwrap_or_default();
        let elapsed = elapsed_since_buy.as_secs();
        if cnt % 100 == 0 {
            info!(
                "Price multiplier of {}: {:.2}x, Time since buy: {}s",
                token_mint, price_multiplier, elapsed,
            );
        }

        let current_balance = get_token_account_balance(&client, &token_account);
        if let Err(e) = current_balance {
            error!("Failed to get token account balance: {:?}", e);
            sleep(Duration::from_secs(1)).await;
            continue;
        }
        let current_balance = current_balance.unwrap();
        if current_balance == 0 {
            // double check to avoid loss
            sleep(Duration::from_secs(5)).await;
            let current_balance = get_token_account_balance(&client, &token_account);
            if let Ok(balance) = current_balance {
                if balance == 0 {
                    info!("Already sold all");
                    break;
                }
            } else {
                sleep(Duration::from_secs(1)).await;
                continue;
            }
        }

        // Get target wallet's token balance and update highest_balance
        let target_token_account = get_associated_token_address(&target_wallet, &token_mint_pubkey);
        let target_balance = match get_token_account_balance(&client, &target_token_account) {
            Ok(balance) => balance,
            Err(e) => {
                error!("Failed to get token account balance: {:?}", e);
                sleep(Duration::from_secs(3)).await;
                continue;
            }
        };

        // Retrieve values from active_trades while holding the lock
        let (target_wallet_highest_balance, my_bought_amount) = {
            let mut active_trades_locked = active_trades.lock().unwrap();
            active_trades_locked.update_highest_balance(
                &token_mint,
                &target_wallet.to_string(),
                target_balance,
            );
            let highest =
                active_trades_locked.get_highest_balance(&token_mint, &target_wallet.to_string());
            let bought =
                active_trades_locked.get_my_bought_amount(&token_mint, &target_wallet.to_string());
            (highest, bought)
        };

        // Calculate target wallet's balance ratio (current/highest)
        let target_balance_ratio = if target_wallet_highest_balance > 0 {
            target_balance as f64 / target_wallet_highest_balance as f64
        } else {
            0.0
        };

        if cnt % 100 == 0 {
            info!(
                "Wallet {} balance ratio of {}: {:.2} (current: {}, highest: {}, my bought: {})",
                target_wallet.to_string(),
                token_mint,
                target_balance_ratio,
                target_balance,
                target_wallet_highest_balance,
                my_bought_amount,
            );
        }

        // Find the next ratio threshold that was crossed
        let target_remaining_ratio = (target_balance_ratio * 10.0 + 0.5).ceil() / 10.0 - 0.1;
        let hold_balance_ratio = 1.0 - (sold_amount as f64 / my_bought_amount as f64);
        if target_remaining_ratio <= hold_balance_ratio - 0.1 || target_remaining_ratio == 0.0 {
            let sell_percentage = (hold_balance_ratio - target_remaining_ratio) as f64;
            info!(
                "Balance ratio drops from {:.1} to {:.1}, selling {:.0}% of our balance",
                hold_balance_ratio,
                target_remaining_ratio,
                sell_percentage * 100.0
            );

            // Calculate sell amount as a percentage of our CURRENT balance
            let mut sell_amount = ((my_bought_amount as f64 * sell_percentage).floor()) as u64;
            if sell_amount as f64 >= current_balance as f64 * 0.995 {
                sell_amount = current_balance;
            }

            info!(
                "Selling {} tokens ({:.1}% of our bought balance {})",
                sell_amount,
                (sell_amount as f64 / my_bought_amount as f64) * 100.0,
                my_bought_amount
            );

            {
                let _guard = acquire_trading_lock(&my_wallet_trading).await;
                let swap_tx = pool_impl
                    .build_swap_transaction(
                        &client,
                        wallet.signer(),
                        &pool_pubkey,
                        sell_amount,
                        0,
                        &token_mint_pubkey,
                        false,
                        &jito_tip_account,
                        400_000,
                        4_000_000,
                        0,
                    )
                    .await;
                let swap_tx = match swap_tx {
                    Ok(tx) => tx,
                    Err(e) => {
                        error!("Error building swap transaction: {:?}", e);
                        continue;
                    }
                };
                let current_sol_balance = client.get_balance(&wallet.payer()).unwrap_or(0);
                if let Ok(Some(uuid)) = send_tx_via_jito(&swap_tx, &jito_sdk).await {
                    info!("Sell order sent! Bundle UUID: {}", uuid);

                    // Wait for the sell to complete
                    let mut attempts = 0;
                    let max_attempts = 30;
                    loop {
                        if let Ok(balance) = client.get_token_account_balance(&token_account) {
                            if let Ok(balance) = balance.amount.parse::<u64>() {
                                if balance < current_balance {
                                    // Update last_balance_ratio_sell to this threshold
                                    sold_amount += sell_amount;
                                    break;
                                }
                            }
                        }
                        attempts += 1;
                        if attempts >= max_attempts {
                            error!(
                                "Sell order did not complete after {} attempts",
                                max_attempts
                            );
                            break;
                        }
                        sleep(Duration::from_secs(1)).await;
                    }
                    let new_sol_balance = client.get_balance(&wallet.payer()).unwrap_or(0);
                    if new_sol_balance > current_sol_balance {
                        let sold_sol = (new_sol_balance - current_sol_balance) as f64 / 1e9;
                        info!("Sold {} and got {:.2} SOL", token_mint, sold_sol,);
                        total_sold_sol += sold_sol;
                    }

                    // If we've sold everything or if ratio is 0, break the loop
                    if target_remaining_ratio == 0.0 || current_balance == sell_amount {
                        break;
                    }
                }
            }

            continue;
        }
        sleep(Duration::from_secs(1)).await;
    }

    // Get total bought SOL amount
    let total_bought_sol = {
        let active_trades_locked = active_trades.lock().unwrap();
        let cost = active_trades_locked.get_total_cost_sol(&token_mint, &target_wallet.to_string());
        cost
    }; // lock is dropped here

    info!(
        "Sold all {} and got {:.2} SOL. Cost {:.2} SOL. Profit: {:.1}%",
        token_mint,
        total_sold_sol,
        total_bought_sol,
        (total_sold_sol / total_bought_sol - 1.0) * 100.0
    );
}

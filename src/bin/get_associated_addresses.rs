use anyhow::{anyhow, Result};
use clap::Parser;
use log::debug;
use solana_bot::wallet_transfer_analyzer::{
    fetch_transfers, get_token_account, should_exclude_account, AccountMetadata, TransferCounter,
};
use std::{
    collections::{HashMap, HashSet},
    time::Duration,
};
use tokio::time::sleep;

#[derive(Parser, Debug)]
#[clap(
    name = "get_associated_addresses",
    about = "Get associated wallet addresses for a given wallet and token"
)]
struct Args {
    /// Wallet address to analyze
    #[clap(long, short = 'w')]
    wallet: String,

    /// Token address to analyze
    #[clap(long, short = 't')]
    token: String,

    /// Number of pages to request (default: 10)
    #[clap(long, short = 'p', default_value = "10")]
    max_pages: usize,

    /// Minimum number of transfers (both in and out) to consider an address as associated
    #[clap(long, short = 'm', default_value = "1")]
    min_transfers: usize,

    /// Set log level (trace, debug, info, warn, error)
    #[clap(long, default_value = "info")]
    log_level: String,

    /// Include wallets with one-way transfers (either in or out)
    #[clap(long, short = 'o')]
    include_one_way: bool,

    /// Minimum number of transfers for one-way relationships (default: 3)
    #[clap(long, default_value = "3")]
    min_one_way_transfers: usize,

    /// Include token creators (don't exclude them in the initial filtering)
    #[clap(long)]
    include_token_creators: bool,
}

/// Analyze transfer activities to identify associated wallet addresses
async fn analyze_transfers(
    wallet: &str,
    token: &str,
    max_pages: usize,
    min_transfers: usize,
    include_one_way: bool,
    min_one_way_transfers: usize,
    include_token_creators: bool,
) -> Result<()> {
    // Initialize HTTP client
    let client = reqwest::Client::builder()
        .timeout(Duration::from_secs(30))
        .build()?;

    // Constants for token addresses
    let sol_token = "So11111111111111111111111111111111111111111";
    let usdc_token = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";

    // Get the token account for the wallet and token
    let token_account = get_token_account(wallet, token)?;
    debug!(
        "Token account for wallet {} and token {}: {}",
        wallet, token, token_account
    );

    // First, fetch transfers without exclusions to identify accounts to exclude
    debug!("Fetching initial transfer data to identify accounts to exclude...");
    let initial_response =
        fetch_transfers(&client, wallet, sol_token, token, usdc_token, &[], &[], 1).await?;

    // Extract accounts to exclude
    let mut exclude_to_addresses: HashSet<String> = HashSet::new();
    let mut exclude_from_addresses: HashSet<String> = HashSet::new();

    for activity in &initial_response.data {
        // For 'in' transfers, check the from_address for exclusion
        if activity.flow == "in" {
            let account_meta = initial_response
                .metadata
                .accounts
                .get(&activity.from_address);
            if should_exclude_account(
                &activity.from_address,
                &account_meta,
                include_token_creators,
            ) {
                exclude_from_addresses.insert(activity.from_address.clone());
                // debug!(
                //     "Excluding from address: {} (tags: {:?})",
                //     activity.from_address,
                //     account_meta.map(|m| m.account_tags.clone().unwrap_or_default())
                // );
            }
        }

        // For 'out' transfers, check the to_address for exclusion
        if activity.flow == "out" {
            let account_meta = initial_response.metadata.accounts.get(&activity.to_address);
            if should_exclude_account(&activity.to_address, &account_meta, include_token_creators) {
                exclude_to_addresses.insert(activity.to_address.clone());
                // debug!(
                //     "Excluding to address: {} (tags: {:?})",
                //     activity.to_address,
                //     account_meta.map(|m| m.account_tags.clone().unwrap_or_default())
                // );
            }
        }
    }

    debug!(
        "Found {} addresses to exclude from 'to' field",
        exclude_to_addresses.len()
    );
    debug!(
        "Found {} addresses to exclude from 'from' field",
        exclude_from_addresses.len()
    );

    // Convert to vec for passing to API
    let exclude_to_vec: Vec<String> = exclude_to_addresses.into_iter().collect();
    let exclude_from_vec: Vec<String> = exclude_from_addresses.into_iter().collect();

    // Now fetch transfers with exclusions for all pages
    let mut all_activities = Vec::new();
    let mut all_accounts: HashMap<String, AccountMetadata> = HashMap::new();

    for page in 1..=max_pages {
        debug!("Fetching page {} of transfer data...", page);
        let response = fetch_transfers(
            &client,
            wallet,
            sol_token,
            token,
            usdc_token,
            &exclude_to_vec,
            &exclude_from_vec,
            page,
        )
        .await?;

        // Store account metadata
        all_accounts.extend(response.metadata.accounts.clone());

        // Add activities to our collection
        all_activities.extend(response.data.clone());

        // If we got less than 100 activities, we've reached the end
        if response.data.len() < 100 {
            debug!("Reached the end of data at page {}", page);
            break;
        }

        // Add a small delay to avoid rate limiting
        sleep(Duration::from_millis(100)).await;
    }

    debug!(
        "Fetched a total of {} transfer activities",
        all_activities.len()
    );

    // Map to count in/out transfers for each counterparty
    let mut counterparty_transfers: HashMap<String, TransferCounter> = HashMap::new();

    // Process all activities to count in/out transfers
    for activity in &all_activities {
        let counterparty = if activity.flow == "in" {
            // For inflow, counterparty is the sender
            activity.from_address.clone()
        } else {
            // For outflow, counterparty is the recipient
            activity.to_address.clone()
        };

        // Skip if counterparty is the wallet itself
        if counterparty == wallet {
            continue;
        }

        // Update the transfer counter
        let counter = counterparty_transfers
            .entry(counterparty)
            .or_insert(TransferCounter {
                in_transfers: 0,
                out_transfers: 0,
            });

        if activity.flow == "in" {
            counter.in_transfers += 1;
        } else {
            counter.out_transfers += 1;
        }
    }

    // Filter wallets based on transfer patterns
    let mut bidirectional_addresses: Vec<(&String, &TransferCounter)> = counterparty_transfers
        .iter()
        .filter(|(address, counter)| {
            let account_info = all_accounts.get(*address);
            let should_exclude =
                should_exclude_account(*address, &account_info, include_token_creators);

            // Include only if it has sufficient transfers in both directions AND is not a program account
            counter.in_transfers >= min_transfers
                && counter.out_transfers >= min_transfers
                && !should_exclude
        })
        .collect();

    // Sort by total transfer count (descending)
    bidirectional_addresses.sort_by(|a, b| {
        let a_total = a.1.in_transfers + a.1.out_transfers;
        let b_total = b.1.in_transfers + b.1.out_transfers;
        b_total.cmp(&a_total)
    });

    // If requested, include wallets with one-way transfers
    if include_one_way {
        // Create a map counting only target token transfers
        let mut token_only_transfers: HashMap<String, TransferCounter> = HashMap::new();

        // Process activities but only for the target token
        for activity in &all_activities {
            // Skip if not the target token
            if activity.token_address != token {
                continue;
            }

            let counterparty = if activity.flow == "in" {
                // For inflow, counterparty is the sender
                activity.from_address.clone()
            } else {
                // For outflow, counterparty is the recipient
                activity.to_address.clone()
            };

            // Skip if counterparty is the wallet itself
            if counterparty == wallet {
                continue;
            }

            // Update the transfer counter
            let counter = token_only_transfers
                .entry(counterparty)
                .or_insert(TransferCounter {
                    in_transfers: 0,
                    out_transfers: 0,
                });

            if activity.flow == "in" {
                counter.in_transfers += 1;
            } else {
                counter.out_transfers += 1;
            }
        }

        // Filter and collect one-way transfers into a vector of owned data
        // to avoid borrow checker issues
        let filtered_addresses: Vec<(String, TransferCounter)> = token_only_transfers
            .iter()
            .filter(|(address, counter)| {
                let account_info = all_accounts.get(*address);
                let should_exclude =
                    should_exclude_account(*address, &account_info, include_token_creators);

                // One-way IN: Has sufficient IN transfers, no/few OUT transfers, and is not a program account
                let is_in_only = counter.in_transfers >= min_one_way_transfers
                    && counter.out_transfers < min_transfers
                    && !should_exclude;

                // One-way OUT: Has sufficient OUT transfers, no/few IN transfers, and is not a program account
                let is_out_only = counter.out_transfers >= min_one_way_transfers
                    && counter.in_transfers < min_transfers
                    && !should_exclude;

                // Include if it's either in-only or out-only
                is_in_only || is_out_only
            })
            .map(|(k, v)| (k.clone(), v.clone()))
            .collect();

        // More debugging for the output
        debug!(
            "After filtering for one-way transfers: Found {} addresses",
            filtered_addresses.len()
        );

        // Sort by total transfer count (descending)
        let mut sorted_addresses = filtered_addresses;
        sorted_addresses.sort_by(|a, b| {
            let a_total = a.1.in_transfers + a.1.out_transfers;
            let b_total = b.1.in_transfers + b.1.out_transfers;
            b_total.cmp(&a_total)
        });

        // Print results for one-way transfers if any were found
        if !sorted_addresses.is_empty() {
            println!("\n=== Associated EOA Wallet Addresses (One-Way Target Token Transfers) ===");
            println!(
                "Found {} addresses with one-way transfers of the target token:",
                sorted_addresses.len()
            );

            println!(
                "{:<44} | {:<5} | {:<5} | {:<8} | {:<10} | {:<}",
                "Address", "IN", "OUT", "Total", "Direction", "Tags/Labels"
            );
            println!(
                "{:-<44}-+-{:-<5}-+-{:-<5}-+-{:-<8}-+-{:-<10}-+-{:-<}",
                "", "", "", "", "", ""
            );

            for (address, counter) in &sorted_addresses {
                let total = counter.in_transfers + counter.out_transfers;
                let account_info = all_accounts.get(address);
                let tags = account_info
                    .and_then(|acc| acc.account_tags.clone())
                    .unwrap_or_default()
                    .join(", ");
                let label = account_info
                    .and_then(|acc| acc.account_label.clone())
                    .unwrap_or_default();

                let display_info = if !tags.is_empty() && !label.is_empty() {
                    format!("{} ({})", label, tags)
                } else if !tags.is_empty() {
                    tags
                } else if !label.is_empty() {
                    label
                } else {
                    "".to_string()
                };

                // Determine direction of transfer
                let direction = if counter.in_transfers >= min_one_way_transfers
                    && counter.out_transfers < min_transfers
                {
                    "IN only"
                } else {
                    "OUT only"
                };

                println!(
                    "{:<44} | {:<5} | {:<5} | {:<8} | {:<10} | {:<}",
                    address,
                    counter.in_transfers,
                    counter.out_transfers,
                    total,
                    direction,
                    display_info
                );
            }
        }
    }

    // Print the results for bidirectional transfers
    println!("\n=== Associated EOA Wallet Addresses (Bidirectional Transfers) ===");
    println!(
        "Found {} associated addresses with bidirectional transfers:",
        bidirectional_addresses.len()
    );

    if !bidirectional_addresses.is_empty() {
        println!(
            "{:<44} | {:<5} | {:<5} | {:<8} | {:<}",
            "Address", "IN", "OUT", "Total", "Tags/Labels"
        );
        println!(
            "{:-<44}-+-{:-<5}-+-{:-<5}-+-{:-<8}-+-{:-<}",
            "", "", "", "", ""
        );

        for (address, counter) in &bidirectional_addresses {
            let total = counter.in_transfers + counter.out_transfers;
            let account_info = all_accounts.get(*address);
            let tags = account_info
                .and_then(|acc| acc.account_tags.clone())
                .unwrap_or_default()
                .join(", ");
            let label = account_info
                .and_then(|acc| acc.account_label.clone())
                .unwrap_or_default();

            let display_info = if !tags.is_empty() && !label.is_empty() {
                format!("{} ({})", label, tags)
            } else if !tags.is_empty() {
                tags
            } else if !label.is_empty() {
                label
            } else {
                "".to_string()
            };

            println!(
                "{:<44} | {:<5} | {:<5} | {:<8} | {:<}",
                address, counter.in_transfers, counter.out_transfers, total, display_info
            );
        }
    }

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let args = Args::parse();

    // Initialize logging based on specified log level
    let log_level = match args.log_level.to_lowercase().as_str() {
        "trace" => log::LevelFilter::Trace,
        "debug" => log::LevelFilter::Debug,
        "info" => log::LevelFilter::Info,
        "warn" => log::LevelFilter::Warn,
        "error" => log::LevelFilter::Error,
        _ => log::LevelFilter::Info,
    };

    env_logger::Builder::new()
        .filter_level(log_level)
        .format_timestamp(None)
        .init();

    debug!(
        "Starting analysis for wallet {} and token {}",
        args.wallet, args.token
    );

    // Analyze transfers to find associated addresses
    match analyze_transfers(
        &args.wallet,
        &args.token,
        args.max_pages,
        args.min_transfers,
        args.include_one_way,
        args.min_one_way_transfers,
        args.include_token_creators,
    )
    .await
    {
        Ok(_) => {
            debug!("Analysis completed successfully");
            Ok(())
        }
        Err(e) => {
            eprintln!("Error analyzing transfers: {}", e);
            Err(anyhow!("Analysis failed: {}", e))
        }
    }
}

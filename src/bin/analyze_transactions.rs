use serde_json::Value;
use std::collections::HashSet;
use std::fs;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let mut seen_txs = HashSet::new();

    for i in 1..=100 {
        let file_path = format!("./data/{}.json", i);

        // Skip if file doesn't exist
        if !std::path::Path::new(&file_path).exists() {
            continue;
        }

        let content = fs::read_to_string(&file_path)?;
        let json: Value = serde_json::from_str(&content)?;

        if let Some(transactions) = json["data"]["transactions"].as_array() {
            for tx in transactions {
                // Get txHash and skip if seen
                let tx_hash = tx["txHash"].as_str().unwrap_or_default();
                if seen_txs.contains(tx_hash) {
                    continue;
                }
                seen_txs.insert(tx_hash.to_string());

                // Check sol_value
                let sol_value = tx["sol_value"].as_str().unwrap_or_default();
                if let Ok(value) = sol_value.parse::<f64>() {
                    if value < 900000000.0 {
                        continue;
                    }
                } else {
                    continue;
                }

                // Check for sell instructions
                let mut has_sell = false;
                if let Some(instructions) = tx["parsedInstruction"].as_array() {
                    for instruction in instructions {
                        if let Some(inst_type) = instruction["type"].as_str() {
                            if inst_type == "sell" {
                                has_sell = true;
                                break;
                            }
                        }
                    }
                }
                if has_sell {
                    continue;
                }

                // Print blockTime and txHash
                if let Some(block_time) = tx["blockTime"].as_i64() {
                    println!("BlockTime: {}, TxHash: {}", block_time, tx_hash);
                }
            }
        }
    }

    Ok(())
}

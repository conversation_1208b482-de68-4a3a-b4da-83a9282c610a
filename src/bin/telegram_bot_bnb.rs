use anyhow::{anyhow, Context, Result};
use dotenv::dotenv;
use log::{error, info, warn};
use solana_bot::{
    bnb_token_monitor::{monitor_bnb_token_balances, monitor_bnb_trades},
    multi_chain_wallet_store::MultiChainWalletStore,
    telegram::TelegramBot,
    token_monitor_utils::{load_bnb_token_monitors, start_bnb_token_monitors_persistence},
    ApiClient, TELEGRAM_BOT,
};
use std::{env, sync::Arc};
use tokio::task::Join<PERSON><PERSON>le;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init_from_env(env_logger::Env::default().default_filter_or("info"));
    info!("Starting BNB Chain Telegram Bot");

    // Load environment variables
    dotenv().ok();

    // Get telegram bot token from environment or exit
    let telegram_token =
        env::var("BNB_TELEGRAM_BOT_TOKEN").context("BNB_TELEGRAM_BOT_TOKEN must be set")?;
    let telegram_channel =
        env::var("BNB_TELEGRAM_CHANNEL_ID").context("BNB_TELEGRAM_CHANNEL_ID must be set")?;
    let telegram_channel_id = telegram_channel
        .parse::<i64>()
        .context("BNB_TELEGRAM_CHANNEL_ID must be a valid integer")?;
    let bnb_rpc_url = env::var("BNB_RPC_URL").context("BNB_RPC_URL must be set")?;

    // Initialize the Telegram bot
    let bot = TelegramBot::new(&telegram_token, telegram_channel_id);

    // Initialize the API client
    let _api_client = ApiClient::new();

    // Set globals
    TELEGRAM_BOT.set(Arc::new(bot.clone())).unwrap();

    // Initialize multi-chain wallet store
    let mut wallet_store = MultiChainWalletStore::load("tracked_wallets_multi.json").unwrap();
    // Ensure BNB chain is set up
    let bnb_store = wallet_store.get_chain_store("bnb");
    info!("Loaded {} BNB wallets", bnb_store.get_wallets().len());

    // Send a startup message
    // let startup_msg =
    //     "🚀 <b>BNB Chain Monitoring Bot Started</b> 🚀\n\nMonitoring BNB token purchases...";
    // if let Err(e) = bot.send_message(startup_msg).await {
    //     warn!("Failed to send startup message: {}", e);
    // }

    info!("BNB bot initialized, connecting to RPC: {}", bnb_rpc_url);

    // Load token monitors from disk
    info!("Loading BNB token monitors from disk");
    if let Err(e) = load_bnb_token_monitors().await {
        warn!("Failed to load BNB token monitors: {}", e);
    }

    // Start persistence task
    info!("Starting BNB token monitor persistence task");
    start_bnb_token_monitors_persistence().await;

    // Start monitoring tasks
    let trade_monitor_task = spawn_trade_monitor(&bnb_rpc_url, bot.clone());
    let balance_monitor_task = spawn_balance_monitor(&bnb_rpc_url);

    info!("All BNB monitoring tasks started");

    // Wait for tasks to complete (they shouldn't unless there's an error)
    tokio::select! {
        result = trade_monitor_task => {
            error!("Trade monitor task exited: {:?}", result);
        }
        result = balance_monitor_task => {
            error!("Balance monitor task exited: {:?}", result);
        }
    }

    // We should only get here if a task fails
    error!("BNB Chain Monitoring tasks exited unexpectedly!");

    // Send an error message to the Telegram channel
    // let error_msg = "❌ <b>BNB Chain Monitoring Error</b> ❌\n\nMonitoring tasks exited unexpectedly. Please check logs and restart the bot.";
    // let _ = bot.send_message(error_msg).await;

    Ok(())
}

// Helper functions to spawn monitoring tasks
fn spawn_trade_monitor(rpc_url: &str, bot: TelegramBot) -> JoinHandle<Result<()>> {
    let rpc_url = rpc_url.to_string();
    tokio::spawn(async move {
        info!("Starting BNB trades monitoring task");
        if let Err(e) = monitor_bnb_trades(rpc_url, &bot).await {
            error!("BNB trades monitoring task failed: {}", e);
            return Err(anyhow!("BNB trades monitoring task failed: {}", e));
        }
        Ok(())
    })
}

fn spawn_balance_monitor(rpc_url: &str) -> JoinHandle<Result<()>> {
    let rpc_url = rpc_url.to_string();
    tokio::spawn(async move {
        info!("Starting BNB token balances monitoring task");
        if let Err(e) = monitor_bnb_token_balances(rpc_url).await {
            error!("BNB token balances monitoring task failed: {}", e);
            return Err(anyhow!("BNB token balances monitoring task failed: {}", e));
        }
        Ok(())
    })
}

use anyhow::Result;
use solana_bot::browser_automation::{
    get_cf_clearance, get_cf_clearance_for_host, get_cf_clearance_gmgn,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Test solscan.io token (default)
    println!("Getting Cloudflare clearance token for solscan.io...");
    let solscan_token = get_cf_clearance().await?;
    println!(
        "Successfully obtained cf_clearance token for solscan.io: {}",
        solscan_token
    );

    // Test gmgn.ai token using the dedicated function
    println!("\nGetting Cloudflare clearance token for gmgn.ai...");
    let gmgn_token = get_cf_clearance_gmgn().await?;
    println!(
        "Successfully obtained cf_clearance token for gmgn.ai: {}",
        gmgn_token
    );

    // Test with a custom host using the generic function
    println!("\nGetting Cloudflare clearance token for custom host (dexscreener.com)...");
    let custom_token = get_cf_clearance_for_host("dexscreener.com").await?;
    println!(
        "Successfully obtained cf_clearance token for custom host: {}",
        custom_token
    );

    Ok(())
}

use anyhow::{anyhow, Result};
use chrono::Utc;
use dotenv::dotenv;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{error, info};
use solana_bot::trade_multi::ActiveTrades;
use solana_bot::{
    acquire_trading_lock, get_latest_token_buy_info, get_raydium_pool_pubkey_from_tx,
    get_sol_price, get_token_info, process_trade_multi, PumpFunPool, TradingPool, BUY_SIGNAL_TIME,
    FOMO_MESSAGE_IDS, LEAST_BUY_COUNT, PUMP_FUN_MIGRATION,
};
use solana_bot::{
    get_latest_trades, get_potential_wallets, get_token_account_balance, send_tx_via_jito,
    telegram::TELEGRAM_BOT, wallet_store::WalletStore, PoolBuyInfo,
};
use solana_bot::{
    get_raydium_pool_implementation, wallet::WalletContext, RaydiumPoolType, TradeState,
};
use solana_bot::{AddSellSignalReq, ApiClient, UpsertBuySignalReq, API_CLIENT};
use solana_client::rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient};
use solana_sdk::signature::Signature;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use std::collections::{HashMap, HashSet};
use std::io::Write;
use std::sync::atomic::AtomicBool;
use std::sync::{Arc, Mutex};
use std::{
    env,
    str::FromStr,
    time::{Duration, SystemTime},
};
use tokio::sync::Semaphore;
use tokio::time::sleep;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::builder()
        .format(|buf, record| {
            writeln!(
                buf,
                "[{}Z {} {}] {}",
                Utc::now().format("%Y-%m-%dT%H:%M:%S"),
                record.level(),
                record.target(),
                record.args()
            )
        })
        .init();
    info!("Starting Solana multi-wallet trading bot...");

    // Initialize Telegram bot
    let telegram_bot = solana_bot::telegram::TelegramBot::new(
        &env::var("TELEGRAM_BOT_TOKEN_2").expect("TELEGRAM_BOT_TOKEN_2 must be set"),
        env::var("TELEGRAM_CHANNEL_ID_2")
            .expect("TELEGRAM_CHANNEL_ID_2 must be set")
            .parse::<i64>()
            .expect("TELEGRAM_CHANNEL_ID_2 must be a valid integer"),
    );
    TELEGRAM_BOT
        .set(Arc::new(telegram_bot))
        .expect("Failed to set Telegram bot");

    // Initialize wallet and rpc
    let wallet = Arc::new(WalletContext::new()?);
    info!("Wallet public key: {}", wallet.payer());
    let rpc_url =
        env::var("RPC_URL").map_err(|_| anyhow!("RPC_URL environment variable not set"))?;
    let client = Arc::new(RpcClient::new_with_commitment(
        rpc_url.to_string(),
        CommitmentConfig::confirmed(),
    ));
    let jito_sdk = Arc::new(JitoJsonRpcSDK::new(
        "https://mainnet.block-engine.jito.wtf/api/v1",
        None,
    ));
    let random_tip_account = jito_sdk.get_random_tip_account().await?;
    let jito_tip_account = Pubkey::from_str(&random_tip_account)?;

    // Shared state
    let active_trades = Arc::new(Mutex::new(ActiveTrades::new()));
    let seen_txs = Arc::new(Mutex::new(HashMap::<String, bool>::new()));
    let my_wallet_trading = Arc::new(AtomicBool::new(false));

    // Thread limiter
    let semaphore = Arc::new(Semaphore::new(10)); // Limit to 10 concurrent trades

    // Main loop for finding trade opportunities
    let mut tracked_wallet_balances: HashMap<String, u64> = HashMap::new();
    let mut interval = tokio::time::interval(Duration::from_secs(5));
    loop {
        interval.tick().await;

        // Load tracked wallets
        let wallet_store = WalletStore::load("tracked_wallets_loose.json")?;
        let tracked_pub_keys: Vec<Pubkey> = wallet_store
            .get_wallets()
            .iter()
            .filter_map(|addr| Pubkey::from_str(addr).ok())
            .collect();

        // Batch get SOL balances for all tracked wallets
        let last_wallet_balances = tracked_wallet_balances.clone();
        let (new_balances, potential_wallets) =
            match get_potential_wallets(&client, &tracked_pub_keys, &last_wallet_balances) {
                Ok((balances, wallets)) => (balances, wallets),
                Err(e) => {
                    error!("Error getting wallet balances: {:?}", e);
                    sleep(Duration::from_secs(1)).await;
                    continue;
                }
            };
        tracked_wallet_balances = new_balances;

        if potential_wallets.is_empty() {
            continue;
        }

        for track_target in potential_wallets.iter() {
            let target_wallet = Pubkey::from_str(&track_target).unwrap();

            // Get latest trades
            let trades =
                match get_latest_trades(&client, &target_wallet, &mut seen_txs.lock().unwrap()) {
                    Ok(trades) => trades,
                    Err(_) => continue,
                };

            for trade in trades {
                // Get token mint from trade info
                let token_mint = match &trade {
                    PoolBuyInfo::Raydium(info) => info.token_mint.to_string(),
                    PoolBuyInfo::PumpFun(info) => info.token_mint.to_string(),
                    _ => continue,
                };

                // Get token amount from trade info
                let token_amount = match &trade {
                    PoolBuyInfo::Raydium(info) => info.token_amount,
                    PoolBuyInfo::PumpFun(info) => info.token_amount,
                    _ => continue,
                };

                // Record this buy in active_trades
                let tracked_wallets: Vec<String>;
                {
                    let mut active_trades_guard = active_trades.lock().unwrap();

                    // Skip if we're already trading this token
                    if active_trades_guard.is_token_trading(&token_mint) {
                        continue;
                    }

                    active_trades_guard.record_buy(
                        &token_mint,
                        target_wallet.to_string(),
                        token_amount,
                    );

                    // Check if we have enough wallets buying this token
                    let recent_buys = active_trades_guard.get_recent_buys(&token_mint);
                    // let close_buys = active_trades_guard.get_close_buys(&token_mint);

                    // Skip if we don't have at least 3 different wallets buying this token
                    if (recent_buys.len() as u32) < LEAST_BUY_COUNT
                    // && (close_buys.len() as u32) < LEAST_BUY_THRESHOLD
                    {
                        continue;
                    }

                    // Get unique wallets that bought this token
                    let mut unique_wallets = HashSet::new();
                    for buy in &recent_buys {
                        unique_wallets.insert(buy.wallet.clone());
                    }
                    tracked_wallets = unique_wallets.iter().map(|s| s.clone()).collect::<Vec<_>>();

                    info!(
                        "Found {} unique wallets buying token {} in last 15 minutes: {}",
                        tracked_wallets.len(),
                        token_mint,
                        tracked_wallets.join(", ")
                    );
                }

                // Try to acquire a thread slot with 5 second timeout
                if let Ok(permit) =
                    tokio::time::timeout(Duration::from_secs(5), semaphore.clone().acquire_owned())
                        .await
                {
                    if let Ok(permit) = permit {
                        // Clone necessary Arc's for the new thread
                        let client = client.clone();
                        let wallet = wallet.clone();
                        let jito_sdk = jito_sdk.clone();
                        let active_trades = active_trades.clone();
                        let token_mint_clone = token_mint.clone();
                        let my_wallet_trading_clone = my_wallet_trading.clone();

                        // Add token to active trades
                        {
                            let mut active_trades = active_trades.lock().unwrap();
                            if !active_trades
                                .add_trade(token_mint.clone(), target_wallet.to_string())
                            {
                                continue;
                            }
                        }

                        // Spawn a new thread for this trade
                        tokio::spawn(async move {
                            // Execute buy phase with multi-wallet strategy
                            if let Some(trade_state) = process_trade_multi(
                                trade,
                                &client,
                                &jito_sdk,
                                jito_tip_account,
                                &wallet,
                                my_wallet_trading_clone.clone(),
                                &active_trades,
                            )
                            .await
                            {
                                // send signal
                                let price_usd = trade_state.initial_price_usd;
                                if let Some(bot) = TELEGRAM_BOT.get() {
                                    let bot = bot.clone();
                                    let token_mint = token_mint_clone.to_string();
                                    let client = client.clone();
                                    let tracked_wallets = tracked_wallets.clone();
                                    tokio::spawn(async move {
                                        let token_mint_pubkey =
                                            Pubkey::from_str(&token_mint).unwrap();
                                        let symbol =
                                            match get_token_info(&client, &token_mint_pubkey).await
                                            {
                                                Ok((s, _)) => s,
                                                Err(_) => String::new(),
                                            };
                                        match bot
                                            .send_fomo_signal(&token_mint, &symbol, price_usd)
                                            .await
                                        {
                                            Err(e) => {
                                                error!("Failed to send FOMO signal: {:?}", e);
                                            }
                                            Ok(msg) => {
                                                if let Ok(mut message_ids) = FOMO_MESSAGE_IDS.lock()
                                                {
                                                    message_ids
                                                        .insert(token_mint.clone(), msg.id.0);
                                                }
                                                let emit_time = ApiClient::current_timestamp();
                                                if let Ok(mut buy_signal_time) =
                                                    BUY_SIGNAL_TIME.lock()
                                                {
                                                    buy_signal_time
                                                        .insert(token_mint.clone(), emit_time);
                                                }

                                                // Send buy signal to API
                                                let telegram_link = bot.get_message_link(&msg);
                                                let current_balance_ratios = vec![1.0, 1.0, 1.0];
                                                let win_rate = ApiClient::calculate_win_rate(
                                                    &current_balance_ratios,
                                                );
                                                let average_holding =
                                                    current_balance_ratios.iter().sum::<f64>()
                                                        / current_balance_ratios.len() as f64;
                                                let wallet_store =
                                                    WalletStore::load("tracked_wallets_loose.json")
                                                        .unwrap();
                                                let average_win_rate = wallet_store
                                                    .get_average_win_rate(&tracked_wallets);

                                                let buy_req = UpsertBuySignalReq {
                                                    token_address: token_mint.clone(),
                                                    smart_wallet_count: tracked_wallets.len()
                                                        as i32,
                                                    buy_entry_price: price_usd,
                                                    emit_time: ApiClient::current_timestamp(),
                                                    telegram_link,
                                                    win_rate,
                                                    average_holding,
                                                    average_win_rate,
                                                    highest_price: price_usd,
                                                    symbol,
                                                };

                                                tokio::spawn(async move {
                                                    if let Err(e) =
                                                        API_CLIENT.send_buy_signal(buy_req).await
                                                    {
                                                        error!("Failed to send buy signal to API: {:?}", e);
                                                    }
                                                });
                                            }
                                        }
                                    });
                                }
                                sleep(Duration::from_secs(1)).await;
                                // Execute sell phase with new multi-wallet strategy
                                execute_sell_phase_multi(
                                    trade_state,
                                    tracked_wallets.clone(),
                                    client.clone(),
                                    &wallet,
                                    &jito_sdk,
                                    jito_tip_account,
                                    my_wallet_trading_clone,
                                )
                                .await;
                            }

                            // Cleanup
                            {
                                let mut active_trades = active_trades.lock().unwrap();
                                active_trades.remove_trade(&token_mint_clone);
                            }
                            // Explicitly release the thread slot
                            drop(permit);
                            info!("Trade thread completed for token: {}", token_mint_clone);
                        });
                    }
                }
            }
        }
    }
}

async fn execute_sell_phase_multi(
    trade_state: TradeState,
    tracked_wallets: Vec<String>,
    client: Arc<RpcClient>,
    wallet: &WalletContext,
    jito_sdk: &JitoJsonRpcSDK,
    jito_tip_account: Pubkey,
    my_wallet_trading: Arc<AtomicBool>,
) {
    let token_mint = trade_state.token_mint.clone();
    let mut pool_pubkey = trade_state.pool_pubkey;
    let bought_price = trade_state.bought_price;
    let bought_amount = trade_state.amount;
    let bought_time = trade_state.bought_time;
    let initial_price_usd = trade_state.initial_price_usd;
    let mut is_pump = trade_state.is_from_pump;
    let mut is_pump_migrating = false;
    let mut sell_signal_sent = false;

    info!(
        "Current trade state: token mint: {}, bought price: {:?} SOL, bought time: {:?}",
        token_mint, bought_price, bought_time
    );

    let mut pool_impl: Box<dyn TradingPool> = if is_pump {
        Box::new(PumpFunPool {
            token_mint: Pubkey::from_str(&token_mint).unwrap(),
        })
    } else {
        get_raydium_pool_implementation(&client, &pool_pubkey, &RaydiumPoolType::OpenBook).unwrap()
    };
    let token_mint_pubkey = Pubkey::from_str(&token_mint).unwrap();
    let token_account = get_associated_token_address(&wallet.payer(), &token_mint_pubkey);

    info!(
        "Tracking {} wallets for token {}",
        tracked_wallets.len(),
        token_mint
    );

    let mut total_sold_sol = 0.0;
    let mut wallet_highest_balances = HashMap::new();

    // Initialize highest balances with initial amounts
    for wallet in tracked_wallets.iter() {
        wallet_highest_balances.insert(wallet, 0);
    }

    for cnt in 0.. {
        if is_pump && !is_pump_migrating {
            // check if pump is migrating
            let pool = PumpFunPool {
                token_mint: Pubkey::from_str(&token_mint).unwrap(),
            };
            let pool_state = pool.get_pool_state(&client, &pool_pubkey).await;
            if let Err(e) = pool_state {
                error!("Failed to get pool state: {:?}", e);
                sleep(Duration::from_secs(3)).await;
                continue;
            }
            let pool_state = pool_state.unwrap();
            if pool_state.complete {
                is_pump_migrating = true;
                info!("Pump token {} is migrating to new pool!", token_mint);
                sleep(Duration::from_secs(5)).await;
                continue;
            }
        }
        if is_pump && is_pump_migrating {
            // check whether raydium pool is ready
            let mut latest_tx: Option<Signature> = None;
            let start_time = SystemTime::now();
            let mut found_raydium_pool = loop {
                // Check if we've been waiting too long (5 minutes)
                if SystemTime::now().duration_since(start_time).unwrap() > Duration::from_secs(300)
                {
                    error!(
                        "Not getting Raydium pool for {} after 5 minutes",
                        token_mint_pubkey
                    );
                    break false;
                }

                let latest_txs = match client.get_signatures_for_address_with_config(
                    &Pubkey::from_str(PUMP_FUN_MIGRATION).unwrap(),
                    GetConfirmedSignaturesForAddress2Config {
                        before: None,
                        until: latest_tx,
                        limit: Some(3),
                        commitment: Some(CommitmentConfig::confirmed()),
                    },
                ) {
                    Ok(txs) => txs,
                    Err(_) => {
                        sleep(Duration::from_secs(3)).await;
                        continue;
                    }
                };

                if !latest_txs.is_empty() {
                    latest_tx = Some(Signature::from_str(&latest_txs[0].signature).unwrap());
                } else {
                    sleep(Duration::from_secs(3)).await;
                    continue;
                }

                let mut found = false;
                for tx in latest_txs {
                    if let Ok(Some((raydium_pool_pubkey, _pool_wsol_account))) =
                        get_raydium_pool_pubkey_from_tx(&client, tx.signature, &token_mint_pubkey)
                    {
                        pool_pubkey = raydium_pool_pubkey;
                        found = true;
                        break;
                    }
                }
                if found {
                    break true;
                }
            };
            if !found_raydium_pool {
                error!(
                    "Not getting Raydium pool for {} after 5 minutes. Try another way...",
                    token_mint_pubkey
                );
                let buy_info = get_latest_token_buy_info(&client, &token_mint_pubkey).await;
                if let Ok(buy_info) = buy_info {
                    if let PoolBuyInfo::Raydium(info) = buy_info {
                        pool_pubkey = info.pool_pubkey;
                        found_raydium_pool = true;
                    }
                }
            }
            if !found_raydium_pool {
                error!(
                    "Not getting Raydium pool for {} after retry. Give up...",
                    token_mint_pubkey
                );
                break;
            }
            info!(
                "Migration Completed! Raydium pool for {}: {}",
                token_mint_pubkey, pool_pubkey
            );
            is_pump_migrating = false;
            is_pump = false;
            pool_impl =
                get_raydium_pool_implementation(&client, &pool_pubkey, &RaydiumPoolType::OpenBook)
                    .unwrap();
            continue;
        }

        let current_price = pool_impl.get_price(&client, &pool_pubkey).await;
        if let Err(e) = current_price {
            error!("Failed to get price: {:?}", e);
            sleep(Duration::from_secs(1)).await;
            continue;
        }
        let current_price = current_price.unwrap();
        let price_multiplier = current_price / bought_price;
        let elapsed_since_buy = SystemTime::now()
            .duration_since(bought_time)
            .unwrap_or_default();
        let elapsed = elapsed_since_buy.as_secs();
        if cnt % 100 == 0 {
            info!(
                "Price multiplier of {}: {:.2}x, Time since buy: {}s",
                token_mint, price_multiplier, elapsed,
            );
        }

        let current_balance = get_token_account_balance(&client, &token_account);
        if let Err(e) = current_balance {
            error!("Failed to get token account balance: {:?}", e);
            sleep(Duration::from_secs(1)).await;
            continue;
        }
        let current_balance = current_balance.unwrap();
        if current_balance == 0 {
            // double check to avoid loss
            sleep(Duration::from_secs(5)).await;
            let current_balance = get_token_account_balance(&client, &token_account);
            if let Ok(balance) = current_balance {
                if balance == 0 {
                    info!("Already sold all");
                    break;
                }
            } else {
                sleep(Duration::from_secs(1)).await;
                continue;
            }
        }

        // Get current balance ratios for all tracked wallets
        let mut current_balance_ratios = Vec::new();
        for wallet in tracked_wallets.iter() {
            let wallet_pubkey = Pubkey::from_str(&wallet).unwrap();
            let token_account = get_associated_token_address(&wallet_pubkey, &token_mint_pubkey);
            match get_token_account_balance(&client, &token_account) {
                Ok(balance) => {
                    // Update highest balance if current is higher
                    let highest_balance = wallet_highest_balances.entry(wallet).or_insert(0);
                    if balance > *highest_balance {
                        *highest_balance = balance;
                    }

                    // Calculate balance ratio
                    let balance_ratio = if *highest_balance > 0 {
                        balance as f64 / *highest_balance as f64
                    } else {
                        0.0
                    };

                    current_balance_ratios.push(balance_ratio);

                    if cnt % 100 == 0 {
                        info!(
                            "Wallet {} balance ratio of {}: {:.2}",
                            wallet_pubkey, token_mint, balance_ratio
                        );
                    }
                }
                Err(e) => {
                    error!(
                        "Failed to get token balance of {} for wallet {}: {:?}",
                        token_mint, wallet_pubkey, e
                    );
                    // Assume wallet sold all
                    current_balance_ratios.push(0.0);
                }
            }
        }

        // Calculate the medium of all balance ratios
        let tracked_ratio = calculate_tracked_ratio(current_balance_ratios.clone());

        // Calculate how much token we should hold now
        let hold_ratio = (tracked_ratio / 0.1).round() * 0.1;
        let target_hold_amount = (bought_amount as f64 * hold_ratio) as u64;

        if hold_ratio > 0.3 && cnt % 10 == 0 && !sell_signal_sent {
            // Send updated buy signal to API when ratio changes
            let token_mint_clone = token_mint.clone();
            let current_balance_ratios_clone = current_balance_ratios.clone();

            // Get the original FOMO message ID if it exists
            let msg_id = FOMO_MESSAGE_IDS
                .lock()
                .ok()
                .and_then(|ids| ids.get(&token_mint).copied());
            let emit_time = BUY_SIGNAL_TIME
                .lock()
                .ok()
                .and_then(|times| times.get(&token_mint).copied());
            let tracked_wallets_clone = tracked_wallets.clone();
            tokio::spawn(async move {
                let wallet_store = WalletStore::load("tracked_wallets_loose.json").unwrap();

                // Calculate win rate
                let win_rate = ApiClient::calculate_win_rate(&current_balance_ratios_clone);
                let average_holding = current_balance_ratios_clone.iter().sum::<f64>()
                    / current_balance_ratios_clone.len() as f64;

                // Get telegram link
                let telegram_link =
                    format!("https://t.me/pump_hound_signal/{}", msg_id.unwrap_or(0));

                let average_win_rate = wallet_store.get_average_win_rate(&tracked_wallets_clone);

                // Send buy signal to API
                let buy_req = UpsertBuySignalReq {
                    token_address: token_mint_clone.clone(),
                    smart_wallet_count: current_balance_ratios_clone
                        .iter()
                        .filter(|&r| *r > 0.0)
                        .count() as i32,
                    buy_entry_price: initial_price_usd,
                    emit_time: emit_time.unwrap_or(ApiClient::current_timestamp()),
                    telegram_link,
                    win_rate,
                    average_holding,
                    average_win_rate,
                    highest_price: initial_price_usd,
                    symbol: "".to_string(),
                };

                if let Err(e) = API_CLIENT.send_buy_signal(buy_req).await {
                    error!("Failed to send updated buy signal to API: {:?}", e);
                }
            });
        }

        // If current balance is higher than target, sell the difference
        if current_balance > target_hold_amount {
            let sell_amount = current_balance - target_hold_amount;

            info!(
                "Wallet ratios: {}, tracked ratio: {:.3}, hold ratio: {:.1}, selling {}/{} tokens",
                current_balance_ratios
                    .iter()
                    .map(|r| format!("{:.2}", r))
                    .collect::<Vec<_>>()
                    .join(", "),
                tracked_ratio,
                hold_ratio,
                sell_amount,
                current_balance
            );

            let threshold = (bought_amount as f64 * 0.2) as u64;
            if current_balance > threshold && target_hold_amount <= threshold && !sell_signal_sent {
                // Send the sold signal
                sell_signal_sent = true;
                let token_mint_clone = token_mint.clone();
                let token_mint_pubkey_clone = token_mint_pubkey;
                let pool_pubkey_clone = pool_pubkey;
                let client_clone = client.clone();
                tokio::spawn(async move {
                    if let Err(e) = send_sold_signal_notification(
                        &token_mint_clone,
                        &token_mint_pubkey_clone,
                        pool_pubkey_clone,
                        is_pump,
                        client_clone,
                    )
                    .await
                    {
                        error!("Failed to send sold signal notification: {:?}", e);
                    }
                });
            }

            {
                let _guard = acquire_trading_lock(&my_wallet_trading).await;
                let swap_tx = pool_impl
                    .build_swap_transaction(
                        &client,
                        wallet.signer(),
                        &pool_pubkey,
                        sell_amount,
                        0,
                        &token_mint_pubkey,
                        false,
                        &jito_tip_account,
                        400_000,
                        4_000_000,
                        0,
                    )
                    .await;
                let swap_tx = match swap_tx {
                    Ok(tx) => tx,
                    Err(e) => {
                        error!("Error building swap transaction: {:?}", e);
                        continue;
                    }
                };
                let current_sol_balance = client.get_balance(&wallet.payer()).unwrap_or(0);
                if let Ok(Some(uuid)) = send_tx_via_jito(&swap_tx, &jito_sdk).await {
                    info!("Sell order sent! Bundle UUID: {}", uuid);

                    // Wait for the sell to complete
                    let mut attempts = 0;
                    let max_attempts = 30;
                    loop {
                        if let Ok(balance) = client.get_token_account_balance(&token_account) {
                            if let Ok(balance) = balance.amount.parse::<u64>() {
                                if balance < current_balance {
                                    break;
                                }
                            }
                        }
                        attempts += 1;
                        if attempts >= max_attempts {
                            error!(
                                "Sell order did not complete after {} attempts",
                                max_attempts
                            );
                            break;
                        }
                        sleep(Duration::from_secs(1)).await;
                    }
                    let new_sol_balance = client.get_balance(&wallet.payer()).unwrap_or(0);
                    if new_sol_balance > current_sol_balance {
                        let sold_sol = (new_sol_balance - current_sol_balance) as f64 / 1e9;
                        info!("Sold {} and got {:.2} SOL", token_mint, sold_sol,);
                        total_sold_sol += sold_sol;
                    }
                }
            }

            continue;
        }
        sleep(Duration::from_secs(1)).await;
    }
    info!(
        "Sold all {} and got {:.2} SOL. Cost {:.2} SOL. Profit: {:.1}%",
        token_mint,
        total_sold_sol,
        trade_state.bought_sol,
        (total_sold_sol / trade_state.bought_sol - 1.0) * 100.0
    );
}

/// Sends a notification about a token being sold
async fn send_sold_signal_notification(
    token_mint: &str,
    token_mint_pubkey: &Pubkey,
    pool_pubkey: Pubkey,
    is_pump: bool,
    client: Arc<RpcClient>,
) -> Result<()> {
    if let Some(bot) = TELEGRAM_BOT.get() {
        // Get the original FOMO message ID if it exists
        let reply_to_id = FOMO_MESSAGE_IDS
            .lock()
            .ok()
            .and_then(|ids| ids.get(token_mint).copied());

        let token_info = get_token_info(&client, token_mint_pubkey).await?;
        let (symbol, _) = token_info;

        let pool_impl: Box<dyn TradingPool> = if is_pump {
            Box::new(PumpFunPool {
                token_mint: Pubkey::from_str(&token_mint).unwrap(),
            })
        } else {
            get_raydium_pool_implementation(&client, &pool_pubkey, &RaydiumPoolType::OpenBook)
                .unwrap()
        };

        let price_by_sol = match pool_impl.get_price(&client, &pool_pubkey).await {
            Ok(price) => price,
            Err(e) => {
                error!("Failed to get price: {:?}", e);
                0.0
            }
        };

        let sol_price = match get_sol_price().await {
            Ok(price) => price,
            Err(e) => {
                error!("Failed to get SOL price: {}", e);
                140.0
            }
        };

        let price_usd = price_by_sol * sol_price;
        let msg_result = bot
            .send_sold_signal(token_mint, &symbol, price_usd, 0.0, reply_to_id)
            .await;

        if let Err(e) = &msg_result {
            error!("Failed to send sold signal: {}", e);
        } else if let Ok(msg) = msg_result {
            // Send sell signal to API
            let telegram_link = bot.get_message_link(&msg);

            let sell_req = AddSellSignalReq {
                token_address: token_mint.to_string(),
                telegram_link,
                highest_gain: 0.0,
            };

            if let Err(e) = API_CLIENT.send_sell_signal(sell_req).await {
                error!("Failed to send sell signal to API: {:?}", e);
            }
        }
    }

    Ok(())
}

fn calculate_tracked_ratio(mut numbers: Vec<f64>) -> f64 {
    let len = numbers.len();
    let ratio = match len {
        0 => 0.0,
        1 => numbers[0],
        _ => {
            numbers.sort_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal));
            if len % 2 == 0 {
                (numbers[len / 2 - 1] + numbers[len / 2]) / 2.0
            } else {
                numbers[len / 2]
            }
        }
    };
    ratio * ratio
}

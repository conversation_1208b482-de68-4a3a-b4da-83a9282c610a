use crate::telegram::TelegramBot;
use crate::token_info::{extract_contract_address, format_token_info, get_detailed_token_info};
use crate::ReferralStore;
use log::{error, info};
use once_cell::sync::Lazy;
use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;
use std::str::FromStr;
use std::sync::Arc;
use teloxide::prelude::*;
use teloxide::types::{Message, ParseMode};

// Map of chat IDs to referral codes
pub static REFERRAL_CODES: Lazy<HashMap<i64, &str>> = Lazy::new(|| {
    let mut m = HashMap::new();
    m.insert(-4715859967, "Nwiv9ko"); // Harry: KG PvP
    m.insert(-1002349999299, "Nwiv9<PERSON>"); // Harry: KG PvP
    m.insert(-1001167958195, "OkKtPk8"); // Kordan: crypto father
    m.insert(-1001350470691, "OkKtPk8"); // Kordan: KryptoGO
    m.insert(-4680480670, "kJXEZND"); // Olivia: test
    m
});

// Map of chat IDs to language codes
pub static GROUP_LANGUAGES: Lazy<HashMap<i64, &str>> = Lazy::new(|| {
    let mut m = HashMap::new();
    m.insert(-1001167958195, "zh-tw"); // Kordan: crypto father
    m.insert(-1002349999299, "zh-tw"); // Harry: KG PvP
    m
});

pub static REFERRAL_STORE: Lazy<Arc<tokio::sync::Mutex<ReferralStore>>> =
    Lazy::new(|| Arc::new(tokio::sync::Mutex::new(ReferralStore::new())));

pub fn get_url_with_ref(
    base_url: &str,
    token_address: &str,
    chat_id: ChatId,
    query: &str,
) -> String {
    let mut url = format!("{}/{}/", base_url, token_address);
    url.push_str(
        &(0..3)
            .map(|_| {
                let random_byte = rand::random::<u8>();
                if random_byte % 36 < 26 {
                    (b'a' + (random_byte % 26)) as char
                } else {
                    (b'0' + ((random_byte % 36) - 26)) as char
                }
            })
            .collect::<String>(),
    );

    // Try to get referral code from store first
    if let Ok(store) = REFERRAL_STORE.try_lock() {
        if let Some(inviter_id) = store.get_group_inviter(chat_id.0) {
            if let Some(ref_code) = store.get_referral_code(*inviter_id) {
                return format!("{}?ref={}&{}", url, ref_code, query);
            }
        }
    }

    // Fallback to static referral codes
    if let Some(ref_code) = REFERRAL_CODES.get(&chat_id.0) {
        url = format!("{}?ref={}&{}", url, ref_code, query);
    } else {
        url = format!("{}?{}", url, query);
    }
    url
}

// Handler for /start command
pub async fn handle_start(bot: Bot, msg: Message) -> ResponseResult<()> {
    let welcome_text = r#"Welcome to Pump Hound Bot! 🚀

To get started:
1️⃣ Download KryptoGO wallet from <a href="https://apps.apple.com/app/kryptogo/id1593830910">App Store</a> or <a href="https://play.google.com/store/apps/details?id=com.kryptogo.walletapp">Google Play</a>
2️⃣ Go to the Meme Trading page
3️⃣ Click on the Referral Reward button
4️⃣ Get your referral code

Your referral code lets you earn 20% of transaction fees when your friends trade through your links! The more they trade, the more you earn.

Please enter your referral code (7 characters, alphanumeric) to continue:"#;

    bot.send_message(msg.chat.id, welcome_text)
        .parse_mode(ParseMode::Html)
        .await?;
    Ok(())
}

// Handler for referral code messages
pub async fn handle_referral_code(bot: Bot, msg: Message) -> ResponseResult<()> {
    if let Some(code) = msg.text() {
        let mut store = REFERRAL_STORE.lock().await;
        match store.add_referral(msg.from.as_ref().unwrap().id.0 as i64, code.to_string()) {
            Ok(_) => {
                bot.send_message(msg.chat.id, "✅ Your referral code has been successfully registered! You can now add me to groups and I'll use your referral code for trading links.")
                    .await?;
            }
            Err(_) => {
                bot.send_message(msg.chat.id, "❌ Invalid referral code format. Please enter a 7-character alphanumeric code.")
                    .await?;
            }
        }
    }
    Ok(())
}

// Handler for new chat members
pub async fn handle_new_chat_members(bot: Bot, msg: Message) -> ResponseResult<()> {
    let msg_clone = msg.clone();
    if let Some(new_members) = msg.new_chat_members() {
        for member in new_members {
            if member.is_bot && member.id == bot.get_me().await?.id {
                // Bot was added to a group
                if let Some(ref from) = msg_clone.from {
                    info!(
                        "Bot was added into {} by {}",
                        msg_clone.chat.id.0 as i64, from.id.0 as i64,
                    );
                    let mut store = REFERRAL_STORE.lock().await;
                    if let Err(e) =
                        store.add_group_inviter(msg_clone.chat.id.0 as i64, from.id.0 as i64)
                    {
                        error!("Failed to save group inviter: {:?}", e);
                    }

                    // Send welcome message to group
                    bot.send_message(
                        msg_clone.chat.id,
                        "👋 Hello! I'm Pump Hound Bot. I'll help you track token information and provide trading links. Just send me CA to get started!",
                    )
                    .await?;
                }
            }
        }
    }
    Ok(())
}

pub async fn handle_message(bot: Bot, msg: Message, client: Arc<RpcClient>) -> ResponseResult<()> {
    let chat_id = msg.chat.id;

    // For private chats, check if user has registered their referral code
    if msg.chat.is_private() {
        if let Some(from) = msg.from.as_ref() {
            let store = REFERRAL_STORE.lock().await;
            if store.get_referral_code(from.id.0 as i64).is_none() {
                bot.send_message(
                    chat_id,
                    "⚠️ Please use /start first to register your referral code before using the bot!",
                )
                .await?;
                return Ok(());
            }
        }
    }

    info!(
        "Received message from group: {} (ID: {})",
        msg.chat.title().unwrap_or("Unknown"),
        chat_id
    );

    let text = match msg.text() {
        Some(t) => t,
        None => return Ok(()),
    };

    let contract_address = match extract_contract_address(text) {
        Some(ca) => ca,
        None => return Ok(()),
    };

    let pubkey = match Pubkey::from_str(contract_address) {
        Ok(pk) => pk,
        Err(_) => return Ok(()),
    };

    // Fetch token info
    let token_info = match get_detailed_token_info(&client, &pubkey).await {
        Ok(info) if info.price_usd > 0.0 && info.liquidity > 0.0 => info,
        _ => return Ok(()),
    };

    let query = "utm_source=tg&utm_medium=group&action=buy";
    let query_for_analysis = "utm_source=tg&utm_medium=group&action=buy&openModal=true";

    // Get language for the group
    let lang = GROUP_LANGUAGES.get(&chat_id.0).copied();
    let is_zh_tw = lang == Some("zh-tw");

    let response = format_token_info(&token_info, chat_id, query, get_url_with_ref, lang);

    let keyboard = TelegramBot::generate_keyboard_buttons(
        &token_info.contract_address,
        query,
        Some(query_for_analysis),
        "sol",
        is_zh_tw,
    );

    if let Err(e) = bot
        .send_message(msg.chat.id, response)
        .parse_mode(ParseMode::Html)
        .reply_markup(keyboard)
        .await
    {
        error!("Failed to send message: {:?}", e);
    }

    Ok(())
}

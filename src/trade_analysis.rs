use crate::{retry_with_backoff, PUMP_FUN_AMM_PROGRAM, PUMP_FUN_PROGRAM, RAYDIUM_V4_PROGRAM};
use anyhow::{anyhow, Result};
use log::{debug, error, info, warn};
use solana_client::{
    rpc_client::{GetConfirmedSignaturesForAddress2Config, RpcClient},
    rpc_config::RpcTransactionConfig,
};
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey, signature::Signature};
use solana_transaction_status::{
    option_serializer::OptionSerializer, EncodedTransaction, UiInstruction, UiMessage,
    UiTransactionEncoding, UiTransactionTokenBalance,
};
use std::{
    collections::HashSet,
    str::FromStr,
    thread::sleep,
    time::{Duration, SystemTime},
};

#[derive(Debug, PartialEq, Clone)]
pub enum PoolBuyInfo {
    Raydium(RaydiumBuyInfo),
    PumpFun(PumpFunBuyInfo),
    PumpFunAmm(PumpFunAmmBuyInfo),
    NotBuy,
}

#[derive(Debug, PartialEq, Clone)]
pub struct RaydiumBuyInfo {
    pub token_mint: String,
    pub pool_pubkey: Pubkey,
    pub sol_amount: u64,
    pub token_amount: u64,
    pub pool_sol_amount: u64,
    pub timestamp: SystemTime,
}

#[derive(Debug, PartialEq, Clone)]
pub struct PumpFunBuyInfo {
    pub token_mint: String,
    pub sol_amount: u64,
    pub token_amount: u64,
    pub pool_sol_amount: u64,
    pub timestamp: SystemTime,
    // some pump fun related address
    pub global_state: Pubkey,
    pub fee_recipient: Pubkey,
    pub bonding_curve: Pubkey,
    pub associated_bonding_curve: Pubkey,
    pub event_authority: Pubkey,
    pub target_wallet: Pubkey,
}

#[derive(Debug, PartialEq, Clone)]
pub struct PumpFunAmmBuyInfo {
    pub token_mint: String,
    pub sol_amount: u64,
    pub token_amount: u64,
    pub pool_sol_amount: u64,
    pub timestamp: SystemTime,
    pub pool_pubkey: Pubkey,
    pub fee_recipient: Pubkey,
    pub event_authority: Pubkey,
    pub target_wallet: Pubkey,
}

pub fn analyze_trade(client: &RpcClient, signature: &str) -> Result<PoolBuyInfo> {
    let sig = Signature::from_str(signature)?;
    let tx = retry_with_backoff(3, Duration::from_secs(1), || {
        match client.get_transaction_with_config(
            &sig,
            RpcTransactionConfig {
                commitment: Some(CommitmentConfig::confirmed()),
                encoding: Some(UiTransactionEncoding::Json),
                max_supported_transaction_version: Some(0),
            },
        ) {
            Ok(tx) => Ok(tx),
            Err(e) => Err(anyhow::anyhow!(e)),
        }
    })
    .map_err(|e| {
        warn!("Failed to get transaction after 3 retries: {}", e);
        e
    })?;

    let tx_json = match tx.transaction.transaction {
        EncodedTransaction::Json(tx) => tx,
        _ => return Ok(PoolBuyInfo::NotBuy),
    };

    let msg = match tx_json.message {
        UiMessage::Raw(msg) => msg,
        _ => return Ok(PoolBuyInfo::NotBuy),
    };

    let mut account_keys = msg.account_keys.clone();
    if let Some(meta) = tx.transaction.meta.as_ref() {
        if let OptionSerializer::Some(loaded_addresses) = meta.loaded_addresses.as_ref() {
            account_keys.extend(loaded_addresses.writable.clone());
            account_keys.extend(loaded_addresses.readonly.clone());
        }
    }

    // Get pre and post token balances for the target wallet
    let meta = tx.transaction.meta.clone();
    let meta_for_pre = meta.clone();
    let meta_for_post = meta.clone();
    let pre_token_balances = match meta_for_pre.as_ref() {
        Some(meta) => match &meta.pre_token_balances {
            OptionSerializer::Some(balances) => balances,
            _ => return Ok(PoolBuyInfo::NotBuy),
        },
        None => return Ok(PoolBuyInfo::NotBuy),
    };
    let post_token_balances = match meta_for_post.as_ref() {
        Some(meta) => match &meta.post_token_balances {
            OptionSerializer::Some(balances) => balances,
            _ => return Ok(PoolBuyInfo::NotBuy),
        },
        None => return Ok(PoolBuyInfo::NotBuy),
    };
    let meta = meta.unwrap();

    // Find Raydium instruction
    for (ix_index, ix) in msg.instructions.clone().iter_mut().enumerate() {
        if account_keys[ix.program_id_index as usize] == RAYDIUM_V4_PROGRAM
            && ix.accounts.len() >= 7
        {
            // Extract pool accounts from instruction
            return analyze_raydium_trade(
                &account_keys,
                &ix.accounts,
                pre_token_balances,
                post_token_balances,
                tx.block_time,
            );
        }
        if account_keys[ix.program_id_index as usize] == PUMP_FUN_PROGRAM && ix.accounts.len() == 12
        {
            // Found a Pump.fun instruction
            return analyze_pump_fun_trade(
                &account_keys,
                &ix.accounts,
                &meta.pre_balances,
                &meta.post_balances,
                pre_token_balances,
                post_token_balances,
                tx.block_time,
            );
        }
        if account_keys[ix.program_id_index as usize] == PUMP_FUN_AMM_PROGRAM
            && ix.accounts.len() >= 17
        {
            // Found a Pump.fun instruction
            let decoded_data = bs58::decode(&ix.data).into_vec().unwrap_or_default();
            return analyze_pump_fun_amm_trade(
                &account_keys,
                &ix.accounts,
                &decoded_data,
                pre_token_balances,
                post_token_balances,
                tx.block_time,
            );
        }

        // Check for Pump.fun inner instructions
        if let Some(inner_ixs) = match meta.clone().inner_instructions {
            OptionSerializer::Some(ixs) => Some(ixs),
            _ => None,
        } {
            for inner_ix_group in inner_ixs {
                if inner_ix_group.index != ix_index as u8 {
                    continue;
                }
                for inner_ix in &inner_ix_group.instructions {
                    if let UiInstruction::Compiled(inner_cix) = inner_ix {
                        let program_id_index = inner_cix.program_id_index as usize;
                        let inner_accounts = inner_cix.accounts.clone();
                        if account_keys[program_id_index] == RAYDIUM_V4_PROGRAM {
                            // Found raydium
                            return analyze_raydium_trade(
                                &account_keys,
                                &inner_cix.accounts,
                                pre_token_balances,
                                post_token_balances,
                                tx.block_time,
                            );
                        }
                        if account_keys[program_id_index] == PUMP_FUN_PROGRAM
                            && inner_accounts.len() >= 11
                        {
                            // Found a Pump.fun instruction
                            return analyze_pump_fun_trade(
                                &account_keys,
                                &inner_accounts,
                                &meta.pre_balances,
                                &meta.post_balances,
                                pre_token_balances,
                                post_token_balances,
                                tx.block_time,
                            );
                        }
                        if account_keys[program_id_index] == PUMP_FUN_AMM_PROGRAM
                            && inner_accounts.len() >= 17
                        {
                            // Found a Pump.fun instruction
                            let decoded_data =
                                bs58::decode(&inner_cix.data).into_vec().unwrap_or_default();
                            return analyze_pump_fun_amm_trade(
                                &account_keys,
                                &inner_accounts,
                                &decoded_data,
                                pre_token_balances,
                                post_token_balances,
                                tx.block_time,
                            );
                        }
                    } else {
                        error!("Inner instruction is not compiled!");
                        continue;
                    }
                }
            }
        }
    }
    Ok(PoolBuyInfo::NotBuy)
}

pub fn analyze_raydium_trade(
    account_keys: &Vec<String>,
    account_indices: &Vec<u8>,
    pre_balances: &Vec<UiTransactionTokenBalance>,
    post_balances: &Vec<UiTransactionTokenBalance>,
    block_time: Option<i64>,
) -> Result<PoolBuyInfo> {
    let amm_account = Some(account_keys[account_indices[1] as usize].clone());
    let amm_wsol_idx = if account_indices.len() == 17 {
        account_indices[4] as usize
    } else {
        account_indices[5] as usize
    };
    let amm_token_idx = if account_indices.len() == 17 {
        account_indices[5] as usize
    } else {
        account_indices[6] as usize
    };
    let pool_pubkey = Pubkey::from_str(&amm_account.clone().unwrap())?;

    // Get WSOL balance change
    let wsol_pre_amount = pre_balances
        .iter()
        .find(|b| (b.account_index as u32) == (amm_wsol_idx as u32))
        .map(|b| b.ui_token_amount.amount.parse::<u64>().unwrap_or(0))
        .unwrap_or(0);
    let wsol_post_amount = post_balances
        .iter()
        .find(|b| (b.account_index as u32) == (amm_wsol_idx as u32))
        .map(|b| b.ui_token_amount.amount.parse::<u64>().unwrap_or(0))
        .unwrap_or(0);
    if wsol_pre_amount >= wsol_post_amount {
        // it's sell
        return Ok(PoolBuyInfo::NotBuy);
    }
    let sol_amount = wsol_post_amount.saturating_sub(wsol_pre_amount);

    // Get token balance change from token balances
    let pre_balance = match pre_balances
        .iter()
        .find(|b| (b.account_index as u32) == (amm_token_idx as u32))
    {
        Some(b) => b,
        None => return Ok(PoolBuyInfo::NotBuy),
    };
    let post_balance = match post_balances
        .iter()
        .find(|b| (b.account_index as u32) == (amm_token_idx as u32))
    {
        Some(b) => b,
        None => return Ok(PoolBuyInfo::NotBuy),
    };
    let pre_amount = pre_balance
        .ui_token_amount
        .amount
        .parse::<u64>()
        .unwrap_or(0);
    let post_amount = post_balance
        .ui_token_amount
        .amount
        .parse::<u64>()
        .unwrap_or(0);
    if pre_amount <= post_amount {
        // it's sell
        return Ok(PoolBuyInfo::NotBuy);
    }

    let token_amount = pre_amount.saturating_sub(post_amount);
    let token_mint = pre_balance.mint.clone();
    let timestamp = block_time.map_or_else(SystemTime::now, |block_time| {
        SystemTime::UNIX_EPOCH + Duration::from_secs(block_time as u64)
    });
    Ok(PoolBuyInfo::Raydium(RaydiumBuyInfo {
        token_mint,
        pool_pubkey,
        sol_amount,
        token_amount,
        pool_sol_amount: wsol_post_amount,
        timestamp,
    }))
}

pub fn analyze_pump_fun_trade(
    accounts: &Vec<String>,
    account_indices: &Vec<u8>,
    pre_balances: &Vec<u64>,
    post_balances: &Vec<u64>,
    pre_token_balances: &Vec<UiTransactionTokenBalance>,
    post_token_balances: &Vec<UiTransactionTokenBalance>,
    block_time: Option<i64>,
) -> Result<PoolBuyInfo> {
    debug!("analyze_pump_fun_trade");

    // Extract required accounts
    let global_state = accounts[account_indices[0] as usize].clone();
    let fee_recipient = accounts[account_indices[1] as usize].clone();
    let token_mint = accounts[account_indices[2] as usize].clone();
    let bonding_curve = accounts[account_indices[3] as usize].clone();
    let associated_bonding_curve = accounts[account_indices[4] as usize].clone();
    let target_wallet = accounts[account_indices[6] as usize].clone();
    let event_authority = accounts[account_indices[10] as usize].clone();

    // Calculate token amount from balance changes
    let token_amount = post_token_balances
        .iter()
        .find(|b| b.account_index == account_indices[4])
        .and_then(|post| {
            let post_amount = post.ui_token_amount.amount.parse::<u64>().unwrap_or(0);
            let pre_balance = pre_token_balances
                .iter()
                .find(|b| b.account_index == account_indices[4]);
            match pre_balance {
                Some(pre) => {
                    let pre_amount = pre.ui_token_amount.amount.parse::<u64>().unwrap_or(0);
                    return Some(pre_amount.saturating_sub(post_amount));
                }
                // newly created token
                None => Some(1_000_000_000_000_000 - post_amount),
            }
        })
        .unwrap_or(0);
    if token_amount == 0 {
        return Ok(PoolBuyInfo::NotBuy);
    }

    // Calculate SOL amount from balance changes
    let pre_sol = pre_balances
        .get(account_indices[3] as usize)
        .copied()
        .unwrap_or(0);
    let post_sol = post_balances
        .get(account_indices[3] as usize)
        .copied()
        .unwrap_or(0);
    let sol_amount = post_sol.saturating_sub(pre_sol);

    Ok(PoolBuyInfo::PumpFun(PumpFunBuyInfo {
        token_mint,
        sol_amount,
        token_amount,
        pool_sol_amount: post_sol,
        timestamp: block_time.map_or_else(SystemTime::now, |block_time| {
            SystemTime::UNIX_EPOCH + Duration::from_secs(block_time as u64)
        }),
        global_state: Pubkey::from_str(&global_state)?,
        fee_recipient: Pubkey::from_str(&fee_recipient)?,
        bonding_curve: Pubkey::from_str(&bonding_curve)?,
        associated_bonding_curve: Pubkey::from_str(&associated_bonding_curve)?,
        event_authority: Pubkey::from_str(&event_authority)?,
        target_wallet: Pubkey::from_str(&target_wallet)?,
    }))
}

pub fn analyze_pump_fun_amm_trade(
    accounts: &Vec<String>,
    account_indices: &Vec<u8>,
    decoded_data: &Vec<u8>,
    pre_token_balances: &Vec<UiTransactionTokenBalance>,
    post_token_balances: &Vec<UiTransactionTokenBalance>,
    block_time: Option<i64>,
) -> Result<PoolBuyInfo> {
    if decoded_data.len() != 24 {
        return Ok(PoolBuyInfo::NotBuy);
    }
    if &decoded_data[0..8] != &[0x66, 0x06, 0x3d, 0x12, 0x01, 0xda, 0xeb, 0xea] {
        return Ok(PoolBuyInfo::NotBuy);
    }

    // Extract required accounts
    let fee_recipient = accounts[account_indices[9] as usize].clone();
    let token_mint = accounts[account_indices[3] as usize].clone();
    let pool_pubkey = accounts[account_indices[0] as usize].clone();
    let target_wallet = accounts[account_indices[1] as usize].clone();
    let event_authority = accounts[account_indices[15] as usize].clone();
    let pool_pubkey_token_account_idx = account_indices[7];

    // Calculate token amount from balance changes
    let token_amount = post_token_balances
        .iter()
        .find(|b| b.account_index == pool_pubkey_token_account_idx)
        .and_then(|post| {
            let post_amount = post.ui_token_amount.amount.parse::<u64>().unwrap_or(0);
            let pre_balance = pre_token_balances
                .iter()
                .find(|b| b.account_index == pool_pubkey_token_account_idx);
            match pre_balance {
                Some(pre) => {
                    let pre_amount = pre.ui_token_amount.amount.parse::<u64>().unwrap_or(0);
                    return Some(pre_amount.saturating_sub(post_amount));
                }
                None => Some(0),
            }
        })
        .unwrap_or(0);
    if token_amount == 0 {
        return Ok(PoolBuyInfo::NotBuy);
    }

    // Calculate SOL amount from balance changes
    let pre_sol = pre_token_balances
        .iter()
        .find(|b| b.account_index == account_indices[8])
        .map(|b| b.ui_token_amount.amount.parse::<u64>().unwrap_or(0))
        .unwrap_or(0);
    let post_sol = post_token_balances
        .iter()
        .find(|b| b.account_index == account_indices[8])
        .map(|b| b.ui_token_amount.amount.parse::<u64>().unwrap_or(0))
        .unwrap_or(0);
    let sol_amount = post_sol.saturating_sub(pre_sol);

    Ok(PoolBuyInfo::PumpFunAmm(PumpFunAmmBuyInfo {
        token_mint,
        sol_amount,
        token_amount,
        pool_sol_amount: post_sol,
        timestamp: block_time.map_or_else(SystemTime::now, |block_time| {
            SystemTime::UNIX_EPOCH + Duration::from_secs(block_time as u64)
        }),
        pool_pubkey: Pubkey::from_str(&pool_pubkey)?,
        fee_recipient: Pubkey::from_str(&fee_recipient)?,
        event_authority: Pubkey::from_str(&event_authority)?,
        target_wallet: Pubkey::from_str(&target_wallet)?,
    }))
}

/// Gets the latest buy information for a token, determining whether it's in the Pump.fun stage or Raydium stage
///
/// # Arguments
///
/// * `client` - A reference to the Solana RPC client
/// * `token_mint` - The mint address of the token to check
///
/// # Returns
///
/// * `Result<PoolBuyInfo>` - The latest buy information for the token, or an error if no valid buy information could be found
pub async fn get_latest_token_buy_info(
    client: &RpcClient,
    token_mint: &Pubkey,
) -> Result<PoolBuyInfo> {
    let mut seen_txs = HashSet::new();
    let mut attempts = 0;

    while attempts < 5 {
        let config = GetConfirmedSignaturesForAddress2Config {
            before: None,
            until: None,
            limit: Some(10),
            commitment: Some(CommitmentConfig::confirmed()),
        };

        // Get the 10 latest signatures for the token mint
        let signatures = client.get_signatures_for_address_with_config(token_mint, config)?;

        if signatures.is_empty() {
            info!("No signatures found for token mint {}", token_mint);
            sleep(Duration::from_secs(1));
            attempts += 1;
            continue;
        }

        // Check each signature
        for sig_info in signatures {
            let signature = sig_info.signature.clone();
            if seen_txs.contains(&signature) {
                continue;
            }
            seen_txs.insert(signature.clone());
            if sig_info.err.is_some() {
                continue;
            }
            match analyze_trade(client, &signature) {
                Ok(buy_info) => {
                    if buy_info == PoolBuyInfo::NotBuy {
                        continue;
                    }
                    return Ok(buy_info);
                }
                Err(e) => {
                    warn!("Error analyzing transaction {}: {}", signature, e);
                    continue;
                }
            }
        }

        // If we've checked all signatures and haven't found a valid result, sleep and try again
        attempts += 1;
        if attempts < 5 {
            sleep(Duration::from_secs(3));
        }
    }
    Err(anyhow!(
        "No valid buy info found for token mint {} after 5 attempts",
        token_mint
    ))
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_client::rpc_client::RpcClient;
    use std::env;

    #[test]
    fn test_analyze_pump_fun_trade() {
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let result = analyze_trade(&client, "4tGh3vTKG4kuQ4p5wGbbQiSxEeGpyh1fJz2FKBkg8QxroKGYoRjzRtHjDHYfMuyDKHsEPhA7ZY1fuaoaD4FjwgEE").unwrap();

        // Expected result based on the transaction
        let expected = PoolBuyInfo::PumpFun(PumpFunBuyInfo {
            token_mint: "AiboRysUFvXDZYkR62FrAgnCfXsL6YLk2h1sTimBpump".to_string(),
            sol_amount: 3430693069,
            token_amount: 23846628855244,
            pool_sol_amount: 39789728895,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1737977357),
            global_state: Pubkey::from_str_const("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf"),
            fee_recipient: Pubkey::from_str_const("CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"),
            bonding_curve: Pubkey::from_str_const("EUusJqoj1Gkb5WPHSLkG2Eo6fHFMLfuRSvw24dvYa9ec"),
            associated_bonding_curve: Pubkey::from_str_const(
                "hVSjR7WjhL9ZWvnCg5eShavCTEmXYvAjQP6ekK3D325",
            ),
            event_authority: Pubkey::from_str_const("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1"),
            target_wallet: Pubkey::from_str_const("9yYya3F5EJoLnBNKW6z4bZvyQytMXzDcpU5D6yYr4jqL"),
        });

        assert_eq!(result, expected);
    }

    #[test]
    fn test_analyze_pump_fun_trade_2() {
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let result = analyze_trade(&client, "4ytZiTEB8upuVHgeNFnf285RVUaDg5dUuLiF9Z7m2Mhr3a45BEoLCHvLFpJQmWfgTKzR3h1pKoSDFmdfXLVzS6xX").unwrap();

        // Expected result based on the transaction
        let expected = PoolBuyInfo::PumpFun(PumpFunBuyInfo {
            token_mint: "Ckv1dJwmpdCByAXePavy4ACDmCwd62bRWK1ug331pump".to_string(),
            sol_amount: 4931914069,
            token_amount: 17813725254129,
            pool_sol_amount: 66903492208,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1738071809),
            global_state: Pubkey::from_str_const("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf"),
            fee_recipient: Pubkey::from_str_const("CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"),
            bonding_curve: Pubkey::from_str_const("6tEMHJVKQUDgCAVqy5YuKCahbRP6ryoj4RrMsXP9AWM"),
            associated_bonding_curve: Pubkey::from_str_const(
                "8sZje8Pga5fqCrmHZvSZeoZaMjZmRJH8aEP4rJHvEnbM",
            ),
            event_authority: Pubkey::from_str_const("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1"),
            target_wallet: Pubkey::from_str_const("qVNtxbLw5epZ7Qf2DoGnawMxghv17kfetWWb1tJajf4"),
        });

        assert_eq!(result, expected);
    }

    #[test]
    fn test_analyze_pump_fun_trade_3() {
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let result = analyze_trade(&client, "56CfJRockfSK1CM1uAy2RiCjdcdeNgVLiEo5tYw81r59MPTgYhx8xWUyuwGFZnudrMnVUYBud4V5S32Rr99dvaQc").unwrap();

        // Expected result based on the transaction
        let expected = PoolBuyInfo::PumpFun(PumpFunBuyInfo {
            token_mint: "********************************************".to_string(),
            sol_amount: 48001231920,
            token_amount: 660307692307692,
            pool_sol_amount: 48001231920,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1739610726),
            global_state: Pubkey::from_str_const("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf"),
            fee_recipient: Pubkey::from_str_const("CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"),
            bonding_curve: Pubkey::from_str_const("DchmwLy8YguLYvJusj3zyhmuUgPJP6bKXn5RzRpbNtww"),
            associated_bonding_curve: Pubkey::from_str_const(
                "AwmUCDztSET2uEbjmrtahNWyMVVb5eGhbxD55xSdhyiP",
            ),
            event_authority: Pubkey::from_str_const("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1"),
            target_wallet: Pubkey::from_str_const("7YEw1PKPVhrpF1CCXGsPYaNrZDjGrYTWf5UvEMBFpSCv"),
        });

        assert_eq!(result, expected);
    }

    #[test]
    fn test_analyze_pump_fun_amm_trade() {
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let result = analyze_trade(&client, "5nRjVBbj6N5AB8gAcD3or9dPDqcaUKubJB6B34d7LsongpQS2hVcWRNQTptZPU6YBEYYRBSYJHK5UWUSJXR8oNVU").unwrap();

        // Expected result based on the transaction
        let expected = PoolBuyInfo::PumpFunAmm(PumpFunAmmBuyInfo {
            token_mint: "********************************************".to_string(),
            sol_amount: 348907290,
            token_amount: 846436000368,
            pool_sol_amount: 85228001608,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1742518328),
            fee_recipient: Pubkey::from_str_const("G5UZAVbAf46s7cKWoyKu8kYTip9DGTpbLZ2qa9Aq69dP"),
            event_authority: Pubkey::from_str_const("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR"),
            target_wallet: Pubkey::from_str_const("H5PVX8yJwJRqs6PbbZQtmYrRCVUGaGwgbReDRHYCW6t7"),
            pool_pubkey: Pubkey::from_str_const("DuhMDiHCz2sXxfPvvYW2TFqj2Yk2xeisw4izT8kwxEt2"),
        });

        assert_eq!(result, expected);

        let result = analyze_trade(&client, "4s3y6KnUy4bfggLi98oEMd6SbTNUxQb37uLargrCdH7cfPvxTUVxptzG75ytWyaAz8rFoWic6kuzkkJiMNynTyqn").unwrap();

        // Expected result based on the transaction
        let expected = PoolBuyInfo::PumpFunAmm(PumpFunAmmBuyInfo {
            token_mint: "A26AeYBPRQUiUkrsU9w41GEH2LHAMx3WZ87KtvrQpump".to_string(),
            sol_amount: 297594000,
            token_amount: 424331755729,
            pool_sol_amount: 112050872624,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1742525890),
            fee_recipient: Pubkey::from_str_const("7hTckgnGnLQR6sdH7YkqFTAA7VwTfYFaZ6EhEsU3saCX"),
            event_authority: Pubkey::from_str_const("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR"),
            target_wallet: Pubkey::from_str_const("241eU4DpzwqQcRa97w2YDxh4YP14eS1JfQGCUB7kP48Y"),
            pool_pubkey: Pubkey::from_str_const("D4An3oqBPCA946jKZkaG9Lg66zXYvtA4KxDZvEwdiStL"),
        });

        assert_eq!(result, expected);

        let result = analyze_trade(&client, "2wegzfa8ZQDxEej5kmkp3NXPQzSgMgXGHu6D65RxEffKxrcUrfTjbjzgFBWpD7bM7GUf4oEDpsC9RGAS4wmv7u9g").unwrap();
        let expected = PoolBuyInfo::NotBuy;
        assert_eq!(result, expected);
    }

    #[test]
    fn test_analyze_raydium_trade() {
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let result = analyze_trade(&client, "2AX8pEKoCVRejC83EbyTJM2qUJXMW5Zq21mk1ctvfx3gRPpRZqgixyeLBZw6vb5Ro4m2r2xpr3Twvvx35Sy1c6Gq").unwrap();

        // Expected result based on the transaction - will update after seeing actual values
        let expected = PoolBuyInfo::Raydium(RaydiumBuyInfo {
            token_mint: "6kLdw2Vx3T4Cps1ZLPC32294eZkKozaf4YHsM7cApump".to_string(),
            pool_pubkey: Pubkey::from_str("7APeHhRUxJAjHkdJFS57pqTRJU3GhMNLSEiTYCMfuw2R").unwrap(),
            sol_amount: 21996184,
            token_amount: 91939991408,
            pool_sol_amount: 66858013932,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1738166458),
        });

        assert_eq!(result, expected);
    }

    #[test]
    fn test_analyze_raydium_trade_2() {
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let result = analyze_trade(&client, "2bnT1Rbq251oEn7xUDmwiu17GBknEC9tQ2NvjXhiim95aFBtageYUcj3i9QgGMdNPm3nSGoELoTiAq5duc4N1ukE").unwrap();

        // Expected result based on the transaction - will update after seeing actual values
        let expected = PoolBuyInfo::Raydium(RaydiumBuyInfo {
            token_mint: "6kLdw2Vx3T4Cps1ZLPC32294eZkKozaf4YHsM7cApump".to_string(),
            pool_pubkey: Pubkey::from_str("7APeHhRUxJAjHkdJFS57pqTRJU3GhMNLSEiTYCMfuw2R").unwrap(),
            sol_amount: 990000000,
            token_amount: 10447277289372,
            pool_sol_amount: 42680230636,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1738168390),
        });

        assert_eq!(result, expected);
    }

    #[test]
    fn test_analyze_pump_trade_2() {
        let rpc_url =
            env::var("RPC_URL").unwrap_or("https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new(rpc_url);

        let result =
            analyze_trade(&client, "45x8CESJk68HNJUTQM1282hVRveMufyHLMJzBRzptqgntnnJeRaGK8TES8ZeEFqBCpnLvBbgLkBZzCEeaknQG4Zn").unwrap();

        // Expected result based on the transaction - will update after seeing actual values
        let expected = PoolBuyInfo::PumpFunAmm(PumpFunAmmBuyInfo {
            token_mint: "C21uTiGyYuBVQZ8d95n55kM9K4kgLoJyQkDQxicZpump".to_string(),
            sol_amount: 98901296,
            token_amount: 100069794240,
            pool_sol_amount: 132957849228,
            timestamp: SystemTime::UNIX_EPOCH + Duration::from_secs(1747060534),
            pool_pubkey: Pubkey::from_str_const("Fn68BYXyktmzym3JzGXC7uBbfgD3dNbgpWjpeGsj9dr1"),
            fee_recipient: Pubkey::from_str_const("G5UZAVbAf46s7cKWoyKu8kYTip9DGTpbLZ2qa9Aq69dP"),
            event_authority: Pubkey::from_str_const("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR"),
            target_wallet: Pubkey::from_str_const("LsFaaTmthWhQ3pqhJQVcuxYuuDjeuWtqowRVHsiqMDi"),
        });

        assert_eq!(result, expected);
    }

    #[tokio::test(flavor = "multi_thread", worker_threads = 2)]
    async fn test_get_latest_token_buy_info() {
        let rpc_url = "https://api.mainnet-beta.solana.com".to_string();
        let client = RpcClient::new(rpc_url);

        // Test Raydium token
        let raydium_token =
            Pubkey::from_str("GBLMVFNai3UNvxNn1NQHah7MvbwnQMrNVtcWVghepump").unwrap();

        let raydium_result = get_latest_token_buy_info(&client, &raydium_token).await;
        assert!(raydium_result.is_ok(), "Failed to get Raydium token info");

        if let Ok(PoolBuyInfo::Raydium(buy_info)) = raydium_result {
            assert!(
                buy_info.sol_amount > 0,
                "Raydium SOL amount should be positive"
            );
            assert!(
                buy_info.token_amount > 0,
                "Raydium token amount should be positive"
            );
        } else {
            assert!(
                false,
                "Expected Raydium pool type for GBLMVFNai3UNvxNn1NQHah7MvbwnQMrNVtcWVghepump"
            );
        }

        // Test Pump.fun token
        let pump_fun_token =
            Pubkey::from_str("Sg7oxJTtTvUnLkAZW43rX8E4MgP994Vm6fiDxh7pump").unwrap();

        let pump_result = get_latest_token_buy_info(&client, &pump_fun_token).await;
        assert!(pump_result.is_ok(), "Failed to get Pump.fun token info");

        if let Ok(PoolBuyInfo::PumpFun(buy_info)) = pump_result {
            assert!(
                buy_info.sol_amount > 0,
                "Pump.fun SOL amount should be positive"
            );
            assert!(
                buy_info.token_amount > 0,
                "Pump.fun token amount should be positive"
            );
        } else {
            assert!(
                false,
                "Expected Pump.fun pool type for Sg7oxJTtTvUnLkAZW43rX8E4MgP994Vm6fiDxh7pump"
            );
        }
    }
}

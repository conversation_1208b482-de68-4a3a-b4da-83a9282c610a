use anyhow::{Context, Result};
use reqwest::Url;
use std::sync::Arc;
use std::time::Duration;
use teloxide::prelude::*;
use teloxide::types::{
    InlineKeyboardButton, InlineKeyboardMarkup, Message, MessageId, ParseMode, ReplyParameters,
};
use tokio::time::timeout;

use crate::format_number;

// Define a reasonable timeout for Telegram API requests
const TELEGRAM_API_TIMEOUT_SECS: u64 = 10;

#[derive(Debug, Clone)]
pub struct TelegramBot {
    bot: Bot,
    pub channel_id: ChatId,
}

impl TelegramBot {
    pub fn new(bot_token: &str, channel_id: i64) -> Self {
        let bot = Bot::new(bot_token);
        let channel_id = ChatId(channel_id);

        Self { bot, channel_id }
    }

    pub async fn send_message(&self, text: &str) -> Result<Message> {
        // Add timeout to avoid indefinite hanging
        let result = timeout(
            Duration::from_secs(TELEGRAM_API_TIMEOUT_SECS),
            self.bot
                .send_message(self.channel_id, text)
                .parse_mode(ParseMode::Html),
        )
        .await;

        match result {
            Ok(api_result) => Ok(api_result?),
            Err(_) => Err(anyhow::anyhow!(
                "Telegram API request timed out after {} seconds",
                TELEGRAM_API_TIMEOUT_SECS
            )),
        }
    }

    pub async fn reply_to_message(&self, text: &str, reply_to_message_id: i32) -> Result<Message> {
        // Add timeout to avoid indefinite hanging
        let result = timeout(
            Duration::from_secs(TELEGRAM_API_TIMEOUT_SECS),
            self.bot
                .send_message(self.channel_id, text)
                .parse_mode(ParseMode::Html)
                .reply_parameters(ReplyParameters::new(MessageId(reply_to_message_id))),
        )
        .await;

        match result {
            Ok(api_result) => Ok(api_result?),
            Err(_) => Err(anyhow::anyhow!(
                "Telegram API request timed out after {} seconds",
                TELEGRAM_API_TIMEOUT_SECS
            )),
        }
    }

    pub fn get_message_link(&self, message: &Message) -> String {
        let message_id = message.id.0;
        format!("https://t.me/pump_hound_signal/{}", message_id)
    }

    pub fn generate_keyboard_buttons(
        contract_address: &str,
        query: &str,
        query_for_analysis: Option<&str>,
        token_type: &str,
        is_zh_tw: bool,
    ) -> InlineKeyboardMarkup {
        let chart_url = format!(
            "https://www.kryptogo.xyz/token/{}/{}?{}",
            token_type, contract_address, query
        );
        let trade_url = format!(
            "https://app.kryptogo.xyz/token/{}/{}?{}",
            token_type, contract_address, query
        );
        let explore_url = format!("https://www.kryptogo.xyz?{}", query);
        let download_url = format!("https://www.kryptogo.com/products/wallet?{}", query);

        let (chart_label, trade_label, analysis_label, explore_label, download_label) = if is_zh_tw
        {
            (
                "📈 線圖",
                "🔥 交易",
                "😈 莊家分析",
                "🛜 查看所有訊號",
                "💎 下載 KryptoGO",
            )
        } else {
            (
                "📈 Chart",
                "🔥 Trade",
                "😈 Insider Analysis",
                "🛜 Explore all Signals",
                "💎 Download KryptoGO",
            )
        };

        let mut buttons = vec![vec![
            InlineKeyboardButton::url(chart_label.to_owned(), Url::parse(&chart_url).unwrap()),
            InlineKeyboardButton::url(trade_label.to_owned(), Url::parse(&trade_url).unwrap()),
        ]];

        // Add analysis button if query_for_analysis is provided
        if let Some(analysis_query) = query_for_analysis {
            let analysis_url = format!(
                "https://www.kryptogo.xyz/token/{}/{}?{}",
                token_type, contract_address, analysis_query
            );
            buttons.push(vec![InlineKeyboardButton::url(
                analysis_label.to_owned(),
                Url::parse(&analysis_url).unwrap(),
            )]);
        }

        buttons.extend_from_slice(&[
            vec![InlineKeyboardButton::url(
                explore_label.to_owned(),
                Url::parse(&explore_url).unwrap(),
            )],
            vec![InlineKeyboardButton::url(
                download_label.to_owned(),
                Url::parse(&download_url).unwrap(),
            )],
        ]);

        InlineKeyboardMarkup::new(buttons)
    }

    pub async fn send_fomo_signal(
        &self,
        contract_address: &str,
        symbol: &str,
        price: f64,
    ) -> Result<Message> {
        let symbol = symbol.trim_end_matches('\0');
        let query = "utm_source=tg&utm_medium=channel&action=buy";
        let query_for_analysis = "utm_source=tg&utm_medium=channel&action=buy&openModal=true";
        let mut link_url = format!("https://www.kryptogo.xyz/token/sol/{}/", contract_address);
        link_url.push_str(
            &(0..3)
                .map(|_| {
                    let random_byte = rand::random::<u8>();
                    if random_byte % 36 < 26 {
                        (b'a' + (random_byte % 26)) as char
                    } else {
                        (b'0' + ((random_byte % 36) - 26)) as char
                    }
                })
                .collect::<String>(),
        );
        link_url = format!("{}?{}", link_url, query);
        let message = if price == 0.0 {
            format!(
                "🟢 Buy <b>{}</b>\n📝 CA: <code>{}</code>\nℹ️ Smart money showing interest, this could be a good entry! <a href=\"{}\">Check more info</a>",
                symbol,
                contract_address,
                link_url,
            )
        } else {
            format!(
                "🟢 Buy <b>{}</b>\n💰 Price: ${}\n📝 CA: <code>{}</code>\nℹ️ Smart money showing interest, this could be a good entry! <a href=\"{}\">Check more info</a>",
                symbol,
                format_number(price),
                contract_address,
                link_url,
            )
        };

        let keyboard = Self::generate_keyboard_buttons(
            contract_address,
            query,
            Some(query_for_analysis),
            "sol",
            false,
        );

        // Add timeout to avoid indefinite hanging
        let result = timeout(
            Duration::from_secs(TELEGRAM_API_TIMEOUT_SECS),
            self.bot
                .send_message(self.channel_id, message)
                .parse_mode(ParseMode::Html)
                .reply_markup(keyboard),
        )
        .await;

        match result {
            Ok(api_result) => Ok(api_result?),
            Err(_) => Err(anyhow::anyhow!(
                "Telegram API request timed out after {} seconds",
                TELEGRAM_API_TIMEOUT_SECS
            )),
        }
    }

    pub async fn send_sold_signal(
        &self,
        contract_address: &str,
        symbol: &str,
        price_usd: f64,
        highest_price_change_percentage: f64,
        reply_to_msg_id: Option<i32>,
    ) -> Result<Message> {
        let symbol = symbol.trim_end_matches('\0');
        let query = "utm_source=tg&utm_medium=channel&action=sell";
        let query_for_analysis = "utm_source=tg&utm_medium=channel&action=sell&openModal=true";

        let message = if price_usd == 0.0 {
            format!(
                "🔴 Sell <b>{}</b>\n📝 CA: <code>{}</code>\nℹ️ Smart money significantly reduced holdings. Sell to avoid risk.",
                symbol, contract_address,
            )
        } else {
            format!(
                "🔴 Sell <b>{}</b>\n💰 Price: ${}\n🎉 Highest Gain: <b>+{:.0}%</b>\n📝 CA: <code>{}</code>\nℹ️ Smart money significantly reduced holdings. Sell to avoid risk.",
                symbol,
                format_number(price_usd),
                highest_price_change_percentage,
                contract_address,
            )
        };

        let keyboard = Self::generate_keyboard_buttons(
            contract_address,
            query,
            Some(query_for_analysis),
            "sol",
            false,
        );

        let mut req = self
            .bot
            .send_message(self.channel_id, message)
            .parse_mode(ParseMode::Html)
            .reply_markup(keyboard);

        // Add reply parameters if a message ID is provided
        if let Some(msg_id) = reply_to_msg_id {
            req = req.reply_parameters(ReplyParameters::new(MessageId(msg_id)));
        }

        // Add timeout to avoid indefinite hanging
        let result = timeout(Duration::from_secs(TELEGRAM_API_TIMEOUT_SECS), req).await;

        match result {
            Ok(api_result) => Ok(api_result.context("Failed to send Telegram message")?),
            Err(_) => Err(anyhow::anyhow!(
                "Telegram API request timed out after {} seconds",
                TELEGRAM_API_TIMEOUT_SECS
            )),
        }
    }

    pub async fn send_bnb_fomo_signal(
        &self,
        contract_address: &str,
        symbol: &str,
        price: f64,
    ) -> Result<Message> {
        let symbol = symbol.trim_end_matches('\0');
        let query = "utm_source=tg&utm_medium=channel&action=buy";
        let mut link_url = format!("https://www.kryptogo.xyz/token/bnb/{}/", contract_address);
        link_url.push_str(
            &(0..3)
                .map(|_| {
                    let random_byte = rand::random::<u8>();
                    if random_byte % 36 < 26 {
                        (b'a' + (random_byte % 26)) as char
                    } else {
                        (b'0' + ((random_byte % 36) - 26)) as char
                    }
                })
                .collect::<String>(),
        );
        link_url = format!("{}?{}", link_url, query);
        let message = if price == 0.0 {
            format!(
                "🟢 Buy <b>{}</b>\n📝 CA: <code>{}</code>\nℹ️ Smart money showing interest, this could be a good entry! <a href=\"{}\">Check more info</a>",
                symbol,
                contract_address,
                link_url,
            )
        } else {
            format!(
                "🟢 Buy <b>{}</b>\n💰 Price: ${}\n📝 CA: <code>{}</code>\nℹ️ Smart money showing interest, this could be a good entry! <a href=\"{}\">Check more info</a>",
                symbol,
                format_number(price),
                contract_address,
                link_url,
            )
        };

        let keyboard = Self::generate_keyboard_buttons(contract_address, query, None, "bnb", false);

        // Add timeout to avoid indefinite hanging
        let result = timeout(
            Duration::from_secs(TELEGRAM_API_TIMEOUT_SECS),
            self.bot
                .send_message(self.channel_id, message)
                .parse_mode(ParseMode::Html)
                .reply_markup(keyboard),
        )
        .await;

        match result {
            Ok(api_result) => Ok(api_result?),
            Err(_) => Err(anyhow::anyhow!(
                "Telegram API request timed out after {} seconds",
                TELEGRAM_API_TIMEOUT_SECS
            )),
        }
    }

    pub async fn send_bnb_sold_signal(
        &self,
        contract_address: &str,
        symbol: &str,
        price_usd: f64,
        reply_to_msg_id: Option<i32>,
    ) -> Result<Message> {
        let symbol = symbol.trim_end_matches('\0');
        let query = "utm_source=tg&utm_medium=channel&action=sell";

        let message = if price_usd == 0.0 {
            format!(
                "🔴 Sell <b>{}</b>\n📝 CA: <code>{}</code>\nℹ️ Smart money significantly reduced holdings. Sell to avoid risk.",
                symbol, contract_address,
            )
        } else {
            format!(
                "🔴 Sell <b>{}</b>\n💰 Price: ${}\n📝 CA: <code>{}</code>\nℹ️ Smart money significantly reduced holdings. Sell to avoid risk.",
                symbol,
                format_number(price_usd),
                contract_address,
            )
        };

        let keyboard = Self::generate_keyboard_buttons(contract_address, query, None, "bnb", false);

        let mut req = self
            .bot
            .send_message(self.channel_id, message)
            .parse_mode(ParseMode::Html)
            .reply_markup(keyboard);

        // Add reply parameters if a message ID is provided
        if let Some(msg_id) = reply_to_msg_id {
            req = req.reply_parameters(ReplyParameters::new(MessageId(msg_id)));
        }

        // Add timeout to avoid indefinite hanging
        let result = timeout(Duration::from_secs(TELEGRAM_API_TIMEOUT_SECS), req).await;

        match result {
            Ok(api_result) => Ok(api_result.context("Failed to send Telegram message")?),
            Err(_) => Err(anyhow::anyhow!(
                "Telegram API request timed out after {} seconds",
                TELEGRAM_API_TIMEOUT_SECS
            )),
        }
    }

    pub async fn send_milestone_notification(
        &self,
        contract_address: &str,
        symbol: &str,
        current_price: f64,
        increase_percentage: f64,
        reply_to_msg_id: Option<i32>,
    ) -> Result<Message> {
        let symbol = symbol.trim_end_matches('\0');
        let query = "utm_source=tg&utm_medium=channel&action=milestone";
        let query_for_analysis = "utm_source=tg&utm_medium=channel&action=milestone&openModal=true";

        // Add money emojis based on increase percentage
        let emoji = if increase_percentage > 0.0 {
            let emoji_count = (increase_percentage / 100.0 + 1.0).log(1.3).floor() as usize + 1;
            "🤑".repeat(emoji_count)
        } else {
            "".to_string()
        };

        let message = format!(
            "🚀 <b>{}</b> | PRICE MILESTONE\n📊 Increase: <b>+{:.0}%</b> {}\n💰 Current Price: ${}\n📝 CA: <code>{}</code>",
            symbol,
            increase_percentage,
            emoji,
            format_number(current_price),
            contract_address,
        );

        let keyboard = Self::generate_keyboard_buttons(
            contract_address,
            query,
            Some(query_for_analysis),
            "sol",
            false,
        );

        let mut req = self
            .bot
            .send_message(self.channel_id, message)
            .parse_mode(ParseMode::Html)
            .reply_markup(keyboard);

        if let Some(msg_id) = reply_to_msg_id {
            req = req.reply_parameters(ReplyParameters::new(MessageId(msg_id)));
        }

        // Add timeout to avoid indefinite hanging
        let result = timeout(Duration::from_secs(TELEGRAM_API_TIMEOUT_SECS), req).await;

        match result {
            Ok(api_result) => Ok(api_result.context("Failed to send Telegram message")?),
            Err(_) => Err(anyhow::anyhow!(
                "Telegram API request timed out after {} seconds",
                TELEGRAM_API_TIMEOUT_SECS
            )),
        }
    }
}

// Global instance that can be shared across threads
pub static TELEGRAM_BOT: tokio::sync::OnceCell<Arc<TelegramBot>> =
    tokio::sync::OnceCell::const_new();

use anyhow::anyhow;
use anyhow::Result;
use log::{debug, info, warn};
use once_cell::sync::Lazy;
use reqwest::{header, Client, StatusCode};
use serde::{Deserialize, Serialize};
use serde_json;
use solana_sdk::pubkey::Pubkey;
use spl_associated_token_account::get_associated_token_address;
use std::sync::Mutex;
use std::{
    collections::{HashMap, HashSet},
    fs,
    path::Path,
    str::FromStr,
    sync::Arc,
    time::{Duration, Instant, SystemTime, UNIX_EPOCH},
};
use tokio::time::sleep;

use crate::browser_automation;

#[derive(Debug, Deserialize, Serialize, <PERSON>lone)]
pub struct TransferActivity {
    pub block_id: u64,
    pub trans_id: String,
    pub block_time: u64,
    pub activity_type: String,
    pub from_address: String,
    pub from_token_account: String,
    pub to_address: String,
    pub to_token_account: String,
    pub token_address: String,
    pub token_decimals: u8,
    pub amount: u64,
    pub flow: String,
    pub value: f64,
}

#[derive(Debug, Deserialize, Serialize, <PERSON>lone)]
pub struct AccountMetadata {
    pub account_address: String,
    pub account_label: Option<String>,
    pub account_tags: Option<Vec<String>>,
    pub account_type: Option<String>,
    pub account_icon: Option<String>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TransferResponse {
    pub success: bool,
    pub data: Vec<TransferActivity>,
    pub metadata: Metadata,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct Metadata {
    pub accounts: HashMap<String, AccountMetadata>,
}

#[derive(Debug, Clone)]
pub struct TransferCounter {
    pub in_transfers: usize,
    pub out_transfers: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssociatedWallet {
    pub address: String,
    pub in_transfers: usize,
    pub out_transfers: usize,
    pub is_bidirectional: bool,
    pub tags: Vec<String>,
    pub label: String,
}

impl AssociatedWallet {
    pub fn total_transfers(&self) -> usize {
        self.in_transfers + self.out_transfers
    }

    pub fn new(
        address: String,
        in_transfers: usize,
        out_transfers: usize,
        is_bidirectional: bool,
        tags: Vec<String>,
        label: String,
    ) -> Self {
        Self {
            address,
            in_transfers,
            out_transfers,
            is_bidirectional,
            tags,
            label,
        }
    }
}

/// Global set to track all tags we've seen
static SEEN_TAGS: Lazy<Mutex<std::collections::HashSet<String>>> =
    Lazy::new(|| Mutex::new(std::collections::HashSet::new()));

pub static KNOWN_EXCLUSIONS: Lazy<Vec<&str>> = Lazy::new(|| {
    vec![
        "9yj3zvLS3fDMqi1F8zhkaWfq8TZpZWHe6cz1Sgt7djXf",
        "4bvsT2fGPMVJ2CkdD1KmQYeJRPB5Xt3hsbPUcHwNj9JV",
        "AxiomRYAid8ZDhS1bJUAzEaNSr69aTWB9ATfdDLfUbnc",
        "AaG6of1gbj1pbDumvbSiTuJhRCRkkUNaWVxijSbWvTJW",
        "CeA3sPZfWWToFEBmw5n1Y93tnV66Vmp8LacLzsVprgxZ",
        "4V65jvcDG9DSQioUVqVPiUcUY9v6sb6HKtMnsxSKEz5S",
        "7LCZckF6XXGQ1hDY6HFXBKWAtiUgL9QY5vj1C4Bn1Qjj",
        "4GQeEya6ZTwvXre4Br6ZfDyfe2WQMkcDz2QbkJZazVqS",
        "j1oeQoPeuEDmjvyMwBmCWexzCQup77kbKKxV59CnYbd",
        "j1oxqtEHFn7rUkdABJLmtVtz5fFmHFs4tCG3fWJnkHX",
        "9yMwSPk9mrXSN7yDHUuZurAh1sjbJsfpUqjZ7SvVtdco",
        "*******************************************",
        "JBGUGPmKUEHCpxGGoMowQxoV4c7HyqxEnyrznVPxftqk",
        "FudPMePeNqmnjMX19zEKDfGXpbp6HAdW6ZGprB5gYRTZ",
        "8psNvWTrdNTiVRNzAgsou9kETXNJm2SXZyaKuJraVRtf",
        "********************************************",
        "EnVppANi2qXbDdWKKXkBTXBx4h2bTAgJko2bYhmfPvKE",
        "EDN6SYHMg5qnUrD7ytq6MDY7Bjcqcb8XNknsgcCMuS2R",
        "Dd2CoUvcELVPr9X9Ev5iRkJp1vzDLYrZw7hzFakYhY2E",
        "GF74UsMG7iYeGqKJyAsMiwcrfh8y58tH1W8MNBsRLLab",
        "G9PhF9C9H83mAjjkdJz4MDqkufiTPMJkx7TnKE1kFyCp",
        "CPvLKPPmb23MSUnzHZeB1dX47oQFdUwxo9yncAndFri4",
        "GV8unppAUobvPfjrR5kDHpcYG8T19KLLLkXAS3sWzgGn",
        "9ujUdfWLsTY2bohj18p9u3neSQ537dY6LCZR7VzbgkWt",
        "HviSb86wUarNoFryWegVMVG115TMbpeTVYLfjhRHQjnh",
        "BzXdDUvNnitfoAEXefvUUc7ZjqXv7Yv3H5aNeGwYjZMS",
        "C4MtffotdnDV47arXo9fQxY4Q2pUBmHgYiw6gfRkgffK",
        "AhZEEmAMVoSyvEoSrL99izp2t5yqFaGkrbZab68aGC8i",
        "79gUQP8xFnrvZxxnnKRKaVWgKNPtG7mKoThxxznKLxJ2",
        "GhaCLDbcaB3qXphhQjXPxS9MNubkAs4bcqf4degHgrv4",
        "BtYvTw4vp517yJnsA4H3FG1fKezANhz95pxdCXa3BYSK",
        "9RYJ3qr5eU5xAooqVcbmdeusjcViL5Nkiq7Gske3tiKq",
        "96ywtMs5KJNt2iAinr1U8KMzxjcY1FUEpgKHMYNz818g",
        "F7p3dFrjRTbtRp8FRF6qHLomXbKRBzpvBLjtQcfcgmNe",
        "j1opmdubY84LUeidrPCsSGskTCYmeJVzds1UWm6nngb",
        "j1oAbxxiDUWvoHxEDhWE7THLjEkDQW2cSHYn2vttxTF",
        "AasQTQH9oroodW5vi3uEoDuLyJDVfMz7GWehvisdGmDX",
        "ZG98FUCjb8mJ824Gbs6RsgVmr1FhXb2oNiJHa2dwmPd",
        "7dm9am6Qx7cH64RB99Mzf7ZsLbEfmXM7ihXXCvMiT2X1",
        "6YbgpW8f974ZJvM4x9gAkz2PLt1acbLQP897FxMQ4XLy",
        "AVahywMVNRYzdgWrufSWrtdGXAeNEvfpJFxhVFK516mT",
        "88dXPPjFb6nUsqUY19dCaARADV3H7uBmjK5CkYpr3Woa",
        "34FKjAdVcTax2DHqV2XnbXa9J3zmyKcFuFKWbcmgxjgm",
        "GWfuH79MNvGQACiXtSQrY8VgPnJwrF1PpLVvxMwQLZA3",
        "G2YxRa6wt1qePMwfJzdXZG62ej4qaTC7YURzuh2Lwd3t",
        "7y5dEvubdMtenAUxY9PS7Kv7sS8QiKX6pibk2CKvmLW6",
        "5ndLnEYqSFiA5yUFHo6LVZ1eWc6Rhh11K5CfJNkoHEPs",
        "Mikamismig6oeeQJJR2ix3b6Nm2jQB4MtixQEb4vtw5",
        "FzZ77TM8Ekcb6gyWPmcT9upWkAZKZc5xrYfuFu7pifPn",
        "HRLqtFmSnbQL4QDUPkq62Uf8G9qbUrD5FtCGWwNLv4aT",
        "7rtiKSUDLBm59b1SBmD9oajcP8xE64vAGSMbAN5CXy1q",
        "8QwU16Xe4BPyUD9MktHtgVjQQ5fAwywb9Zd5Hg1YTauF",
        "EmNPrL1X2mHwBsVkNLf2mYGHTUUBp3Bvx4RgK9hpeJBH",
        "9bc61xemFMSZBsQZp59zQppw3sGXrPhRkxrdVBtip6om",
        "Cc3bpPzUvgAzdW9Nv7dUQ8cpap8Xa7ujJgLdpqGrTCu6",
        "HLNu3gxUqCweqBuoYTfqSDaUN3iaLwy6N4aDPSVzy2t2",
        "Biw4eeaiYYYq6xSqEd7GzdwsrrndxA8mqdxfAtG3PTUU",
    ]
});

/// Check if an account should be excluded based on its tags
pub fn should_exclude_account(
    wallet: &str,
    account: &Option<&AccountMetadata>,
    include_token_creators: bool,
) -> bool {
    if KNOWN_EXCLUSIONS.contains(&wallet) {
        return true;
    }
    if !is_on_curve(wallet) {
        return true;
    }
    if let Some(account) = account {
        if let Some(tags) = &account.account_tags {
            for tag in tags {
                let tag_lower = tag.as_str().to_lowercase();
                match tag_lower.as_str() {
                    "market" | "exchange_wallet" | "dex_wallet" | "meteora" | "pool_owner"
                    | "vault_owner" | "pump.fun amm" | "jupiter" | "orca" | "mev_bot"
                    | "binance" | "solfi" | "raydium" | "phoenix" | "pool" | "bybit"
                    | "boop.fun" | "mexc" | "jupiter_perp" | "lifinity" | "kraken"
                    | "streamflow" | "lptoken" | "coinbase" | "fee_vault" | "moonshot"
                    | "raydium_launchpad" | "backpack_exchange" => {
                        return true;
                    }
                    "pump.fun" | "exchange_deposit_address" => {
                        continue;
                    }
                    "token_creator" => {
                        // Skip excluding token creators if requested
                        if include_token_creators {
                            continue;
                        } else {
                            return true;
                        }
                    }
                    _ => {
                        // Record tag in global set and print if new
                        let mut seen_tags = SEEN_TAGS.lock().unwrap();
                        if !seen_tags.contains(tag) {
                            seen_tags.insert(tag.clone());
                            println!("New tag found: {}", tag);
                        }
                    }
                }
            }
        }
    }
    is_likely_program_account(account, include_token_creators)
}

/// Check if an account is likely to be a DEX or program-controlled account
pub fn is_likely_program_account(
    account: &Option<&AccountMetadata>,
    include_token_creators: bool,
) -> bool {
    if let Some(account) = account {
        // If we're including token creators, check if this is one
        if include_token_creators {
            if let Some(tags) = &account.account_tags {
                if tags.iter().any(|t| t == "token_creator") {
                    return false; // Don't consider token creators as program accounts
                }
            }
        }

        // Check account labels for program-controlled account patterns
        if let Some(label) = &account.account_label {
            let label_lower = label.to_lowercase();
            if label_lower.contains("jupiter")
                || label_lower.contains("aggregator")
                || label_lower.contains("authority")
                || label_lower.contains("pool")
                || label_lower.contains("program")
                || label_lower.contains("vault")
                || label_lower.contains("contract")
                || label_lower.contains("treasury")
                || label_lower.contains("escrow")
                || label_lower.contains("router")
                || label_lower.contains("wintermute")
                || label_lower.contains("amm")
                || label_lower.contains("fee")
                || label_lower.contains("tip")
                || label_lower.contains("jito")
                || label_lower.contains("vault")
                || label_lower.contains("gmgn")
            {
                return true;
            }
        }

        // Check account tags
        if let Some(tags) = &account.account_tags {
            for tag in tags {
                let tag_lower = tag.to_lowercase();
                if tag_lower.contains("dex")
                    || tag_lower.contains("jupiter")
                    || tag_lower.contains("market")
                    || tag_lower == "exchange"
                    || tag_lower.contains("program")
                    || tag_lower.contains("protocol")
                {
                    // Don't exclude token creators here if requested
                    if include_token_creators && tags.iter().any(|t| t == "token_creator") {
                        continue;
                    }
                    return true;
                }
            }
        }
    }
    false
}

/// Get associated token address for a wallet and token
pub fn get_token_account(wallet: &str, token: &str) -> Result<Pubkey> {
    let wallet_pubkey = Pubkey::from_str(wallet)?;
    let token_pubkey = Pubkey::from_str(token)?;

    let token_account = get_associated_token_address(&wallet_pubkey, &token_pubkey);
    Ok(token_account)
}

pub async fn fetch_transfers(
    client: &Client,
    wallet: &str,
    sol_token: &str,
    token: &str,
    usdc_token: &str,
    to_exclude: &[String],
    from_exclude: &[String],
    page: usize,
) -> Result<TransferResponse> {
    // Construct the URL with parameters
    let mut url = format!(
        "https://api-v2.solscan.io/v2/account/transfer?address={}&page={}&page_size=100&remove_spam=true&exclude_amount_zero=true&activity_type[]=ACTIVITY_SPL_TRANSFER&token={},{},{}",
        wallet, page, sol_token, token, usdc_token
    );

    // Add from exclusions (limited to 5)
    if !from_exclude.is_empty() {
        let exclude_list = from_exclude
            .iter()
            .take(5) // Solscan API accepts max 5 addresses
            .map(|addr| format!("!{}", addr))
            .collect::<Vec<_>>()
            .join(",");

        url.push_str(&format!("&from={}", exclude_list));
    }

    // Add to exclusions (limited to 5)
    if !to_exclude.is_empty() {
        let exclude_list = to_exclude
            .iter()
            .take(5) // Solscan API accepts max 5 addresses
            .map(|addr| format!("!{}", addr))
            .collect::<Vec<_>>()
            .join(",");

        url.push_str(&format!("&to={}", exclude_list));
    }

    debug!("Request URL: {}", url);

    // Create the request with headers
    let mut attempts = 0;
    let max_attempts = 2;
    let mut delay = 1000; // Start with 1 second delay (in milliseconds)

    loop {
        // Get Solscan cookies
        let cookies = browser_automation::get_solscan_cookies().await?;

        let response = match client
            .get(&url)
            .header(header::ACCEPT, "application/json, text/plain, */*")
            .header(header::ACCEPT_LANGUAGE, "en-US,en;q=0.9")
            .header(header::CACHE_CONTROL, "no-cache")
            .header(header::PRAGMA, "no-cache")
            .header(header::ORIGIN, "https://solscan.io")
            .header(header::REFERER, "https://solscan.io/")
            .header(
                "sec-ch-ua",
                "\"Chromium\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            )
            .header("sec-ch-ua-mobile", "?0")
            .header("sec-ch-ua-platform", "\"macOS\"")
            .header(header::USER_AGENT, &cookies.user_agent)
            .header("token", &cookies.token)
            .header(
                header::COOKIE,
                format!("cf_clearance={}", cookies.cf_clearance),
            )
            .send()
            .await
        {
            Ok(resp) => resp,
            Err(e) => {
                warn!("Error making request to Solscan: {}", e);
                let message = format!("⚠️ Solscan request error: {}", e);
                if let Err(e) = send_slack_notification(&message).await {
                    warn!("Failed to send Slack notification: {}", e);
                }
                browser_automation::invalidate_cf_clearance()?;
                return Err(anyhow!("Request error: {}", e));
            }
        };

        attempts += 1;

        if response.status() == StatusCode::OK {
            // If we got a successful response, parse and return it
            let transfer_data: TransferResponse = response.json().await?;
            return Ok(transfer_data);
        }

        if attempts >= max_attempts {
            warn!(
                "Max retry attempts reached for Solscan request. Status code: {}, attempts: {}/{}",
                response.status(),
                attempts,
                max_attempts
            );

            // Send Slack notification for max attempts reached
            let message = format!(
                "⚠️ Solscan max retry attempts reached! Status code: {}, attempts: {}/{}",
                response.status(),
                attempts,
                max_attempts
            );
            if let Err(e) = send_slack_notification(&message).await {
                warn!("Failed to send Slack notification: {}", e);
            }
            browser_automation::invalidate_cf_clearance()?;

            return Err(anyhow!("Max retry attempts reached"));
        }

        warn!(
            "Solscan status code: {}, attempt {}/{}. Retrying in {}ms",
            response.status(),
            attempts,
            max_attempts,
            delay
        );

        // Send Slack notification for authentication issues
        let message = format!(
            "⚠️ Solscan authentication required! Status code: {}",
            response.status()
        );
        if let Err(e) = send_slack_notification(&message).await {
            warn!("Failed to send Slack notification: {}", e);
        }

        // Sleep with exponential backoff
        tokio::time::sleep(Duration::from_millis(delay)).await;

        // Increase delay exponentially for next attempt
        delay *= 5 / 4;

        browser_automation::invalidate_cf_clearance()?;
    }
}

#[derive(Debug, Serialize, Deserialize)]
struct CachedWalletAnalysis {
    timestamp: u64,
    associated_wallets: Vec<AssociatedWallet>,
}

#[derive(Debug, Clone)]
pub struct WalletAnalyzer {
    client: Client,
    token: String,
    max_pages: usize,
    min_transfers: usize,
    include_one_way: bool,
    min_one_way_transfers: usize,
    include_token_creators: bool,
}

impl WalletAnalyzer {
    pub fn new(
        token: String,
        max_pages: usize,
        min_transfers: usize,
        include_one_way: bool,
        min_one_way_transfers: usize,
        include_token_creators: bool,
    ) -> Result<Self> {
        let client = Client::builder().timeout(Duration::from_secs(30)).build()?;

        Ok(Self {
            client,
            token,
            max_pages,
            min_transfers,
            include_one_way,
            min_one_way_transfers,
            include_token_creators,
        })
    }

    fn get_cache_path(&self, wallet: &str) -> String {
        let cache_dir = "wallet_cache";
        if !Path::new(cache_dir).exists() {
            fs::create_dir_all(cache_dir).expect("Failed to create cache directory");
        }
        format!("{}/{}.json", cache_dir, wallet)
    }

    fn read_from_cache(&self, wallet: &str) -> Option<Vec<AssociatedWallet>> {
        let cache_path = self.get_cache_path(wallet);
        if !Path::new(&cache_path).exists() {
            return None;
        }

        match fs::read_to_string(&cache_path) {
            Ok(content) => {
                match serde_json::from_str::<CachedWalletAnalysis>(&content) {
                    Ok(cached) => {
                        let current_time = SystemTime::now()
                            .duration_since(UNIX_EPOCH)
                            .unwrap()
                            .as_secs();

                        // Check if cache is less than 1 day old
                        if current_time - cached.timestamp < 24 * 60 * 60 {
                            debug!("Using cached data for wallet {}", wallet);
                            return Some(cached.associated_wallets);
                        }
                    }
                    Err(e) => {
                        warn!("Failed to parse cache for wallet {}: {}", wallet, e);
                    }
                }
            }
            Err(e) => {
                warn!("Failed to read cache for wallet {}: {}", wallet, e);
            }
        }
        None
    }

    fn write_to_cache(&self, wallet: &str, associated_wallets: &[AssociatedWallet]) -> Result<()> {
        let cache_path = self.get_cache_path(wallet);
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let cache_data = CachedWalletAnalysis {
            timestamp: current_time,
            associated_wallets: associated_wallets.to_vec(),
        };

        let json = serde_json::to_string_pretty(&cache_data)?;
        fs::write(&cache_path, json)?;
        debug!("Cached data for wallet {}", wallet);
        Ok(())
    }

    pub async fn analyze_wallet(&self, wallet: &str) -> Result<Vec<AssociatedWallet>> {
        // Check cache first
        if let Some(cached_wallets) = self.read_from_cache(wallet) {
            return Ok(cached_wallets);
        }

        // Check if wallet is on Solana curve
        if !is_on_curve(wallet) {
            debug!(
                "Wallet {} is not on Solana curve, skipping analysis",
                wallet
            );
            return Ok(Vec::new());
        }
        if KNOWN_EXCLUSIONS.contains(&wallet) {
            return Ok(Vec::new());
        }

        // Constants for token addresses
        let sol_token = "So11111111111111111111111111111111111111111";
        let usdc_token = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";

        // Get the token account for the wallet and token
        let token_account = get_token_account(wallet, &self.token)?;
        debug!(
            "Token account for wallet {} and token {}: {}",
            wallet, self.token, token_account
        );

        // First, fetch transfers without exclusions to identify accounts to exclude
        debug!("Fetching initial transfer data to identify accounts to exclude...");
        let initial_response = fetch_transfers(
            &self.client,
            wallet,
            sol_token,
            &self.token,
            usdc_token,
            &[],
            &[],
            1,
        )
        .await?;
        debug!("got initial response");

        // Extract accounts to exclude
        let mut exclude_to_addresses: HashSet<String> = HashSet::new();
        let mut exclude_from_addresses: HashSet<String> = HashSet::new();

        for activity in &initial_response.data {
            // For 'in' transfers, check the from_address for exclusion
            if activity.flow == "in" {
                let account_meta = initial_response
                    .metadata
                    .accounts
                    .get(&activity.from_address);
                if should_exclude_account(
                    &activity.from_address,
                    &account_meta,
                    self.include_token_creators,
                ) {
                    exclude_from_addresses.insert(activity.from_address.clone());
                    // debug!(
                    //     "Excluding from address: {} (tags: {:?})",
                    //     activity.from_address,
                    //     account_meta.map(|m| m.account_tags.clone().unwrap_or_default())
                    // );
                }
            }

            // For 'out' transfers, check the to_address for exclusion
            if activity.flow == "out" {
                let account_meta = initial_response.metadata.accounts.get(&activity.to_address);
                if should_exclude_account(
                    &activity.to_address,
                    &account_meta,
                    self.include_token_creators,
                ) {
                    exclude_to_addresses.insert(activity.to_address.clone());
                    // debug!(
                    //     "Excluding to address: {} (tags: {:?})",
                    //     activity.to_address,
                    //     account_meta.map(|m| m.account_tags.clone().unwrap_or_default())
                    // );
                }
            }
        }

        debug!(
            "Found {} addresses to exclude from 'to' field",
            exclude_to_addresses.len()
        );
        debug!(
            "Found {} addresses to exclude from 'from' field",
            exclude_from_addresses.len()
        );

        // Convert to vec for passing to API
        let exclude_to_vec: Vec<String> = exclude_to_addresses.into_iter().collect();
        let exclude_from_vec: Vec<String> = exclude_from_addresses.into_iter().collect();

        // Now fetch transfers with exclusions for all pages
        let mut all_activities: Vec<TransferActivity> = Vec::new();
        let mut all_accounts: HashMap<String, AccountMetadata> = HashMap::new();

        // Create a vector of futures for parallel fetching
        let mut fetch_futures = Vec::new();
        for page in 1..=self.max_pages {
            let client = &self.client;
            let token = self.token.clone();
            let exclude_to_vec = exclude_to_vec.clone();
            let exclude_from_vec = exclude_from_vec.clone();

            let future = async move {
                debug!("Fetching page {} of transfer data...", page);
                let response = fetch_transfers(
                    client,
                    &wallet,
                    &sol_token,
                    &token,
                    &usdc_token,
                    &exclude_to_vec,
                    &exclude_from_vec,
                    page,
                )
                .await?;

                // If we got less than 100 activities, we've reached the end
                let is_last_page = response.data.len() < 100;

                Ok::<(usize, TransferResponse, bool), anyhow::Error>((page, response, is_last_page))
            };

            fetch_futures.push(future);
        }

        // Execute all futures in parallel with a small delay between starts to avoid rate limiting
        let mut results = Vec::new();
        for future in fetch_futures {
            results.push(future);
            sleep(Duration::from_millis(20)).await;
        }

        // Wait for all results and process them
        let mut last_page_found = false;
        for result in futures::future::join_all(results).await {
            match result {
                Ok((page, response, is_last_page)) => {
                    // Store account metadata
                    all_accounts.extend(response.metadata.accounts.clone());

                    // Add activities to our collection
                    all_activities.extend(response.data.clone());

                    if is_last_page {
                        debug!("Reached the end of data at page {}", page);
                        last_page_found = true;
                    }
                }
                Err(e) => {
                    debug!("Error fetching transfer data: {}", e);
                }
            }
        }
        sleep(Duration::from_secs(1)).await;

        if !last_page_found && self.max_pages > 1 {
            debug!("Reached maximum page limit of {}", self.max_pages);
        }

        debug!(
            "Fetched a total of {} transfer activities",
            all_activities.len()
        );

        // Map to count in/out transfers for each counterparty
        let mut counterparty_transfers: HashMap<String, TransferCounter> = HashMap::new();

        // For one-way transfers, create a separate map counting only one way transfers
        let mut one_way_transfers: HashMap<String, TransferCounter> = HashMap::new();

        // Process all activities to count transfers
        for activity in &all_activities {
            let counterparty = if activity.flow == "in" {
                // For inflow, counterparty is the sender
                activity.from_address.clone()
            } else {
                // For outflow, counterparty is the recipient
                activity.to_address.clone()
            };

            // Skip if counterparty is the wallet itself
            if counterparty == wallet {
                continue;
            }

            // Update the all-token transfer counter
            let counter = counterparty_transfers
                .entry(counterparty.clone())
                .or_insert(TransferCounter {
                    in_transfers: 0,
                    out_transfers: 0,
                });

            if activity.flow == "in" {
                counter.in_transfers += 1;
            } else {
                counter.out_transfers += 1;
            }

            // For one-way transfers, at least 0.49 SOL to exclude some fee vault
            if self.include_one_way
                && (activity.token_address != "So11111111111111111111111111111111111111111"
                    || activity.amount >= 490_000_000)
            {
                let token_counter =
                    one_way_transfers
                        .entry(counterparty)
                        .or_insert(TransferCounter {
                            in_transfers: 0,
                            out_transfers: 0,
                        });

                if activity.flow == "in" {
                    token_counter.in_transfers += 1;
                } else {
                    token_counter.out_transfers += 1;
                }
            }
        }

        // Collect results
        let mut associated_wallets: Vec<AssociatedWallet> = Vec::new();

        // Filter counterparties that have both inflows and outflows (bidirectional)
        let bidirectional_addresses: Vec<(&String, &TransferCounter)> = counterparty_transfers
            .iter()
            .filter(|(address, counter)| {
                let account_info = all_accounts.get(*address);
                let should_exclude =
                    should_exclude_account(*address, &account_info, self.include_token_creators);

                // Include only if it has sufficient transfers in both directions AND is not a program account
                counter.in_transfers >= self.min_transfers
                    && counter.out_transfers >= self.min_transfers
                    && !should_exclude
            })
            .collect();

        // Add bidirectional addresses to results
        for (address, counter) in bidirectional_addresses {
            let account_info = all_accounts.get(address);
            let tags = account_info
                .and_then(|acc| acc.account_tags.clone())
                .unwrap_or_default();
            let label = account_info
                .and_then(|acc| acc.account_label.clone())
                .unwrap_or_default();

            associated_wallets.push(AssociatedWallet::new(
                address.clone(),
                counter.in_transfers,
                counter.out_transfers,
                true,
                tags,
                label,
            ));
        }

        // If requested, include wallets with one-way transfers
        if self.include_one_way {
            // Filter for one-way transfers
            let filtered_addresses: Vec<(String, TransferCounter)> = one_way_transfers
                .iter()
                .filter(|(address, counter)| {
                    let account_info = all_accounts.get(*address);
                    let should_exclude = should_exclude_account(
                        *address,
                        &account_info,
                        self.include_token_creators,
                    );

                    // One-way IN: Has sufficient IN transfers, no/few OUT transfers, and is not a program account
                    let is_in_only = counter.in_transfers >= self.min_one_way_transfers
                        && counter.out_transfers < self.min_transfers
                        && !should_exclude;

                    // One-way OUT: Has sufficient OUT transfers, no/few IN transfers, and is not a program account
                    let is_out_only = counter.out_transfers >= self.min_one_way_transfers
                        && counter.in_transfers < self.min_transfers
                        && !should_exclude;

                    // Include if it's either in-only or out-only
                    is_in_only || is_out_only
                })
                .map(|(k, v)| (k.clone(), v.clone()))
                .collect();

            // Add one-way addresses to results
            for (address, counter) in filtered_addresses {
                let account_info = all_accounts.get(&address);
                let tags = account_info
                    .and_then(|acc| acc.account_tags.clone())
                    .unwrap_or_default();
                let label = account_info
                    .and_then(|acc| acc.account_label.clone())
                    .unwrap_or_default();

                associated_wallets.push(AssociatedWallet::new(
                    address,
                    counter.in_transfers,
                    counter.out_transfers,
                    false,
                    tags,
                    label,
                ));
            }
        }

        // Sort by total transfer count (descending)
        associated_wallets.sort_by(|a, b| {
            let a_total = a.total_transfers();
            let b_total = b.total_transfers();
            b_total.cmp(&a_total)
        });

        // Cache the results
        if let Err(e) = self.write_to_cache(wallet, &associated_wallets) {
            warn!("Failed to cache results for wallet {}: {}", wallet, e);
        }

        Ok(associated_wallets)
    }
}

pub async fn analyze_wallet_clusters(
    wallets: &[String],
    token: &str,
    max_pages: usize,
    min_transfers: usize,
    include_one_way: bool,
    min_one_way_transfers: usize,
    include_token_creators: bool,
) -> Result<Vec<Vec<String>>> {
    let analyzer = WalletAnalyzer::new(
        token.to_string(),
        max_pages,
        min_transfers,
        include_one_way,
        min_one_way_transfers,
        include_token_creators,
    )?;

    // Create a graph of wallet relationships
    let mut wallet_transfers: HashMap<String, HashMap<String, TransferCounter>> = HashMap::new();
    let mut associated_wallets_map: HashMap<String, Vec<AssociatedWallet>> = HashMap::new();

    // Process wallets in parallel with a maximum of 5 concurrent tasks
    let semaphore = std::sync::Arc::new(tokio::sync::Semaphore::new(6));
    let mut tasks = Vec::new();

    // Create a shared error flag using Arc<AtomicBool>
    let error_occurred = std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false));

    // Create tasks for each wallet
    for wallet in wallets {
        let wallet_clone = wallet.clone();
        let analyzer_clone = analyzer.clone();
        let semaphore_clone = semaphore.clone();
        let error_flag = error_occurred.clone();

        let task = tokio::spawn(async move {
            let _permit = semaphore_clone.acquire().await.unwrap();
            // Check if an error has already occurred in another task
            if error_flag.load(std::sync::atomic::Ordering::Relaxed) {
                return Ok((String::new(), Vec::new(), HashMap::new())); // Early return if error occurred
            }
            info!("Analyzing wallet: {}", wallet_clone);

            // Get associated wallets
            match analyzer_clone.analyze_wallet(&wallet_clone).await {
                Ok(associated) => {
                    // Track all transfers for this wallet
                    let mut transfers_map: HashMap<String, TransferCounter> = HashMap::new();
                    for assoc in associated.clone() {
                        // Skip wallets with specific labels
                        let label_lowercase = assoc.label.to_lowercase();
                        if label_lowercase.contains("jito")
                            || label_lowercase.contains("wintermute")
                            || label_lowercase.contains("binance")
                            || label_lowercase.contains("mexc")
                            || label_lowercase.contains("bitget")
                            || label_lowercase.contains("gate")
                            || label_lowercase.contains("kraken")
                            || label_lowercase.contains("okx")
                            || label_lowercase.contains("coinbase")
                            || label_lowercase.contains("bybit")
                            || label_lowercase.contains("phantom")
                            || label_lowercase.contains("solfi")
                            || label_lowercase.contains("pump.fun")
                        {
                            continue;
                        }

                        transfers_map.insert(
                            assoc.address,
                            TransferCounter {
                                in_transfers: assoc.in_transfers,
                                out_transfers: assoc.out_transfers,
                            },
                        );
                    }

                    Ok((wallet_clone, associated, transfers_map))
                }
                Err(e) => {
                    // Set the error flag to signal other tasks to stop
                    warn!("Error analyzing wallet: {}", e);
                    error_flag.store(true, std::sync::atomic::Ordering::Relaxed);
                    Err(e)
                }
            }
        });

        tasks.push(task);
    }

    // Collect results from all tasks
    for task in futures::future::join_all(tasks).await {
        match task {
            Ok(result) => match result {
                Ok((wallet, associated, transfers_map)) => {
                    // Skip empty results from early returns due to errors
                    if !wallet.is_empty() {
                        associated_wallets_map.insert(wallet.clone(), associated);
                        wallet_transfers.insert(wallet, transfers_map);
                    }
                }
                Err(e) => {
                    return Err(e);
                }
            },
            Err(e) => {
                return Err(anyhow!("Task failed: {}", e));
            }
        }
    }

    // Check if any errors occurred
    if error_occurred.load(std::sync::atomic::Ordering::Relaxed) {
        warn!("Analysis stopped due to an error in one of the tasks");
        return Err(anyhow!(
            "Analysis stopped due to an error in one of the tasks"
        ));
    }

    // Create a graph of all wallet relationships (including intermediate wallets)
    let mut wallet_associations: HashMap<String, HashSet<String>> = HashMap::new();

    // First, build the complete graph of all wallet relationships
    for (wallet, associated) in &associated_wallets_map {
        let mut associations = HashSet::new();

        // Add all associated wallets to the graph, regardless of whether they're in the input list
        for assoc_wallet in associated {
            associations.insert(assoc_wallet.address.clone());

            // Add the reverse association (make it bidirectional)
            wallet_associations
                .entry(assoc_wallet.address.clone())
                .or_insert_with(HashSet::new)
                .insert(wallet.clone());
        }

        if !associations.is_empty() {
            wallet_associations.insert(wallet.clone(), associations);
        }
    }

    // Add one-way transfers if enabled
    if include_one_way {
        debug!("Checking for one-way transfers...");

        for (from_wallet, transfers) in &wallet_transfers {
            let mut associations = HashSet::new();

            for (to_wallet, counter) in transfers {
                if counter.in_transfers >= min_one_way_transfers
                    || counter.out_transfers >= min_one_way_transfers
                {
                    associations.insert(to_wallet.clone());
                    debug!(
                        "One-way transfer association: {} -> {} (in: {}, out: {})",
                        from_wallet, to_wallet, counter.in_transfers, counter.out_transfers
                    );

                    // Also add the reverse association (make it bidirectional)
                    wallet_associations
                        .entry(to_wallet.clone())
                        .or_insert_with(HashSet::new)
                        .insert(from_wallet.clone());
                }
            }

            if !associations.is_empty() {
                // Update the existing associations or insert new ones
                wallet_associations
                    .entry(from_wallet.clone())
                    .or_insert_with(HashSet::new)
                    .extend(associations);
            }
        }
    }

    // Find clusters with the associations we've built
    let clusters = find_clusters(&wallet_associations, wallets);
    debug!("Found {} clusters", clusters.len());

    Ok(clusters)
}

// Function to find clusters of wallets
fn find_clusters(
    associations: &HashMap<String, HashSet<String>>,
    all_wallets: &[String],
) -> Vec<Vec<String>> {
    let mut clusters: Vec<Vec<String>> = Vec::new();
    let mut visited: HashSet<String> = HashSet::new();
    let input_wallets: HashSet<&String> = all_wallets.iter().collect();

    for wallet in all_wallets {
        if visited.contains(wallet) {
            continue;
        }

        let mut cluster = Vec::new();
        let mut queue = vec![wallet.clone()];
        visited.insert(wallet.clone());

        while let Some(current) = queue.pop() {
            // Only add wallets that are in the input list
            if input_wallets.contains(&current) {
                cluster.push(current.clone());
            }

            let mut neighbor_in_input = 0;
            if let Some(neighbors) = associations.get(&current) {
                for neighbor in neighbors {
                    if input_wallets.contains(neighbor) {
                        neighbor_in_input += 1;
                    }
                    if !visited.contains(neighbor) {
                        queue.push(neighbor.clone());
                        visited.insert(neighbor.clone());
                    }
                }
            }
            if neighbor_in_input > 3 {
                warn!(
                    "Wallet {} has {} neighbors in the input list",
                    current, neighbor_in_input
                );
            }
        }

        // Only include clusters with more than one wallet from the input list
        if cluster.len() > 1 {
            clusters.push(cluster);
        }
    }

    clusters
}

// Function to check if a wallet address is a valid Ed25519 public key on the curve
fn is_on_curve(wallet: &str) -> bool {
    match bs58::decode(wallet).into_vec() {
        Ok(bytes) => {
            if bytes.len() != 32 {
                return false;
            }
            // Check if the public key is on the curve
            let bytes_array: [u8; 32] = bytes.try_into().unwrap_or_else(|_| [0; 32]);
            match ed25519_dalek::VerifyingKey::from_bytes(&bytes_array) {
                Ok(_) => true,
                Err(_) => false,
            }
        }
        Err(_) => false,
    }
}

// Change the static declaration to use Arc<Mutex>
static LAST_SLACK_NOTIFICATION: Lazy<Arc<Mutex<Option<Instant>>>> =
    Lazy::new(|| Arc::new(Mutex::new(None)));

pub async fn send_slack_notification(_message: &str) -> Result<()> {
    //     let last_notification = LAST_SLACK_NOTIFICATION.clone();
    //     let should_send = {
    //         let mut last_notification = last_notification.lock().unwrap();
    //         let now = Instant::now();
    //         let should_send = match *last_notification {
    //             None => true,
    //             Some(last) => now.duration_since(last) >= Duration::from_secs(30),
    //         };
    //         if should_send {
    //             *last_notification = Some(now);
    //         }
    //         should_send
    //     };
    //
    //     if !should_send {
    //         return Ok(());
    //     }
    //
    //     let webhook_url =
    //         "*******************************************************************************";
    //     let client = Client::new();
    //
    //     let payload = serde_json::json!({
    //         "text": message
    //     });
    //
    //     match client.post(webhook_url).json(&payload).send().await {
    //         Ok(_) => {
    //             debug!("Slack notification sent successfully");
    //         }
    //         Err(e) => warn!("Failed to send Slack notification: {}", e),
    //     }
    //
    //     Ok(())
    Ok(())
}

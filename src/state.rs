use crate::telegram::TelegramBot;
use anyhow::Result;
use redis::{Client, Commands, Connection, RedisResult, Value};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;

#[derive(Debug, Serialize, Deserialize)]
pub struct WalletInfo {
    pub address: String,
    pub added_at: u64,
    pub last_balance: u64,
}

#[derive(Clone)]
pub struct AppState {
    pub redis: Arc<RedisState>,
    pub telegram_bot: Option<Arc<TelegramBot>>,
}

pub struct RedisState {
    client: Client,
}

impl RedisState {
    pub fn new(host: &str, port: u16) -> Result<Self> {
        let client = Client::open(format!("redis://{}:{}", host, port))?;
        Ok(Self { client })
    }

    pub fn get_connection(&self) -> Result<Connection> {
        Ok(self.client.get_connection()?)
    }

    pub fn get_wallets(&self) -> Result<Vec<WalletInfo>> {
        let mut conn = self.get_connection()?;
        let wallets: HashMap<String, String> = conn.hgetall("wallets")?;
        wallets
            .values()
            .map(|w| serde_json::from_str(w))
            .collect::<Result<Vec<_>, _>>()
            .map_err(Into::into)
    }

    pub fn update_wallets(&self, wallets: &[String]) -> Result<()> {
        let mut conn = self.get_connection()?;
        let mut pipe = redis::pipe();

        // Clear existing wallets
        pipe.del("wallets");

        // Add new wallets
        for wallet in wallets {
            let info = WalletInfo {
                address: wallet.clone(),
                added_at: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)?
                    .as_secs(),
                last_balance: 0,
            };
            pipe.hset("wallets", wallet, serde_json::to_string(&info)?);
        }

        let _: Vec<Value> = pipe.query(&mut conn)?;
        Ok(())
    }

    pub fn remove_wallet(&self, wallet: &str) -> Result<()> {
        let mut conn = self.get_connection()?;
        let _: RedisResult<i32> = conn.hdel("wallets", wallet);
        Ok(())
    }
}

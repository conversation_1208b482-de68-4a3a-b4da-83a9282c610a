use std::collections::HashMap;

use log::{debug, info, warn};

// Shared state for active trades
pub struct ActiveTrades {
    pub trades: HashMap<String, Vec<TargetWalletInfo>>,
}

// Information about a target wallet trading a token
#[derive(Clone)]
pub struct TargetWalletInfo {
    pub wallet: String,
    pub highest_balance: u64,  // Highest balance observed for this wallet
    pub current_balance: u64,  // Current balance of this wallet
    pub my_bought_amount: u64, // Amount of tokens our wallet has bought
    pub total_cost_sol: f64,   // Total cost of tokens our wallet has bought in SOL
}

impl ActiveTrades {
    pub fn new() -> Self {
        Self {
            trades: HashMap::new(),
        }
    }

    pub fn is_token_trading(&self, token_mint: &str, wallet: &str) -> bool {
        if let Some(wallet_infos) = self.trades.get(token_mint) {
            wallet_infos.iter().any(|info| info.wallet == wallet)
        } else {
            false
        }
    }

    pub fn get_highest_balance(&self, token_mint: &str, wallet: &str) -> u64 {
        if let Some(wallet_infos) = self.trades.get(token_mint) {
            wallet_infos
                .iter()
                .find(|info| info.wallet == wallet)
                .map_or(0, |info| info.highest_balance)
        } else {
            warn!(
                "get_highest_balance: No trade found for token: {}",
                token_mint
            );
            0
        }
    }

    pub fn update_highest_balance(&mut self, token_mint: &str, wallet: &str, amount: u64) {
        if let Some(wallet_infos) = self.trades.get_mut(token_mint) {
            if let Some(info) = wallet_infos.iter_mut().find(|info| info.wallet == wallet) {
                if amount > info.highest_balance {
                    info.highest_balance = amount;
                }
            } else {
                warn!(
                    "update_highest_balance: No wallet found for token: {} {}",
                    token_mint, wallet
                );
            }
        } else {
            warn!(
                "update_highest_balance: No trade found for token: {}",
                token_mint
            );
        }
    }

    pub fn get_my_bought_amount(&self, token_mint: &str, wallet: &str) -> u64 {
        if let Some(wallet_infos) = self.trades.get(token_mint) {
            wallet_infos
                .iter()
                .find(|info| info.wallet == wallet)
                .map_or(0, |info| info.my_bought_amount)
        } else {
            warn!(
                "get_my_bought_amount: No trade found for token: {}",
                token_mint
            );
            0
        }
    }

    pub fn get_total_cost_sol(&self, token_mint: &str, wallet: &str) -> f64 {
        if let Some(wallet_infos) = self.trades.get(token_mint) {
            wallet_infos
                .iter()
                .find(|info| info.wallet == wallet)
                .map_or(0.0, |info| info.total_cost_sol)
        } else {
            0.0
        }
    }

    // return whether it's newly tracked wallet
    pub fn add_wallet_to_token(&mut self, token_mint: String, wallet: String, amount: u64) -> bool {
        debug!(
            "Adding wallet {} to token: {}, amount: {}",
            wallet, token_mint, amount
        );
        let wallet_infos = self
            .trades
            .entry(token_mint.clone())
            .or_insert_with(Vec::new);

        // Check if the wallet is already being tracked for this token
        if let Some(wallet_info) = wallet_infos.iter_mut().find(|info| info.wallet == wallet) {
            // If wallet buys more, this is a new buy - reset highest_balance to current
            if amount > wallet_info.current_balance {
                wallet_info.highest_balance = amount; // Reset highest to current on new buy
            }
            wallet_info.current_balance = amount;
            info!(
                "Wallet {} already tracked in {}, only update balance with {}",
                wallet, token_mint, amount
            );
            false
        } else {
            wallet_infos.push(TargetWalletInfo {
                wallet: wallet.clone(),
                highest_balance: amount,
                current_balance: amount,
                my_bought_amount: 0,
                total_cost_sol: 0.0,
            });
            info!("Added wallet {} to token: {}", wallet, token_mint);
            true
        }
    }

    pub fn add_my_bought_info(
        &mut self,
        token_mint: &str,
        wallet: &str,
        amount: u64,
        cost_sol: f64,
    ) {
        debug!(
            "Adding my bought info to token: {}, wallet: {}, amount: {}, cost_sol: {}",
            token_mint, wallet, amount, cost_sol
        );
        if let Some(wallet_infos) = self.trades.get_mut(token_mint) {
            if let Some(info) = wallet_infos.iter_mut().find(|info| info.wallet == wallet) {
                info.my_bought_amount += amount;
                info.total_cost_sol += cost_sol;
            }
        } else {
            warn!(
                "add_my_bought_info: No trade found for token: {}",
                token_mint
            );
        }
    }

    pub fn remove_wallet_from_token(&mut self, token_mint: &str, wallet: &str) {
        info!("Removing wallet {} from token: {}", wallet, token_mint);
        if let Some(wallet_infos) = self.trades.get_mut(token_mint) {
            wallet_infos.retain(|info| info.wallet != wallet);
            if wallet_infos.is_empty() {
                self.trades.remove(token_mint);
                info!("Removed token: {}", token_mint);
            }
        } else {
            warn!(
                "remove_wallet_from_token: No trade found for token: {}",
                token_mint
            );
        }
    }
}

#[cfg(test)]
mod tests {
    use std::sync::{Arc, Mutex};

    use super::*;

    #[test]
    fn test_new() {
        let active_trades = ActiveTrades::new();
        assert!(active_trades.trades.is_empty());
    }

    #[test]
    fn test_is_token_trading() {
        let mut active_trades = ActiveTrades::new();

        // Empty trades map
        assert!(!active_trades.is_token_trading("token1", "wallet1"));

        // Add a wallet to a token
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);

        // Now it should return true
        assert!(active_trades.is_token_trading("token1", "wallet1"));

        // Different token or wallet should return false
        assert!(!active_trades.is_token_trading("token2", "wallet1"));
        assert!(!active_trades.is_token_trading("token1", "wallet2"));
    }

    #[test]
    fn test_get_highest_balance() {
        let mut active_trades = ActiveTrades::new();

        // Empty trades map should return 0
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 0);

        // Add a wallet with some balance
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);

        // Should return the balance we set
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 100);

        // Different token or wallet should return 0
        assert_eq!(active_trades.get_highest_balance("token2", "wallet1"), 0);
        assert_eq!(active_trades.get_highest_balance("token1", "wallet2"), 0);
    }

    #[test]
    fn test_update_highest_balance() {
        let mut active_trades = ActiveTrades::new();

        // Update on empty trades map should do nothing
        active_trades.update_highest_balance("token1", "wallet1", 200);
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 0);

        // First, add a wallet with some balance
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 100);

        // Update to a higher value should work
        active_trades.update_highest_balance("token1", "wallet1", 200);
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 200);

        // Update to a lower value should not change highest_balance
        active_trades.update_highest_balance("token1", "wallet1", 150);
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 200);

        // Update for different token or wallet should do nothing
        active_trades.update_highest_balance("token2", "wallet1", 300);
        assert_eq!(active_trades.get_highest_balance("token2", "wallet1"), 0);
        active_trades.update_highest_balance("token1", "wallet2", 300);
        assert_eq!(active_trades.get_highest_balance("token1", "wallet2"), 0);
    }

    #[test]
    fn test_get_my_bought_amount() {
        let mut active_trades = ActiveTrades::new();

        // Empty trades map should return 0
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet1"), 0);

        // Add a wallet, my_bought_amount starts at 0
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet1"), 0);

        // Add my bought info
        active_trades.add_my_bought_info("token1", "wallet1", 50, 1.0);
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet1"), 50);

        // Different token or wallet should return 0
        assert_eq!(active_trades.get_my_bought_amount("token2", "wallet1"), 0);
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet2"), 0);
    }

    #[test]
    fn test_get_total_cost_sol() {
        let mut active_trades = ActiveTrades::new();

        // Empty trades map should return 0.0
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet1"), 0.0);

        // Add a wallet, total_cost_sol starts at 0.0
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet1"), 0.0);

        // Add my bought info
        active_trades.add_my_bought_info("token1", "wallet1", 50, 1.5);
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet1"), 1.5);

        // Add more
        active_trades.add_my_bought_info("token1", "wallet1", 25, 0.8);
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet1"), 2.3);

        // Different token or wallet should return 0.0
        assert_eq!(active_trades.get_total_cost_sol("token2", "wallet1"), 0.0);
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet2"), 0.0);
    }

    #[test]
    fn test_add_wallet_to_token() {
        let mut active_trades = ActiveTrades::new();

        // Add a new wallet, should return true (newly added)
        assert!(active_trades.add_wallet_to_token(
            "token1".to_string(),
            "wallet1".to_string(),
            100
        ));
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 100);
        assert_eq!(active_trades.trades["token1"][0].current_balance, 100);

        // Add same wallet with higher balance, should return false (already existed)
        assert!(!active_trades.add_wallet_to_token(
            "token1".to_string(),
            "wallet1".to_string(),
            200
        ));
        // Highest should be reset to current on new buy
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 200);
        assert_eq!(active_trades.trades["token1"][0].current_balance, 200);

        // Add same wallet with lower balance
        assert!(!active_trades.add_wallet_to_token(
            "token1".to_string(),
            "wallet1".to_string(),
            150
        ));
        // Highest should not change (since not a new buy, current < highest)
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 200);
        assert_eq!(active_trades.trades["token1"][0].current_balance, 150);

        // Add different wallet to same token
        assert!(active_trades.add_wallet_to_token(
            "token1".to_string(),
            "wallet2".to_string(),
            300
        ));
        assert_eq!(active_trades.trades["token1"].len(), 2);
        assert_eq!(active_trades.get_highest_balance("token1", "wallet2"), 300);

        // Add wallet to different token
        assert!(active_trades.add_wallet_to_token(
            "token2".to_string(),
            "wallet1".to_string(),
            400
        ));
        assert!(active_trades.trades.contains_key("token2"));
        assert_eq!(active_trades.get_highest_balance("token2", "wallet1"), 400);
    }

    #[test]
    fn test_add_my_bought_info() {
        let mut active_trades = ActiveTrades::new();

        // Add on empty trades map should do nothing, no panic
        active_trades.add_my_bought_info("token1", "wallet1", 50, 1.0);

        // Add a wallet
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);

        // Add bought info
        active_trades.add_my_bought_info("token1", "wallet1", 50, 1.5);
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet1"), 50);
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet1"), 1.5);

        // Add more, should accumulate
        active_trades.add_my_bought_info("token1", "wallet1", 25, 0.8);
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet1"), 75);
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet1"), 2.3);

        // Adding to nonexistent wallet should do nothing
        active_trades.add_my_bought_info("token1", "wallet2", 100, 3.0);
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet2"), 0);
    }

    #[test]
    fn test_remove_wallet_from_token() {
        let mut active_trades = ActiveTrades::new();

        // Remove from empty trades map should do nothing, no panic
        active_trades.remove_wallet_from_token("token1", "wallet1");

        // Add two wallets to a token
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);
        active_trades.add_wallet_to_token("token1".to_string(), "wallet2".to_string(), 200);

        // Remove one wallet
        active_trades.remove_wallet_from_token("token1", "wallet1");
        assert!(!active_trades.is_token_trading("token1", "wallet1"));
        assert!(active_trades.is_token_trading("token1", "wallet2"));

        // Remove last wallet, token should be removed too
        active_trades.remove_wallet_from_token("token1", "wallet2");
        assert!(!active_trades.is_token_trading("token1", "wallet2"));
        assert!(!active_trades.trades.contains_key("token1"));
    }

    #[test]
    fn test_string_vs_str_references() {
        let mut active_trades = ActiveTrades::new();

        // Add using String
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);

        // Check using &str
        assert!(active_trades.is_token_trading("token1", "wallet1"));
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 100);

        // Update using &str
        active_trades.update_highest_balance("token1", "wallet1", 200);

        // Verify using &str
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 200);

        // Add info using &str
        active_trades.add_my_bought_info("token1", "wallet1", 50, 1.5);

        // Verify using &str
        assert_eq!(active_trades.get_my_bought_amount("token1", "wallet1"), 50);
        assert_eq!(active_trades.get_total_cost_sol("token1", "wallet1"), 1.5);

        // Check string equality
        let token_string = "token1".to_string();
        let wallet_string = "wallet1".to_string();

        assert!(active_trades.is_token_trading(&token_string, &wallet_string));
        assert_eq!(
            active_trades.get_highest_balance(&token_string, &wallet_string),
            200
        );
    }

    #[test]
    fn test_string_with_whitespace() {
        let mut active_trades = ActiveTrades::new();

        // Add with trailing space
        active_trades.add_wallet_to_token("token1 ".to_string(), "wallet1".to_string(), 100);

        // Check without trailing space - should fail
        assert!(!active_trades.is_token_trading("token1", "wallet1"));

        // Check with trailing space - should work
        assert!(active_trades.is_token_trading("token1 ", "wallet1"));

        // Similar test for wallet
        active_trades.add_wallet_to_token("token2".to_string(), "wallet2 ".to_string(), 200);
        assert!(!active_trades.is_token_trading("token2", "wallet2"));
        assert!(active_trades.is_token_trading("token2", "wallet2 "));
    }

    #[test]
    fn test_update_existing_wallet() {
        let mut active_trades = ActiveTrades::new();

        // Add a wallet
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);

        // Update directly in a separate scope to verify our changes stick
        {
            active_trades.update_highest_balance("token1", "wallet1", 200);
        }

        // Verify the change persisted
        assert_eq!(active_trades.get_highest_balance("token1", "wallet1"), 200);
    }

    #[test]
    fn test_mutex_arc_behavior() {
        // This test simulates the Arc<Mutex<>> behavior
        let active_trades = Arc::new(Mutex::new(ActiveTrades::new()));

        // First, add a wallet
        {
            let mut guard = active_trades.lock().unwrap();
            guard.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);
        }

        // Clone the Arc and update through clone
        let active_trades_clone = active_trades.clone();
        {
            let mut guard = active_trades_clone.lock().unwrap();
            guard.update_highest_balance("token1", "wallet1", 200);
        }

        // Verify update is visible through original Arc
        {
            let guard = active_trades.lock().unwrap();
            assert_eq!(guard.get_highest_balance("token1", "wallet1"), 200);
        }

        // Add another wallet through original
        {
            let mut guard = active_trades.lock().unwrap();
            guard.add_wallet_to_token("token1".to_string(), "wallet2".to_string(), 300);
        }

        // Verify through clone
        {
            let guard = active_trades_clone.lock().unwrap();
            assert_eq!(guard.get_highest_balance("token1", "wallet2"), 300);
        }
    }

    #[test]
    fn test_update_and_remove() {
        // This test checks if update works correctly after removing a wallet
        let mut active_trades = ActiveTrades::new();

        // Add two wallets
        active_trades.add_wallet_to_token("token1".to_string(), "wallet1".to_string(), 100);
        active_trades.add_wallet_to_token("token1".to_string(), "wallet2".to_string(), 200);

        // Remove one
        active_trades.remove_wallet_from_token("token1", "wallet1");

        // Update the remaining one
        active_trades.update_highest_balance("token1", "wallet2", 300);

        // Verify it worked
        assert_eq!(active_trades.get_highest_balance("token1", "wallet2"), 300);

        // Remove all wallets for token
        active_trades.remove_wallet_from_token("token1", "wallet2");

        // Try to update - should do nothing and not panic
        active_trades.update_highest_balance("token1", "wallet2", 400);

        // Re-add wallet
        active_trades.add_wallet_to_token("token1".to_string(), "wallet2".to_string(), 500);

        // Verify the new value
        assert_eq!(active_trades.get_highest_balance("token1", "wallet2"), 500);
    }
}

use alloy::{
    primitives::{Address, U256},
    providers::Provider,
};
use anyhow::{anyhow, Result};
use log::info;
use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;
use spl_associated_token_account::get_associated_token_address;
use std::{collections::HashMap, str::FromStr as _};

use crate::{
    abis, get_latest_token_buy_info, get_raydium_price_provider, get_sol_price,
    get_token_account_balance, get_token_info, ApiClient, PoolBuyInfo, PriceProvider,
    PumpFunAmmPool, PumpFunPool, RaydiumPoolType, UpsertBuySignalReq,
};

/// Determine the pool type (PumpFun or Raydium) and get the pool pubkey
/// for a token mint.
///
/// Returns a tuple of (is_pump, pool_pubkey)
pub async fn determine_pool_type(
    client: &RpcClient,
    token_mint: &Pubkey,
) -> Result<(bool, Pubkey)> {
    let latest_buy_info = get_latest_token_buy_info(client, token_mint).await?;

    match latest_buy_info {
        PoolBuyInfo::PumpFun(info) => Ok((true, info.bonding_curve)),
        PoolBuyInfo::Raydium(info) => Ok((false, info.pool_pubkey)),
        _ => Err(anyhow!("Unsupported pool type for token {}", token_mint)),
    }
}

/// Get the appropriate pool implementation based on the pool type and pubkey
pub fn get_pool_implementation(
    client: &RpcClient,
    pool_pubkey: &Pubkey,
    is_pump: bool,
    is_pump_amm: bool,
    token_mint: Option<&Pubkey>,
) -> Box<dyn PriceProvider> {
    if is_pump {
        if let Some(token_mint) = token_mint {
            Box::new(PumpFunPool {
                token_mint: *token_mint,
            })
        } else {
            // This should not happen in practice, but we need to handle it
            Box::new(PumpFunPool {
                token_mint: Pubkey::default(),
            })
        }
    } else if is_pump_amm {
        if let Some(token_mint) = token_mint {
            Box::new(PumpFunAmmPool {
                token_mint: *token_mint,
                pool_pubkey: *pool_pubkey,
            })
        } else {
            // This should not happen in practice, but we need to handle it
            Box::new(PumpFunAmmPool {
                token_mint: Pubkey::default(),
                pool_pubkey: Pubkey::default(),
            })
        }
    } else {
        get_raydium_price_provider(client, pool_pubkey, &RaydiumPoolType::OpenBook).unwrap()
    }
}

/// Get the price of a token in USD
pub async fn get_token_price_usd(
    client: &RpcClient,
    pool_pubkey: &Pubkey,
    is_pump: bool,
    is_pump_amm: bool,
    token_mint: Option<&Pubkey>,
) -> Result<f64> {
    // Get pool implementation
    let pool_impl = get_pool_implementation(client, pool_pubkey, is_pump, is_pump_amm, token_mint);

    // Get price in SOL
    let price_in_sol = pool_impl.get_price(client, pool_pubkey).await?;

    // Get SOL price in USD
    let sol_price = get_sol_price().await.unwrap_or(0.0);

    // Calculate price in USD
    Ok(price_in_sol * sol_price)
}

/// Format a buy signal request for API
pub fn format_buy_signal_request(
    token_mint: &str,
    smart_wallet_count: i32,
    price_usd: f64,
    avg_ratio: f64,
    avg_win_rate: f64,
    price_change: f64,
    telegram_link: String,
    emit_time: i64,
    highest_price: f64,
    symbol: String,
) -> UpsertBuySignalReq {
    UpsertBuySignalReq {
        token_address: token_mint.to_string(),
        smart_wallet_count,
        buy_entry_price: price_usd,
        emit_time,
        telegram_link,
        win_rate: ApiClient::get_win_rate_v3(avg_ratio, avg_win_rate, price_change),
        average_holding: (avg_ratio * 100.0).round() / 100.0,
        average_win_rate: (avg_win_rate * 100.0).round() / 100.0,
        highest_price,
        symbol,
    }
}

/// Calculate holding ratio for a wallet
/// Returns the ratio of current amount to max amount (0.0 to 1.0)
pub fn calculate_holding_ratio(current_amount: u64, max_amount: u64) -> f64 {
    if max_amount == 0 {
        return 0.0;
    }
    current_amount as f64 / max_amount as f64
}

/// Get token account balance for a wallet and token mint
pub fn get_wallet_token_balance(
    client: &RpcClient,
    wallet: &Pubkey,
    token_mint: &Pubkey,
) -> Result<u64> {
    let token_account = get_associated_token_address(wallet, token_mint);
    get_token_account_balance(client, &token_account)
}

/// Calculate price change ratio
pub fn calculate_price_change(current_price: f64, entry_price: f64) -> f64 {
    if entry_price > 0.0 {
        (current_price - entry_price) / entry_price
    } else {
        0.0
    }
}

/// Get token info (symbol and decimals)
pub async fn get_token_metadata(client: &RpcClient, token_mint: &Pubkey) -> Result<(String, u8)> {
    get_token_info(client, token_mint).await
}

/// Extract basic information from a PoolBuyInfo structure
/// Returns (token_mint, sol_amount, pool_sol_amount, token_amount)
pub fn extract_pool_buy_info_details(buy_info: &PoolBuyInfo) -> Option<(String, f64, f64, u64)> {
    match buy_info {
        PoolBuyInfo::Raydium(info) => {
            let sol_amount = info.sol_amount as f64 / 1e9;
            let pool_sol = info.pool_sol_amount as f64 / 1e9;
            let token_amount = info.token_amount;
            Some((info.token_mint.clone(), sol_amount, pool_sol, token_amount))
        }
        PoolBuyInfo::PumpFun(info) => {
            let sol_amount = info.sol_amount as f64 / 1e9;
            let pool_sol = info.pool_sol_amount as f64 / 1e9;
            let token_amount = info.token_amount;
            Some((info.token_mint.clone(), sol_amount, pool_sol, token_amount))
        }
        PoolBuyInfo::PumpFunAmm(info) => {
            let sol_amount = info.sol_amount as f64 / 1e9;
            let pool_sol = info.pool_sol_amount as f64 / 1e9;
            let token_amount = info.token_amount;
            Some((info.token_mint.clone(), sol_amount, pool_sol, token_amount))
        }
        _ => None,
    }
}

/// Update pool information and caches
/// Returns (is_pump, pool_pubkey) if successful
pub async fn update_pool_info(
    client: &RpcClient,
    token_mint: &str,
    token_mint_pubkey: &Pubkey,
    token_status_cache: &mut HashMap<String, (bool, bool)>,
    token_pool_pubkey_cache: &mut HashMap<String, Pubkey>,
) -> Result<(bool, bool, Pubkey)> {
    info!("Getting latest buy info for token {}", token_mint);
    let latest_buy_info = get_latest_token_buy_info(client, token_mint_pubkey).await?;

    let (is_pump, is_pump_amm, pool_pubkey) = match latest_buy_info {
        PoolBuyInfo::PumpFun(info) => {
            info!("Got latest buy info for token {}: PumpFun pool", token_mint);
            (true, false, info.bonding_curve)
        }
        PoolBuyInfo::Raydium(info) => {
            info!("Got latest buy info for token {}: Raydium pool", token_mint);
            (false, false, info.pool_pubkey)
        }
        PoolBuyInfo::PumpFunAmm(info) => {
            info!(
                "Got latest buy info for token {}: PumpFun Amm pool",
                token_mint
            );
            (false, true, info.pool_pubkey)
        }
        _ => {
            return Err(anyhow!("Unsupported pool type for token {}", token_mint));
        }
    };

    // Update caches
    token_status_cache.insert(token_mint.to_string(), (is_pump, is_pump_amm));
    token_pool_pubkey_cache.insert(token_mint.to_string(), pool_pubkey);

    Ok((is_pump, is_pump_amm, pool_pubkey))
}

/// Check if a token trade meets basic criteria for processing
/// Returns true if the trade should be processed, false otherwise
pub fn should_process_trade(
    token_mint: &str,
    sol_amount: f64,
    pool_sol_amount: f64,
    wsol_mint: &str,
) -> bool {
    if sol_amount < 0.98 || pool_sol_amount < 40.0 || token_mint == wsol_mint {
        return false;
    }
    pool_sol_amount <= 1000.0 || sol_amount / pool_sol_amount > 0.01
}

/// Calculate token price in USD based on the amount spent and tokens received
pub fn calculate_token_price_usd(
    sol_amount: f64,
    token_amount: u64,
    decimal: i32,
    sol_price: f64,
) -> f64 {
    let price_by_sol = sol_amount / (token_amount as f64 / 10f64.powi(decimal));
    price_by_sol * sol_price
}

/// Get the current token price from the pool, handling all pool types and error cases
/// Returns the token price in USD if successful
pub async fn get_current_token_price(
    client: &RpcClient,
    token_mint_pubkey: &Pubkey,
    pool_pubkey: &Pubkey,
    is_pump: bool,
    is_pump_amm: bool,
    sol_price: f64,
) -> Result<f64> {
    // Get the appropriate pool implementation
    let pool_impl = get_pool_implementation(
        client,
        pool_pubkey,
        is_pump,
        is_pump_amm,
        Some(token_mint_pubkey),
    );

    // Get price in SOL from the pool
    let price_by_sol = pool_impl.get_price(client, pool_pubkey).await?;

    // Calculate token price in USD
    let current_price = price_by_sol * sol_price;

    Ok(current_price)
}

/// Get balances for multiple token accounts at once
/// Returns a HashMap mapping Pubkey to token balance
pub fn get_multiple_token_balances(
    client: &RpcClient,
    token_accounts: &[Pubkey],
) -> Result<HashMap<Pubkey, u64>> {
    let mut balances = HashMap::new();

    // Initialize all accounts with 0 balance by default
    for &token_account in token_accounts {
        balances.insert(token_account, 0);
    }

    // Process token accounts in chunks of 100 to avoid RPC limits
    for chunk in token_accounts.chunks(100) {
        let accounts = client.get_multiple_accounts(chunk)?;

        for (i, account_opt) in accounts.iter().enumerate() {
            if i >= chunk.len() {
                break;
            }

            let token_account = chunk[i];
            if let Some(account) = account_opt {
                if account.data.len() == 165 {
                    // Extract amount at offset 64 (8 bytes for u64)
                    let amount_bytes = &account.data[64..72];
                    let balance = u64::from_le_bytes(amount_bytes.try_into().unwrap_or([0; 8]));
                    balances.insert(token_account, balance);
                }
            }
        }
    }

    Ok(balances)
}

/// Get multiple token account balances for a list of wallets and a token mint
/// Returns a HashMap mapping wallet Pubkey to token balance
pub async fn get_multiple_wallet_token_balances(
    client: &RpcClient,
    wallets: &[Pubkey],
    token_mint: &Pubkey,
) -> Result<HashMap<Pubkey, u64>> {
    // Get associated token accounts for each wallet
    let token_accounts: Vec<Pubkey> = wallets
        .iter()
        .map(|wallet| get_associated_token_address(wallet, token_mint))
        .collect();

    // Get the RPC URL and clone the token accounts
    let rpc_url = client.url().to_string();
    let token_accounts_clone = token_accounts.clone();

    // Use spawn_blocking for the blocking RPC operation
    let account_balances = tokio::task::spawn_blocking(move || {
        // Create a new RPC client inside the blocking task
        let client = RpcClient::new(rpc_url);
        get_multiple_token_balances(&client, &token_accounts_clone)
    })
    .await??;

    // Map account balances back to wallet addresses
    let mut wallet_balances = HashMap::new();
    for (i, wallet) in wallets.iter().enumerate() {
        if i < token_accounts.len() {
            let token_account = &token_accounts[i];
            let balance = account_balances.get(token_account).copied().unwrap_or(0);
            wallet_balances.insert(*wallet, balance);
        }
    }

    Ok(wallet_balances)
}

/// Get SOL balances for multiple wallets
/// Returns a HashMap mapping wallet Pubkey to SOL balance
pub async fn get_multiple_wallet_balances(
    client: &RpcClient,
    wallets: &[Pubkey],
) -> Result<HashMap<Pubkey, u64>> {
    // Get the RPC URL
    let rpc_url = client.url().to_string();
    let wallets_clone = wallets.to_vec();
    // Use spawn_blocking for the blocking RPC operation
    let balances = tokio::task::spawn_blocking(move || {
        // Create a new RPC client inside the blocking task
        let client = RpcClient::new(rpc_url);
        let mut result = HashMap::new();

        // Process wallets in chunks of 100 to avoid RPC limits
        for chunk in wallets_clone.chunks(100) {
            if let Ok(accounts) = client.get_multiple_accounts(chunk) {
                for (i, account_opt) in accounts.iter().enumerate() {
                    if i >= chunk.len() {
                        break;
                    }
                    let wallet = chunk[i];
                    if let Some(account) = account_opt {
                        result.insert(wallet, account.lamports);
                    } else {
                        result.insert(wallet, 0);
                    }
                }
            }
        }
        result
    })
    .await?;

    Ok(balances)
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::commitment_config::CommitmentConfig;
    use std::str::FromStr;

    #[tokio::test(flavor = "multi_thread")]
    async fn test_get_pool_implementation() {
        // Set up RPC client
        let rpc_url = std::env::var("RPC_URL")
            .unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

        // Parse Pubkeys from strings
        let pool_pubkey = Pubkey::from_str("72RUevi63oP3wkyriqsrGW1KUKaGDzfwYhv2L3VvnzyJ").unwrap();
        let token_mint = Pubkey::from_str("D3xrZFSKxZHY4tEMMbjtrqsXzpBp9sYsrwkR12eWpump").unwrap();

        // Get pool implementation
        let pool_impl =
            get_pool_implementation(&client, &pool_pubkey, false, true, Some(&token_mint));

        // Get price
        match pool_impl.get_price(&client, &pool_pubkey).await {
            Ok(price_in_sol) => {
                println!("Price in SOL: {}", price_in_sol);

                // Get SOL price and calculate USD value
                match get_sol_price().await {
                    Ok(sol_price) => {
                        let price_in_usd = price_in_sol * sol_price;
                        println!("SOL price: ${}", sol_price);
                        println!("Price in USD: ${}", price_in_usd);
                    }
                    Err(e) => println!("Failed to get SOL price: {}", e),
                }
            }
            Err(e) => println!("Failed to get price: {}", e),
        }
    }

    #[tokio::test(flavor = "multi_thread")]
    async fn test_get_multiple_token_balances() {
        // Set up RPC client
        let rpc_url = std::env::var("RPC_URL")
            .unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
        let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

        // Parse token account Pubkeys from strings
        let token_accounts = vec![
            Pubkey::from_str("AqYw3RNvFPXGQxJ7isFrtjJ4cmYXX8mNU8DvFbsqp5Wb").unwrap(),
            Pubkey::from_str("5ofcj5zQRfrJgpe6n1NRBzdqjCYGvfMSZkucfnsk8t1S").unwrap(),
        ];

        // Get balances for the token accounts
        let account_balances = get_multiple_token_balances(&client, &token_accounts).unwrap();

        // Print balances for debugging
        for (account, balance) in &account_balances {
            println!("Account: {}, Balance: {}", account, balance);
        }
    }
}

// Helper function to get token balance
pub async fn get_evm_token_balance<T: Provider>(
    provider: T,
    token_address: &str,
    wallet_address: &str,
) -> Result<U256> {
    // Convert addresses from string to Address
    let token_addr = Address::from_str(token_address)?;
    let wallet_addr = Address::from_str(wallet_address)?;
    let contract = abis::ERC20::new(token_addr, provider);
    let balance = contract.balanceOf(wallet_addr).call().await?;
    Ok(balance._0)
}

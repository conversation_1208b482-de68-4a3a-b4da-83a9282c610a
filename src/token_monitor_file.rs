use anyhow::Result;
use log::{debug, error, info};
use std::collections::HashMap;
use std::fs::{self, OpenOptions};
use std::io::Write;
use std::path::Path;
use std::time::{Duration, SystemTime};

use crate::token_monitor::{TokenMonitorState, FOMO_MESSAGE_IDS, SENT_SIGNALS, TOKEN_MONITORS};

// Constants for persistence
pub const TOKEN_MONITORS_FILE: &str = "token_monitors.json";
pub const SOLD_TOKENS_CSV_FILE: &str = "sold_tokens.csv";
pub const SENT_SIGNALS_FILE: &str = "sent_signals.json";
pub const SAVE_INTERVAL_SECS: u64 = 10; // Save every 10 seconds

// Save TOKEN_MONITORS to disk
pub fn save_token_monitors() -> Result<()> {
    // Create a clone of the monitors while holding the lock
    let monitors: HashMap<String, TokenMonitorState>;
    {
        let token_monitors = TOKEN_MONITORS
            .lock()
            .map_err(|e| anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e))?;
        monitors = token_monitors.clone();
    }

    // Write to a temporary file first (now outside the lock)
    let temp_file = format!("{}.tmp", TOKEN_MONITORS_FILE);
    let json = serde_json::to_string_pretty(&monitors)?;
    fs::write(&temp_file, json)?;

    // Then rename to the actual file (more atomic)
    fs::rename(&temp_file, TOKEN_MONITORS_FILE)?;

    debug!("Saved {} token monitors to disk", monitors.len());
    Ok(())
}

// Load TOKEN_MONITORS from disk
pub fn load_token_monitors() -> Result<()> {
    if !Path::new(TOKEN_MONITORS_FILE).exists() {
        return Ok(());
    }

    let json = fs::read_to_string(TOKEN_MONITORS_FILE)?;
    let monitors: HashMap<String, TokenMonitorState> = serde_json::from_str(&json)?;

    // Acquire lock once to update TOKEN_MONITORS
    {
        let mut token_monitors = TOKEN_MONITORS
            .lock()
            .map_err(|e| anyhow::anyhow!("Failed to acquire TOKEN_MONITORS lock: {:?}", e))?;
        *token_monitors = monitors.clone();
    }

    // Update FOMO_MESSAGE_IDS
    if let Ok(mut message_ids) = FOMO_MESSAGE_IDS.lock() {
        for (key, value) in &monitors {
            message_ids.insert(
                key.clone(),
                value
                    .telegram_link
                    .split("/")
                    .last()
                    .unwrap()
                    .parse::<i32>()
                    .unwrap(),
            );
        }
    }

    info!("Loaded {} token monitors from disk", monitors.len());
    Ok(())
}

// Save SENT_SIGNALS to disk
pub fn save_sent_signals() -> Result<()> {
    // Create a clone of the sent signals while holding the lock
    let signals: HashMap<String, u8>;
    {
        let sent_signals = SENT_SIGNALS.clone();
        signals = sent_signals
            .iter()
            .map(|r| (r.key().clone(), *r.value()))
            .collect();
    }

    // Write to a temporary file first
    let temp_file = format!("{}.tmp", SENT_SIGNALS_FILE);
    let json = serde_json::to_string_pretty(&signals)?;
    fs::write(&temp_file, json)?;

    // Then rename to the actual file (more atomic)
    fs::rename(&temp_file, SENT_SIGNALS_FILE)?;

    debug!("Saved {} sent signals to disk", signals.len());
    Ok(())
}

// Load SENT_SIGNALS from disk
pub fn load_sent_signals() -> Result<()> {
    if !Path::new(SENT_SIGNALS_FILE).exists() {
        return Ok(());
    }

    let json = fs::read_to_string(SENT_SIGNALS_FILE)?;
    let signals: HashMap<String, u8> = serde_json::from_str(&json)?;
    info!("Loaded {} sent signals from disk", signals.len());

    // Clear existing signals and add loaded ones
    SENT_SIGNALS.clear();
    for (key, value) in signals {
        SENT_SIGNALS.insert(key, value);
    }

    Ok(())
}

// Start a background task to periodically save TOKEN_MONITORS and SENT_SIGNALS
pub async fn start_token_monitors_persistence() {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(SAVE_INTERVAL_SECS));
        loop {
            interval.tick().await;
            if let Err(e) = save_token_monitors() {
                error!("Failed to save token monitors: {:?}", e);
            }
            if let Err(e) = save_sent_signals() {
                error!("Failed to save sent signals: {:?}", e);
            }
        }
    });
}

// Record sold token data to CSV file
pub fn record_sold_token_to_csv(
    token_mint: &str,
    symbol: &str,
    price_change: f64,
    minutes_monitored: u64,
    related_wallets: Vec<String>,
) -> Result<()> {
    let symbol = symbol.trim_end_matches('\0');
    let file_exists = Path::new(SOLD_TOKENS_CSV_FILE).exists();

    // Open file in append mode, create if it doesn't exist
    let mut file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(SOLD_TOKENS_CSV_FILE)?;

    // Write header if the file is new
    if !file_exists {
        writeln!(
            file,
            "timestamp,token_mint,symbol,price_change,minutes_monitored,related_wallets"
        )?;
    }

    // Get current timestamp
    let now = SystemTime::now();
    let unix_timestamp = now
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or(Duration::from_secs(0))
        .as_secs();
    let related_wallets_str = related_wallets.join("|");

    // Write data
    writeln!(
        file,
        "{},{},{},{:.4},{},{}",
        unix_timestamp, token_mint, symbol, price_change, minutes_monitored, related_wallets_str
    )?;

    info!(
        "Recorded sold token {} ({}) to CSV with price change {:.2}%, monitored for {} minutes, related wallets: {}",
        token_mint,
        symbol,
        price_change * 100.0,
        minutes_monitored,
        related_wallets.len()
    );

    Ok(())
}

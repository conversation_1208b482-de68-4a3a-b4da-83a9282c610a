use anyhow::Result;
use async_trait::async_trait;
use solana_client::rpc_client::RpcClient;
use solana_sdk::signer::keypair::Keypair;
use solana_sdk::{pubkey::Pubkey, transaction::Transaction};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum RaydiumPoolType {
    CPMM,
    OpenBook,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub enum TradingState {
    Buying { sol_balance: u64 },
    Selling { prev_sol_balance: u64 },
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct TokenConfig {
    pub token_mint: Pubkey,
    pub raydium_pool: Pubkey,
    pub pool_type: RaydiumPoolType,
    pub wsol_amount: f64,           // Amount of SOL to swap
    pub jito_tip_amount: u64,       // Amount in lamports
    pub jito_tip_amount_fast: u64,  // Amount in lamports fast
    pub priority_fee_amount: u64,   // Amount in micro-lamports
    pub buy_price_ratio: f64,       // Price ratio to buy the token
    pub sell_price_ratio: f64,      // Price ratio to sell the token
    pub min_buy_price_ratio: f64,   // Minimum allowed buy price ratio
    pub max_buy_price_ratio: f64,   // Maximum allowed buy price ratio
    pub ratio_adjustment_rate: f64, // How much to adjust ratio on each trade
    pub consecutive_losses: i32,    // Track consecutive losses to adjust more aggressively
    pub trading_state: TradingState,
}

#[derive(Debug)]
pub struct SellConfig {
    pub target_multiplier: f64, // Price target multiplier (e.g., 2.0 for 2x)
    pub sell_percentage: f64,   // Percentage to sell when target is hit (e.g., 0.1 for 10%)
    pub stop_loss_multiplier: f64, // Stop loss multiplier (e.g., 0.6 for 60% of bought price)
    pub timeout_minutes: u64,   // Minutes to wait before selling all
    pub previous_target: f64,   // Previous target multiplier for stop loss
}

#[async_trait]
pub trait PriceProvider: Send + Sync {
    async fn get_price(&self, client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64>;
}

#[async_trait]
pub trait TradingPool: PriceProvider {
    async fn build_swap_transaction(
        &self,
        client: &RpcClient,
        wallet: &Keypair,
        pool_pubkey: &Pubkey,
        input_amount: u64,
        output_amount: u64,
        token_mint: &Pubkey,
        is_buying: bool,
        jito_tip_account: &Pubkey,
        jito_tip_amount: u64,
        priority_fee_amount: u64,
        self_send_sol: u64,
    ) -> Result<Transaction>;
}

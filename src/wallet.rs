use anyhow::{anyhow, Result};
use bs58;
use dirs;
use log::debug;
use serde::{Deserialize, Serialize};
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, Signer},
};
use std::collections::HashSet;
use std::{
    env,
    time::{SystemTime, UNIX_EPOCH},
};

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(untagged)]
pub enum NumberOrString {
    Number(f64),
    String(String),
}

impl Default for NumberOrString {
    fn default() -> Self {
        NumberOrString::Number(0.0)
    }
}

impl NumberOrString {
    pub fn as_f64(&self) -> f64 {
        match self {
            NumberOrString::Number(n) => *n,
            NumberOrString::String(s) => s.parse::<f64>().unwrap_or(0.0),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct WalletRisk {
    #[serde(default)]
    pub fast_tx_ratio: Option<f64>,
    #[serde(default)]
    pub no_buy_hold_ratio: Option<f64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WalletData {
    pub total_profit: Option<NumberOrString>,
    #[serde(default)]
    pub realized_profit_7d: Option<f64>,
    #[serde(default)]
    pub pnl: Option<f64>,
    #[serde(default)]
    pub pnl_7d: Option<f64>,
    #[serde(default)]
    pub token_avg_cost: Option<f64>,
    #[serde(default)]
    pub pnl_lt_minus_dot5_num: Option<i32>,
    #[serde(default)]
    pub pnl_minus_dot5_0x_num: Option<i32>,
    #[serde(default)]
    pub pnl_lt_2x_num: Option<i32>,
    #[serde(default)]
    pub pnl_2x_5x_num: Option<i32>,
    #[serde(default)]
    pub pnl_gt_5x_num: Option<i32>,
    #[serde(default)]
    pub buy_7d: i32,
    #[serde(default)]
    pub buy_30d: i32,
    #[serde(default)]
    pub unrealized_profit: Option<NumberOrString>,
    #[serde(default)]
    pub avg_holding_peroid: Option<f64>,
    #[serde(default)]
    pub risk: WalletRisk,
    #[serde(default)]
    pub low_liquidity_ratio: Option<f64>,
}

impl WalletData {
    pub fn total_trades(&self) -> i32 {
        self.pnl_lt_minus_dot5_num.unwrap_or(0)
            + self.pnl_minus_dot5_0x_num.unwrap_or(0)
            + self.pnl_lt_2x_num.unwrap_or(0)
            + self.pnl_2x_5x_num.unwrap_or(0)
            + self.pnl_gt_5x_num.unwrap_or(0)
    }

    pub fn win_rate(&self) -> f64 {
        let total_trades = self.total_trades();
        if total_trades == 0 {
            return 0.0;
        }

        let winning_trades = self.pnl_lt_2x_num.unwrap_or(0)
            + self.pnl_2x_5x_num.unwrap_or(0)
            + self.pnl_gt_5x_num.unwrap_or(0);

        (winning_trades as f64) / (total_trades as f64)
    }

    pub fn win_a_lot_rate(&self) -> f64 {
        let total_trades = self.total_trades();
        if total_trades == 0 {
            return 0.0;
        }
        (self.pnl_gt_5x_num.unwrap_or(0) as f64 + (self.pnl_2x_5x_num.unwrap_or(0) as f64) / 5.0)
            / (total_trades as f64)
    }
}

pub struct WalletContext {
    keypair: Keypair,
}

impl WalletContext {
    pub fn new() -> Result<Self> {
        // Load private key from environment variable
        let private_key = env::var("PRIVATE_KEY")
            .map_err(|_| anyhow!("PRIVATE_KEY environment variable not set"))?;

        // Convert base58 private key to bytes
        let priv_key_bytes = bs58::decode(&private_key)
            .into_vec()
            .map_err(|_| anyhow!("Invalid private key format"))?;

        // Create keypair from bytes
        let keypair = Keypair::from_bytes(&priv_key_bytes)
            .map_err(|_| anyhow!("Failed to create keypair from private key"))?;

        Ok(Self { keypair })
    }

    pub fn payer(&self) -> Pubkey {
        self.keypair.pubkey()
    }

    pub fn signer(&self) -> &Keypair {
        &self.keypair
    }
}

// deprecated
pub async fn analyze_wallet(_wallet: &str) -> Result<Option<WalletData>> {
    return Ok(None);
}

pub async fn wallet_summary_data(chain: &str, wallet: &str) -> Result<Option<WalletData>> {
    // Use browser automation to get wallet summary data
    match crate::browser_automation::get_wallet_summary_data_with_browser(chain, wallet).await {
        Ok(response_text) => match serde_json::from_str::<WalletData>(&response_text) {
            Ok(response) => {
                return Ok(Some(response));
            }
            Err(e) => {
                println!("Failed to parse wallet data: {}", e);
                println!("Response text: {}", response_text);
                return Ok(None);
            }
        },
        Err(e) => {
            println!("Failed to get wallet summary data: {}", e);
            return Ok(None);
        }
    }
}

/// Helper function to analyze wallet data and determine if it's good for signals or bots
fn analyze_wallet_data(
    summary_data: &WalletData,
    holdings_data: &Vec<WalletHolding>,
    chain: &str,
) -> (bool, bool) {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let one_week_ago = now - 86400 * 7;
    let one_day_ago = now - 86400;
    let traded_holdings = holdings_data
        .iter()
        .filter(|h| {
            (h.start_holding_at.unwrap_or(0) > 0 || chain == "bsc")
                && h.last_active_timestamp.unwrap_or(0) >= one_week_ago
                && h.avg_cost.parse::<f64>().unwrap_or(0.0) != 0.0
        })
        .collect::<Vec<_>>();
    if traded_holdings.len() == 0 {
        return (false, false);
    }
    let traded_holdings_len = traded_holdings.len();
    let win_holdings_count = traded_holdings
        .iter()
        .filter(|h| h.total_profit.parse::<f64>().unwrap_or(0.0) > 0.0)
        .count();
    let strict_win_holdings_count = traded_holdings
        .iter()
        .filter(|h| h.total_profit_pnl.parse::<f64>().unwrap_or(0.0) > 0.2)
        .count();
    let loss_50_holdings_count = traded_holdings
        .iter()
        .filter(|h| h.total_profit_pnl.parse::<f64>().unwrap_or(0.0) < -0.5)
        .count();
    let sold_holdings = traded_holdings
        .clone()
        .into_iter()
        .filter(|h| {
            h.avg_cost.parse::<f64>().unwrap_or(0.0) != 0.0
                && h.avg_sold.parse::<f64>().unwrap_or(0.0) != 0.0
                && h.balance.parse::<f64>().unwrap_or(0.0) == 0.0
        })
        .collect::<Vec<_>>();
    let bad_sold_holdings = traded_holdings
        .clone()
        .into_iter()
        .filter(|h| {
            h.avg_cost.parse::<f64>().unwrap() < 0.000012
                && h.avg_sold.parse::<f64>().unwrap() < 0.000012
        })
        .collect::<Vec<_>>();
    let bad_unsold_holdings = traded_holdings
        .into_iter()
        .filter(|h| {
            h.avg_cost.parse::<f64>().unwrap_or(0.0) != 0.0
                && h.balance.parse::<f64>().unwrap_or(0.0) > 0.0
                && h.start_holding_at.unwrap_or(0) < one_day_ago
                && h.total_profit.parse::<f64>().unwrap_or(0.0) < 0.0
                && h.unrealized_profit.parse::<f64>().unwrap_or(0.0) < 0.0
        })
        .collect::<Vec<_>>();
    let sold_holdings_count = sold_holdings.len();
    let bad_unsold_holdings_count = bad_unsold_holdings.len();
    let bad_sold_holdings_count = bad_sold_holdings.len();
    let mut sold_holdings_duration = sold_holdings
        .iter()
        .map(|h| {
            h.end_holding_at
                .unwrap_or(0)
                .saturating_sub(h.start_holding_at.unwrap_or(0))
        })
        .collect::<Vec<_>>();
    sold_holdings_duration.sort_unstable();

    let sold_holding_avg_duration = if sold_holdings_duration.len() > 0 {
        sold_holdings_duration.iter().sum::<u64>() as f64 / sold_holdings_duration.len() as f64
    } else {
        0.0
    };
    let sold_holding_median_duration = if sold_holdings_duration.len() > 0 {
        sold_holdings_duration[sold_holdings_duration.len() / 2]
    } else {
        0
    };
    let hold_short_ratio = if sold_holdings_duration.len() > 0 {
        sold_holdings_duration
            .iter()
            .filter(|&&duration| duration <= 180)
            .count() as f64
            / sold_holdings_duration.len() as f64
    } else {
        0.0
    };
    let bad_sold_holding_ratio = bad_sold_holdings_count as f64 / traded_holdings_len as f64;
    let bad_unsold_holding_ratio = bad_unsold_holdings_count as f64 / traded_holdings_len as f64;
    let fast_tx_ratio = summary_data.risk.fast_tx_ratio.unwrap_or(0.0);

    // For simplicity, we'll use a default value for native_balance_usd
    // In a real implementation, you would need to get this from somewhere
    let native_balance_usd = 100.0;

    let buy_7d = summary_data.buy_7d;
    let buy_30d = summary_data.buy_30d;
    let win_a_lot_rate = summary_data.win_a_lot_rate();
    let token_avg_cost = summary_data.token_avg_cost.unwrap_or(0.0);
    let win_rate = f64::min(
        win_holdings_count as f64 / traded_holdings_len as f64,
        summary_data.win_rate(),
    );
    let loss_50_rate = loss_50_holdings_count as f64 / traded_holdings_len as f64;
    let strict_win_rate = f64::min(
        win_rate,
        strict_win_holdings_count as f64 / traded_holdings_len as f64,
    );

    let unrealized_profit = summary_data
        .unrealized_profit
        .clone()
        .unwrap_or(NumberOrString::Number(0.0))
        .as_f64();
    let total_profit = summary_data
        .total_profit
        .clone()
        .unwrap_or(NumberOrString::Number(0.0))
        .as_f64();
    let unrealized_too_negative =
        unrealized_profit < 0.0 && (-2.0 * unrealized_profit) >= total_profit;

    debug!("native_balance_usd: {}", native_balance_usd);
    debug!("buy_7d: {}", buy_7d);
    debug!("token_avg_cost: {}", token_avg_cost);
    debug!("win_rate: {}", win_rate);
    debug!("strict_win_rate: {}", strict_win_rate);
    debug!("loss_50_rate: {}", loss_50_rate);
    debug!("sold_holdings_count: {}", sold_holdings_count);
    debug!("hold_short_ratio: {}", hold_short_ratio);
    debug!("sold_holding_avg_duration: {}", sold_holding_avg_duration);
    debug!(
        "sold_holding_median_duration: {}",
        sold_holding_median_duration
    );
    debug!("bad_unsold_holding_ratio: {}", bad_unsold_holding_ratio);
    debug!("fast_tx_ratio: {}", fast_tx_ratio);
    debug!("unrealized_too_negative: {}", unrealized_too_negative);
    debug!("total_profit: {}", total_profit);
    debug!("buy_30d: {}", buy_30d);
    debug!("win_a_lot_rate: {}", win_a_lot_rate);
    debug!("bad_sold_holding_ratio: {}", bad_sold_holding_ratio);

    let for_signal = native_balance_usd >= 50.0
        && buy_7d >= 10
        && token_avg_cost >= 200.0
        && win_rate + win_a_lot_rate >= 0.4
        && strict_win_rate + win_a_lot_rate >= 0.2
        && loss_50_rate - win_a_lot_rate * 0.5 <= 0.15
        && win_a_lot_rate >= 0.01
        && hold_short_ratio <= 0.15
        && sold_holdings_count >= 5
        && (sold_holding_avg_duration >= 1800.0 || chain == "bsc")
        && (sold_holding_median_duration >= 300 || chain == "bsc")
        && bad_unsold_holding_ratio < 0.1
        && fast_tx_ratio < 0.1
        && !unrealized_too_negative
        && total_profit >= 3_000.0
        && total_profit < 1_000_000.0;

    // Check if the wallet is good for bots
    let for_bot = native_balance_usd >= 50.0
        && buy_30d >= 10
        && token_avg_cost >= 200.0
        && win_rate + win_a_lot_rate >= 0.4
        && strict_win_rate + win_a_lot_rate >= 0.2
        && loss_50_rate - win_a_lot_rate * 0.5 <= 0.15
        && win_a_lot_rate >= 0.01
        && sold_holdings_count >= 5
        && (sold_holding_avg_duration >= 180.0 || chain == "bsc")
        && (sold_holding_median_duration >= 30 || chain == "bsc")
        && bad_unsold_holding_ratio < 0.1
        && fast_tx_ratio < 0.25
        && !unrealized_too_negative
        && total_profit >= 5_000.0
        && total_profit < 1_000_000.0;

    (for_signal, for_bot)
}

/// Analyze a single wallet to determine if it's profitable
pub async fn analyze_wallet_profitable(chain: &str, wallet: &str) -> Result<(bool, bool)> {
    debug!("analyze_wallet_profitable: {}", wallet);

    // Get wallet summary data using browser automation
    let summary_data = match crate::browser_automation::get_wallet_summary_data_with_browser(
        chain, wallet,
    )
    .await
    {
        Ok(data) => data,
        Err(e) => {
            debug!("Failed to get wallet summary data: {}", e);
            return Ok((false, false));
        }
    };

    // Parse the summary data
    let data = match serde_json::from_str::<WalletData>(&summary_data) {
        Ok(data) => data,
        Err(e) => {
            debug!("Failed to parse wallet summary data: {}", e);
            return Ok((false, false));
        }
    };

    debug!("Got wallet summary data");

    // Get wallet holdings using browser automation
    let holdings =
        match crate::browser_automation::get_wallet_holdings_with_browser(chain, wallet).await {
            Ok(holdings) => holdings,
            Err(e) => {
                debug!("Failed to get wallet holdings: {}", e);
                return Ok((false, false));
            }
        };

    debug!("Got wallet holdings");

    // Parse the holdings data
    let holdings_data = match serde_json::from_str::<Vec<WalletHolding>>(&holdings) {
        Ok(data) => data,
        Err(e) => {
            debug!("Failed to parse wallet holdings data: {}", e);
            return Ok((false, false));
        }
    };

    // Use the helper function to analyze the wallet data
    Ok(analyze_wallet_data(&data, &holdings_data, chain))
}

/// Analyze multiple wallets in batch and categorize them based on profitability
pub async fn analyze_sol_wallets_batch(
    wallets: &HashSet<String>,
) -> Result<(Vec<String>, Vec<String>)> {
    // Get raw wallet data from browser automation
    let wallet_data =
        crate::browser_automation::analyze_wallets_batch_with_browser(wallets, true).await?;

    let mut good_for_signals = Vec::new();
    let mut good_for_bots = Vec::new();

    // Skip if no wallets to analyze
    if wallets.is_empty() {
        return Ok((Vec::new(), Vec::new()));
    }

    // Load existing tracked wallets for signals
    let mut existing_signal_wallets = HashSet::new();
    if let Ok(content) = std::fs::read_to_string(
        dirs::home_dir()
            .unwrap()
            .join(".solana-bot/tracked_wallets.json"),
    ) {
        if let Ok(json) = serde_json::from_str::<serde_json::Value>(&content) {
            if let Some(wallets) = json.get("wallets").and_then(|w| w.as_array()) {
                for wallet in wallets {
                    if let Some(w) = wallet.as_str() {
                        existing_signal_wallets.insert(w.to_string());
                    }
                }
            }
            if let Some(blacklist) = json.get("blacklist").and_then(|b| b.as_array()) {
                for wallet in blacklist {
                    if let Some(w) = wallet.as_str() {
                        existing_signal_wallets.insert(w.to_string());
                    }
                }
            }
        }
    }

    // Load existing tracked wallets for bots
    let mut existing_bot_wallets = HashSet::new();
    if let Ok(content) = std::fs::read_to_string(
        dirs::home_dir()
            .unwrap()
            .join(".solana-bot/tracked_wallets_bot.json"),
    ) {
        if let Ok(json) = serde_json::from_str::<serde_json::Value>(&content) {
            if let Some(wallets) = json.get("all_wallets").and_then(|w| w.as_array()) {
                for wallet in wallets {
                    if let Some(w) = wallet.as_str() {
                        existing_bot_wallets.insert(w.to_string());
                    }
                }
            }
            if let Some(blacklist) = json.get("blacklist").and_then(|b| b.as_array()) {
                for wallet in blacklist {
                    if let Some(w) = wallet.as_str() {
                        existing_bot_wallets.insert(w.to_string());
                    }
                }
            }
        }
    }

    // Analyze each wallet
    for wallet in wallets {
        debug!("Analyzing wallet: {}", wallet);
        let wallet_info = wallet_data.get(wallet).ok_or_else(|| {
            anyhow::anyhow!("No data found for wallet: {} on chain solana", wallet)
        })?;

        // Check if we have both summary and holdings data
        let summary = wallet_info.get("wallet_summary");
        let holdings = wallet_info.get("wallet_holdings");

        if let (Some(summary), Some(holdings)) = (summary, holdings) {
            // Parse the summary data
            let summary_data = match serde_json::from_value::<WalletData>(summary.clone()) {
                Ok(data) => data,
                Err(e) => {
                    debug!("Failed to parse wallet summary data for {}: {}", wallet, e);
                    continue;
                }
            };

            // Parse the holdings data
            let holdings_data = match serde_json::from_value::<Vec<WalletHolding>>(holdings.clone())
            {
                Ok(data) => data,
                Err(e) => {
                    debug!("Failed to parse wallet holdings data for {}: {}", wallet, e);
                    debug!("Holdings data: {:.100}", holdings.to_string());
                    continue;
                }
            };

            // Use the helper function to analyze the wallet data
            let (for_signal, for_bot) = analyze_wallet_data(&summary_data, &holdings_data, "sol");

            // Add the wallet to the appropriate lists if not already tracked
            if for_signal && !existing_signal_wallets.contains(wallet) {
                println!("Found profitable wallet for signal: {}", wallet);
                good_for_signals.push(wallet.clone());
            }
            if for_bot && !existing_bot_wallets.contains(wallet) {
                println!("Found profitable wallet for bot: {}", wallet);
                good_for_bots.push(wallet.clone());
            }
        }
    }

    Ok((good_for_signals, good_for_bots))
}

/// Get the win rate for a wallet based on its holdings
///
/// # Arguments
///
/// * `wallet_address` - The wallet address to analyze
///
/// # Returns
///
/// * `Result<f64>` - The win rate as a float between 0.0 and 1.0, or error
pub async fn get_wallet_win_rate(_chain: &str, wallet_address: &str) -> Result<f64> {
    let wallets = HashSet::from([wallet_address.to_string()]);
    let wallet_data =
        crate::browser_automation::analyze_wallets_batch_with_browser(&wallets, true).await?;
    let wallet_info = wallet_data.get(wallet_address).ok_or_else(|| {
        anyhow::anyhow!(
            "No data found for wallet: {} on chain solana",
            wallet_address
        )
    })?;

    // Check if we have both summary and holdings data
    let summary = wallet_info.get("wallet_summary");
    let holdings = wallet_info.get("wallet_holdings");
    let summary = match summary {
        Some(s) => s,
        None => return Ok(0.0),
    };
    let holdings = match holdings {
        Some(h) => h,
        None => return Ok(0.0),
    };
    let summary_data = match serde_json::from_value::<WalletData>(summary.clone()) {
        Ok(data) => data,
        Err(e) => {
            println!(
                "Failed to parse wallet summary data for {}: {}",
                wallet_address, e
            );
            return Ok(0.0);
        }
    };
    let holdings_data = match serde_json::from_value::<Vec<WalletHolding>>(holdings.clone()) {
        Ok(data) => data,
        Err(e) => {
            println!(
                "Failed to parse wallet holdings data for {}: {}",
                wallet_address, e
            );
            println!("Holdings data: {:.100}", holdings.to_string());
            return Ok(0.0);
        }
    };

    // Filter for relevant holdings
    let one_week_ago = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs()
        - 86400 * 7;

    let traded_holdings = holdings_data
        .iter()
        .filter(|h| {
            h.start_holding_at.unwrap_or(0) > 0
                && h.last_active_timestamp.unwrap_or(0) >= one_week_ago
                && h.avg_cost.parse::<f64>().unwrap_or(0.0) != 0.0
        })
        .collect::<Vec<_>>();
    debug!("Got traded holdings len: {}", traded_holdings.len());

    if traded_holdings.is_empty() {
        return Ok(0.0);
    }

    // Calculate win rate
    let win_holdings_count = traded_holdings
        .iter()
        .filter(|h| h.total_profit.parse::<f64>().unwrap_or(0.0) > 0.0)
        .count();

    let win_rate = win_holdings_count as f64 / traded_holdings.len() as f64;
    Ok(f64::min(
        0.8,
        f64::min(win_rate, summary_data.win_rate()) + summary_data.win_a_lot_rate(),
    ))
}

// deprecated
pub fn is_profitable_wallet(_data: &WalletData) -> bool {
    return false;
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WalletHolding {
    token: TokenInfo,
    balance: String,
    total_profit: String,
    total_profit_pnl: String,
    unrealized_profit: String,
    avg_cost: String,
    avg_sold: String,
    liquidity: Option<String>,
    last_active_timestamp: Option<u64>,
    start_holding_at: Option<u64>,
    end_holding_at: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenInfo {
    address: String,
    name: String,
    symbol: String,
}

async fn get_wallet_holdings(chain: &str, wallet_address: &str) -> Result<Vec<WalletHolding>> {
    // Use browser automation to get wallet holdings
    match crate::browser_automation::get_wallet_holdings_with_browser(chain, wallet_address).await {
        Ok(response_text) => {
            let mut all_holdings = Vec::new();
            match serde_json::from_str::<Vec<WalletHolding>>(&response_text) {
                Ok(resp) => {
                    all_holdings.extend(resp);
                }
                Err(e) => {
                    println!("Failed to parse wallet holdings response: {}", e);
                    println!(
                        "Response text: {}",
                        &response_text[..100.min(response_text.len())]
                    );
                    // Continue with other responses even if one fails
                }
            }

            Ok(all_holdings)
        }
        Err(e) => {
            println!("Failed to get wallet holdings: {}", e);
            Err(e)
        }
    }
}

fn filter_pump_tokens_from_holdings(holdings: Vec<WalletHolding>) -> Vec<String> {
    let mut pump_tokens = Vec::new();
    for holding in holdings {
        if let Ok(_profit) = holding.total_profit.parse::<f64>() {
            if holding.token.address.to_lowercase().ends_with("pump") {
                pump_tokens.push(holding.token.address);
            }
        }
    }

    pump_tokens
}

pub async fn is_suspicious_wallet(chain: &str, wallet_address: &str) -> Result<bool> {
    use crate::{
        analyze_pump_transactions, buy_records::load_buy_records_from_cache,
        get_token_mint_activity, has_significant_buy_activity,
    };
    use solana_client::rpc_client::RpcClient;
    use solana_sdk::commitment_config::CommitmentConfig;
    use std::env;

    // Get RPC URL from environment or use default
    let rpc_url =
        env::var("RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
    let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

    // Get wallet holdings
    let holdings = match get_wallet_holdings(chain, wallet_address).await {
        Ok(h) => h,
        Err(e) => {
            println!(
                "Error getting holdings for wallet {}: {}",
                wallet_address, e
            );
            return Ok(false);
        }
    };

    // Filter for pump tokens with high profit
    let pump_tokens = filter_pump_tokens_from_holdings(holdings);
    if pump_tokens.is_empty() {
        return Ok(false);
    }

    // Analyze each pump token
    for token_address in pump_tokens {
        // Check cache first
        if let Some(cache) = load_buy_records_from_cache(&token_address) {
            // Check if wallet address is in the cached buy records
            let is_suspicious = cache
                .buy_records
                .iter()
                .any(|r| r.source_address == wallet_address);

            if is_suspicious {
                println!(
                    "⚠️ SUSPICIOUS: Wallet {} found in buy records for token {}",
                    wallet_address, token_address
                );
                return Ok(true);
            }
        }

        // Get token mint activity
        let mint_activity = match get_token_mint_activity(&token_address).await {
            Ok(Some(activity)) => activity,
            Ok(None) => {
                continue;
            }
            Err(e) => {
                println!(
                    "Error getting mint activity for token {}: {}",
                    token_address, e
                );
                continue;
            }
        };

        // Extract bonding curve address
        let bonding_curve_address = mint_activity.to_address.clone();

        // Analyze transactions
        let buy_records = match analyze_pump_transactions(&client, &bonding_curve_address).await {
            Ok(records) => records,
            Err(e) => {
                println!(
                    "Error analyzing transactions for token {}: {}",
                    token_address, e
                );
                continue;
            }
        };

        if buy_records.is_empty() {
            continue;
        }

        // Check for significant buy activity
        let has_significant_activity = has_significant_buy_activity(&buy_records, 1, 30.0);

        if has_significant_activity {
            // Check if wallet address is in the buy records
            let is_suspicious = buy_records
                .iter()
                .any(|r| r.source_address == wallet_address);

            if is_suspicious {
                println!(
                    "⚠️ SUSPICIOUS: Wallet {} found in buy records for token {}",
                    wallet_address, token_address
                );
                return Ok(true);
            }
        }
    }

    Ok(false)
}

use anyhow::{anyhow, Result};
use async_trait::async_trait;
use borsh::{BorshDeserialize, BorshSerialize};
use log::{debug, error};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    compute_budget::ComputeBudgetInstruction,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
    system_instruction,
    transaction::Transaction,
};
use spl_associated_token_account::{
    get_associated_token_address, instruction::create_associated_token_account,
};
use std::{str::FromStr, thread::sleep, time::Duration};

use crate::{config::TradingPool, retry_with_backoff, PriceProvider, WSOL_MINT};

#[derive(BorshDeserialize, BorshSerialize, Debug)]
#[repr(C)]
pub struct PumpFunPoolState {
    pub virtual_token_reserves: u64,
    pub virtual_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub token_total_supply: u64,
    pub complete: bool,
}

#[derive(BorshDeserialize, BorshSerialize, Debug)]
#[repr(C)]
pub struct PumpFunAmmPoolState {
    pub pool_bump: u8,
    pub index: u16,
    pub creator: Pubkey,
    pub base_mint: Pubkey,
    pub quote_mint: Pubkey,
    pub lp_mint: Pubkey,
    pub pool_base_token_account: Pubkey,
    pub pool_quote_token_account: Pubkey,
    pub lp_supply: u64,
}

impl PumpFunPoolState {
    pub fn from_bytes(data: &[u8]) -> Result<Self> {
        // Use manual parsing directly
        Self::parse_manual(data)
    }

    // Manual parsing based on the correct field order
    pub fn parse_manual(data: &[u8]) -> Result<Self> {
        if data.len() < 49 {
            // 8 (discriminator) + 5 * 8 + 1 bytes minimum
            return Err(anyhow!("Data too short to be a valid PumpFunPoolState"));
        }

        // Skip the first 8 bytes (discriminator)
        let virtual_token_reserves = u64::from_le_bytes(data[8..16].try_into()?);
        let virtual_sol_reserves = u64::from_le_bytes(data[16..24].try_into()?);
        let real_token_reserves = u64::from_le_bytes(data[24..32].try_into()?);
        let real_sol_reserves = u64::from_le_bytes(data[32..40].try_into()?);
        let token_total_supply = u64::from_le_bytes(data[40..48].try_into()?);
        let complete = data[48] != 0;

        Ok(Self {
            virtual_token_reserves,
            virtual_sol_reserves,
            real_token_reserves,
            real_sol_reserves,
            token_total_supply,
            complete,
        })
    }

    // Calculate price using bonding curve formula
    pub fn calculate_price(&self) -> f64 {
        // Token decimals is always 6 for Pump.fun tokens
        let token_factor = 1_000_000.0; // 10^6
        let sol_factor = 1_000_000_000.0; // 10^9 (lamports to SOL)

        // Standard AMM formula using virtual reserves
        let price = (self.virtual_sol_reserves as f64 / sol_factor)
            / (self.virtual_token_reserves as f64 / token_factor);

        price
    }
}

impl PumpFunAmmPoolState {
    pub fn from_bytes(data: &[u8]) -> Result<Self> {
        // Skip the first 8 bytes (discriminator) and use exactly the size of the struct
        let exact_size = 203; // The size of PumpFunAmmPoolState in bytes
        let end = std::cmp::min(data.len(), 8 + exact_size);

        Self::try_from_slice(&data[8..end])
            .map_err(|e| anyhow!("Failed to deserialize PumpFunAmmPoolState: {}", e))
    }
}

pub struct PumpFunPool {
    pub token_mint: Pubkey,
}

pub struct PumpFunAmmPool {
    pub token_mint: Pubkey,
    pub pool_pubkey: Pubkey,
}

impl PumpFunPool {
    // Helper method to get pool state
    pub async fn get_pool_state(
        &self,
        client: &RpcClient,
        pool_pubkey: &Pubkey,
    ) -> Result<PumpFunPoolState> {
        let mut attempts = 0;
        let account = loop {
            match client.get_account(pool_pubkey) {
                Ok(account) => break account,
                Err(e) if attempts < 5 => {
                    attempts += 1;
                    error!(
                        "Failed to get account {}, retrying ({}/5): {:?}",
                        pool_pubkey.to_string(),
                        attempts,
                        e
                    );
                    sleep(Duration::from_millis(500));
                }
                Err(e) => return Err(e.into()),
            }
        };

        // Parse the account data directly
        PumpFunPoolState::parse_manual(&account.data)
    }
}

impl PumpFunAmmPool {
    // Helper method to get pool state
    pub async fn get_pool_state(
        &self,
        client: &RpcClient,
        pool_pubkey: &Pubkey,
    ) -> Result<PumpFunAmmPoolState> {
        let mut attempts = 0;
        let account = loop {
            match client.get_account(pool_pubkey) {
                Ok(account) => break account,
                Err(e) if attempts < 5 => {
                    attempts += 1;
                    error!(
                        "Failed to get account {}, retrying ({}/5): {:?}",
                        pool_pubkey.to_string(),
                        attempts,
                        e
                    );
                    sleep(Duration::from_millis(500));
                }
                Err(e) => return Err(e.into()),
            }
        };
        PumpFunAmmPoolState::from_bytes(&account.data)
    }
}

#[async_trait]
impl PriceProvider for PumpFunPool {
    async fn get_price(&self, client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64> {
        // Get the pool state
        let pool_state = self.get_pool_state(client, pool_pubkey).await?;
        if pool_state.complete {
            return Err(anyhow!("pool is complete"));
        }

        // Calculate price using bonding curve formula
        let price = pool_state.calculate_price();

        Ok(price)
    }
}

#[async_trait]
impl PriceProvider for PumpFunAmmPool {
    async fn get_price(&self, client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64> {
        // Get the pool state
        let pool_state = self.get_pool_state(client, pool_pubkey).await?;
        let base_balance = client
            .get_token_account_balance(&pool_state.pool_base_token_account)?
            .amount
            .parse::<f64>()
            .map(|v| v as u64)
            .map_err(|e| anyhow!("Failed to parse base balance: {}", e))?;

        let quote_balance = client
            .get_token_account_balance(&pool_state.pool_quote_token_account)?
            .amount
            .parse::<f64>()
            .map(|v| v as u64)
            .map_err(|e| anyhow!("Failed to parse quote balance: {}", e))?;

        if base_balance == 0 || quote_balance == 0 {
            return Err(anyhow!(
                "No liquidity in pool, base: {}, quote: {}",
                base_balance,
                quote_balance
            ));
        }

        debug!(
            "Base token {}, balance: {}",
            pool_state.base_mint, base_balance
        );
        debug!(
            "Quote token {}, balance: {}",
            pool_state.quote_mint, quote_balance
        );

        // Calculate price with decimal adjustment
        // price = (base_balance / base_decimals) / (quote_balance / quote_decimals)
        let base_factor = 10u64.pow(6 as u32);
        let quote_factor = 10u64.pow(9 as u32);
        let price = (base_balance as f64 / base_factor as f64)
            / (quote_balance as f64 / quote_factor as f64);

        // Calculate final price
        if pool_state.quote_mint == Pubkey::from_str(WSOL_MINT)? {
            Ok(1.0 / price)
        } else {
            Ok(price)
        }
    }
}

#[async_trait]
impl TradingPool for PumpFunPool {
    async fn build_swap_transaction(
        &self,
        client: &RpcClient,
        wallet: &Keypair,
        pool_pubkey: &Pubkey,
        input_amount: u64,
        output_amount: u64,
        token_mint: &Pubkey,
        is_buying: bool,
        jito_tip_account: &Pubkey,
        jito_tip_amount: u64,
        priority_fee_amount: u64,
        self_send_amount: u64,
    ) -> Result<Transaction> {
        // Common accounts and constants
        let global_account = Pubkey::from_str("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf")?;
        let fee_recipient = Pubkey::from_str("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV")?;
        let pump_fun_program = Pubkey::from_str("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")?;
        let event_authority = Pubkey::from_str("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1")?;
        let system_program = solana_sdk::system_program::id();
        let token_program = spl_token::id();
        let rent_program = solana_sdk::sysvar::rent::id();
        let associated_token_program = spl_associated_token_account::id();

        // Get the pool's token account
        let pool_token_account = get_associated_token_address(pool_pubkey, token_mint);

        // Get the user's token account
        let user_token_account = get_associated_token_address(&wallet.pubkey(), token_mint);

        // Set compute unit limit and price
        let set_compute_unit_limit_ix = ComputeBudgetInstruction::set_compute_unit_limit(300_000);
        let set_compute_unit_price_ix =
            ComputeBudgetInstruction::set_compute_unit_price(priority_fee_amount);

        // Build instructions vector
        let mut instructions = vec![set_compute_unit_limit_ix, set_compute_unit_price_ix];

        if is_buying {
            // Check if token account exists and create if needed
            if client.get_account(&user_token_account).is_err() {
                let create_token_account_ix = create_associated_token_account(
                    &wallet.pubkey(),
                    &wallet.pubkey(),
                    token_mint,
                    &token_program,
                );
                instructions.push(create_token_account_ix);
            }

            // Build buy instruction data
            // Prefix + amount (tokens to buy) + maxSolCost
            let mut buy_data = hex::decode("66063d1201daebea")?;
            buy_data.extend_from_slice(&output_amount.to_le_bytes()); // Amount of tokens to buy
            buy_data.extend_from_slice(&input_amount.to_le_bytes()); // Max SOL cost

            // Build buy instruction
            let buy_ix = Instruction {
                program_id: pump_fun_program,
                accounts: vec![
                    AccountMeta::new_readonly(global_account, false),
                    AccountMeta::new(fee_recipient, false),
                    AccountMeta::new_readonly(*token_mint, false),
                    AccountMeta::new(*pool_pubkey, false),
                    AccountMeta::new(pool_token_account, false),
                    AccountMeta::new(user_token_account, false),
                    AccountMeta::new(wallet.pubkey(), true),
                    AccountMeta::new_readonly(system_program, false),
                    AccountMeta::new_readonly(token_program, false),
                    AccountMeta::new_readonly(rent_program, false),
                    AccountMeta::new_readonly(event_authority, false),
                    AccountMeta::new_readonly(pump_fun_program, false),
                ],
                data: buy_data,
            };

            instructions.push(buy_ix);
        } else {
            // Build sell instruction data
            // Prefix + amount (tokens to sell) + minSolOutput
            let mut sell_data = hex::decode("33e685a4017f83ad")?;
            sell_data.extend_from_slice(&input_amount.to_le_bytes()); // Amount of tokens to sell
            sell_data.extend_from_slice(&output_amount.to_le_bytes()); // Min SOL output

            // Build sell instruction
            let sell_ix = Instruction {
                program_id: pump_fun_program,
                accounts: vec![
                    AccountMeta::new_readonly(global_account, false),
                    AccountMeta::new(fee_recipient, false),
                    AccountMeta::new_readonly(*token_mint, false),
                    AccountMeta::new(*pool_pubkey, false),
                    AccountMeta::new(pool_token_account, false),
                    AccountMeta::new(user_token_account, false),
                    AccountMeta::new(wallet.pubkey(), true),
                    AccountMeta::new_readonly(system_program, false),
                    AccountMeta::new_readonly(associated_token_program, false),
                    AccountMeta::new_readonly(token_program, false),
                    AccountMeta::new_readonly(event_authority, false),
                    AccountMeta::new_readonly(pump_fun_program, false),
                ],
                data: sell_data,
            };

            instructions.push(sell_ix);
        }

        // Add self-send instruction if needed
        if self_send_amount > 0 {
            if is_buying {
                let self_transfer_ix = system_instruction::transfer(
                    &wallet.pubkey(),
                    &wallet.pubkey(),
                    self_send_amount,
                );
                instructions.push(self_transfer_ix);
            } else {
                let self_transfer_ix = spl_token::instruction::transfer(
                    &token_program,
                    &user_token_account,
                    &user_token_account,
                    &wallet.pubkey(),
                    &[&wallet.pubkey()],
                    self_send_amount,
                )?;
                instructions.push(self_transfer_ix);
            }
        }

        // Add Jito tip instruction
        let jito_tip_ix =
            system_instruction::transfer(&wallet.pubkey(), jito_tip_account, jito_tip_amount);
        instructions.push(jito_tip_ix);

        // Build and sign transaction
        let recent_blockhash = retry_with_backoff(5, Duration::from_millis(500), || {
            client.get_latest_blockhash().map_err(anyhow::Error::new)
        })?;

        let transaction = Transaction::new_signed_with_payer(
            &instructions,
            Some(&wallet.pubkey()),
            &[wallet],
            recent_blockhash,
        );

        Ok(transaction)
    }
}

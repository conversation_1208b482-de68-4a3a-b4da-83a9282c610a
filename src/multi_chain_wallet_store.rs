use crate::wallet_store::WalletStore;
use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::{self, File};
use std::path::PathBuf;

#[derive(Debug, Serialize, Deserialize)]
pub struct MultiChainWalletStore {
    #[serde(default)]
    chain_stores: HashMap<String, WalletStore>,
    #[serde(default)]
    filename: String,
}

impl MultiChainWalletStore {
    pub fn new(filename: &str) -> Self {
        Self {
            chain_stores: HashMap::new(),
            filename: filename.to_string(),
        }
    }

    pub fn load(filename: &str) -> Result<Self> {
        let mut store = Self::new(filename);
        let path = store.get_store_path()?;
        Self::ensure_store_dir()?;

        if !path.exists() {
            // Check if old format exists and migrate
            let old_store_result = WalletStore::load(filename);
            if let Ok(old_store) = old_store_result {
                // Migrate data from old format
                store.chain_stores.insert("solana".to_string(), old_store);
                store.save()?;
                return Ok(store);
            }

            File::create(&path)?;
            return Ok(store);
        }

        let content = fs::read_to_string(path)?;
        if !content.trim().is_empty() {
            store = serde_json::from_str(&content)?;
            store.filename = filename.to_string();
        }
        Ok(store)
    }

    pub fn save(&self) -> Result<()> {
        let path = self.get_store_path()?;
        Self::ensure_store_dir()?;

        let content = serde_json::to_string_pretty(self)?;
        fs::write(path, content)?;
        Ok(())
    }

    fn ensure_store_dir() -> Result<()> {
        let home = dirs::home_dir().ok_or_else(|| anyhow!("Could not find home directory"))?;
        let store_dir = home.join(".solana-bot");
        if !store_dir.exists() {
            fs::create_dir_all(&store_dir)?;
        }
        Ok(())
    }

    fn get_store_path(&self) -> Result<PathBuf> {
        let home = dirs::home_dir().ok_or_else(|| anyhow!("Could not find home directory"))?;
        Ok(home.join(".solana-bot").join(&self.filename))
    }

    // Get a specific chain's wallet store, create if it doesn't exist
    pub fn get_chain_store(&mut self, chain: &str) -> &mut WalletStore {
        if !self.chain_stores.contains_key(chain) {
            let new_store = WalletStore::new(&format!("{}-{}", self.filename, chain));
            self.chain_stores.insert(chain.to_string(), new_store);
        }
        self.chain_stores.get_mut(chain).unwrap()
    }

    // Get a specific chain's wallet store if it exists
    pub fn get_chain_store_if_exists(&self, chain: &str) -> Option<&WalletStore> {
        self.chain_stores.get(chain)
    }

    // Get all chains
    pub fn get_chains(&self) -> Vec<String> {
        self.chain_stores.keys().cloned().collect()
    }
}

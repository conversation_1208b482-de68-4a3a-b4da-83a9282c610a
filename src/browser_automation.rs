use anyhow::{anyhow, Result};
use chrono::{DateTime, Duration, Utc};
use log::warn;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::fs::{self, File, OpenOptions};
use std::io::{Read, Write};
use std::path::Path;
use std::process::Command;
use tokio::process::Command as AsyncCommand;

use crate::wallet_transfer_analyzer;

// Path to the Python script
const PYTHON_SCRIPT: &str = "py/get_cf_clearance_cfsession.py";
// Cache directory
const CACHE_DIR: &str = "cache";
// Token expiry in seconds
// const TOKEN_EXPIRY_SECONDS: u64 = 3600; // 1 hour

/// Structure to store the Cloudflare clearance token and user agent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CloudflareToken {
    pub cf_clearance: String,
    pub user_agent: String,
    pub host: String,
    #[serde(with = "chrono::serde::ts_seconds")]
    pub expiry: DateTime<Utc>,
}

impl std::fmt::Display for CloudflareToken {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "CloudflareToken {{ cf_clearance: {}, host: {}, expiry: {} }}",
            self.cf_clearance, self.host, self.expiry
        )
    }
}

/// Structure to manage the token cache
#[derive(Debug, Serialize, Deserialize)]
pub struct TokenCache {
    tokens: Vec<CloudflareToken>,
}

impl TokenCache {
    /// Create a new token cache
    fn new() -> Self {
        Self { tokens: Vec::new() }
    }

    /// Load the token cache from file
    fn load() -> Result<Self> {
        let token_cache_file = format!("{}/cf_token_cache.json", CACHE_DIR);
        if Path::new(&token_cache_file).exists() {
            let mut file = File::open(&token_cache_file)?;
            let mut contents = String::new();
            file.read_to_string(&mut contents)?;
            let cache: TokenCache = serde_json::from_str(&contents)?;
            Ok(cache)
        } else {
            Ok(Self::new())
        }
    }

    /// Save the token cache to file
    fn save(&self) -> Result<()> {
        let contents = serde_json::to_string(self)?;
        let token_cache_file = format!("{}/cf_token_cache.json", CACHE_DIR);
        let mut file = File::create(&token_cache_file)?;
        file.write_all(contents.as_bytes())?;
        Ok(())
    }

    /// Check if the token is valid (not expired) for a particular host
    #[allow(dead_code)]
    fn is_token_valid(&self, host: &str) -> bool {
        self.tokens
            .iter()
            .any(|token| token.host == host && token.expiry > Utc::now())
    }

    /// Set a new token with expiry time
    fn set_token(
        &mut self,
        cf_clearance: String,
        user_agent: String,
        host: String,
        expiry_minutes: i64,
    ) -> Result<()> {
        let expiry = Utc::now() + Duration::minutes(expiry_minutes);

        // Remove any existing token for this host
        self.tokens.retain(|t| t.host != host);

        // Add the new token
        self.tokens.push(CloudflareToken {
            cf_clearance,
            user_agent,
            host,
            expiry,
        });

        self.save()?;
        Ok(())
    }

    /// Get the current token for a specific host if valid
    fn get_token(&self, host: &str) -> Option<CloudflareToken> {
        self.tokens
            .iter()
            .find(|token| token.host == host && token.expiry > Utc::now())
            .cloned()
    }

    /// Invalidate the token for a specific host
    fn invalidate_token(&mut self, host: &str) -> Result<()> {
        self.tokens.retain(|token| token.host != host);
        self.save()?;
        Ok(())
    }
}

/// Structure to store the Solscan cookies and token
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolscanCookies {
    pub cf_clearance: String,
    pub token: String,
    pub user_agent: String,
    #[serde(with = "chrono::serde::ts_seconds")]
    pub expiry: DateTime<Utc>,
}

impl std::fmt::Display for SolscanCookies {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "SolscanCookies {{ cf_clearance: {}, token: {}, expiry: {} }}",
            self.cf_clearance, self.token, self.expiry
        )
    }
}

/// Get the Cloudflare clearance token with file-based caching for a specific host
pub async fn get_cf_clearance_for_host(host: &str) -> Result<CloudflareToken> {
    // Ensure cache directory exists
    if !Path::new(CACHE_DIR).exists() {
        fs::create_dir_all(CACHE_DIR)?;
    }

    let lock_file = format!("{}/cf_token_{}.lock", CACHE_DIR, host.replace(".", "_"));

    // Try to create the lock file - if it exists, another process is refreshing the token
    match OpenOptions::new()
        .write(true)
        .create_new(true)
        .open(&lock_file)
    {
        Ok(_lock_file) => {
            // We got the lock, proceed with token refresh if needed
            let result = get_cf_clearance_with_lock(host).await;

            // Release the lock by deleting the lock file
            let _ = fs::remove_file(&lock_file);

            result
        }
        Err(_) => {
            // Another process is refreshing the token, wait and try to use the cached token
            println!(
                "Another process is refreshing the token for {}, waiting...",
                host
            );

            // Wait for a short time to allow the other process to finish
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

            // Try to load the token from cache
            let cache = TokenCache::load()?;
            if let Some(token) = cache.get_token(host) {
                println!(
                    "Successfully loaded token from cache for {} after waiting",
                    host
                );
                Ok(token)
            } else {
                // If still no valid token, wait longer and try again
                tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
                let cache = TokenCache::load()?;
                if let Some(token) = cache.get_token(host) {
                    println!(
                        "Successfully loaded token from cache for {} after extended wait",
                        host
                    );
                    Ok(token)
                } else {
                    Err(anyhow!(
                        "Failed to get token for {} after waiting for other process",
                        host
                    ))
                }
            }
        }
    }
}

/// Convenience function to get the Cloudflare clearance token for solscan.io
pub async fn get_cf_clearance() -> Result<CloudflareToken> {
    get_cf_clearance_for_host("solscan.io").await
}

/// Convenience function to get the Cloudflare clearance token for gmgn.ai
pub async fn get_cf_clearance_gmgn() -> Result<CloudflareToken> {
    get_cf_clearance_for_host("gmgn.ai").await
}

/// Force refresh the Cloudflare clearance token for a specific host, ignoring any cached token
pub async fn force_refresh_cf_clearance_for_host(host: &str) -> Result<CloudflareToken> {
    println!("Forcing refresh of Cloudflare token for {}...", host);

    // Ensure cache directory exists
    if !Path::new(CACHE_DIR).exists() {
        fs::create_dir_all(CACHE_DIR)?;
    }

    let lock_file = format!("{}/cf_token_{}.lock", CACHE_DIR, host.replace(".", "_"));

    // Try to create the lock file - if it exists, another process is refreshing the token
    match OpenOptions::new()
        .write(true)
        .create_new(true)
        .open(&lock_file)
    {
        Ok(_lock_file) => {
            // We got the lock, invalidate the current token and refresh
            let mut cache = TokenCache::load()?;
            cache.invalidate_token(host)?;

            // Now refresh the token
            let result = get_cf_clearance_with_lock(host).await;

            // Release the lock by deleting the lock file
            let _ = fs::remove_file(&lock_file);

            result
        }
        Err(_) => {
            // Another process is refreshing the token, wait
            println!(
                "Another process is refreshing the token for {}, waiting...",
                host
            );

            // Wait for the other process to finish
            tokio::time::sleep(tokio::time::Duration::from_secs(15)).await;

            // Try to load the token from cache
            let cache = TokenCache::load()?;
            if let Some(token) = cache.get_token(host) {
                println!(
                    "Successfully loaded refreshed token for {} from cache",
                    host
                );
                Ok(token)
            } else {
                Err(anyhow!("Failed to get refreshed token for {}", host))
            }
        }
    }
}

/// Convenience function to force refresh the Cloudflare clearance token for solscan.io
pub async fn force_refresh_cf_clearance() -> Result<CloudflareToken> {
    force_refresh_cf_clearance_for_host("solscan.io").await
}

/// Convenience function to force refresh the Cloudflare clearance token for gmgn.ai
pub async fn force_refresh_cf_clearance_gmgn() -> Result<CloudflareToken> {
    force_refresh_cf_clearance_for_host("gmgn.ai").await
}

/// Get the Cloudflare clearance token with an exclusive lock for a specific host
async fn get_cf_clearance_with_lock(host: &str) -> Result<CloudflareToken> {
    // Load the token cache
    let mut cache = TokenCache::load()?;

    // Check if we have a valid token for this host
    if let Some(token) = cache.get_token(host) {
        return Ok(token);
    }

    println!("No valid token in cache for {}, refreshing...", host);

    // Construct the URL for the host
    let url = format!("https://{}/", host);

    // Token is not valid, refresh it
    let output = Command::new("python3")
        .arg(PYTHON_SCRIPT)
        .arg(&url)
        .output()
        .map_err(|e| {
            println!("Failed to execute Python script: {}", e);
            anyhow!("Failed to execute Python script: {}", e)
        })?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("Python script error: {}", stderr);
        return Err(anyhow!("Python script failed: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    println!("Python script output: {}", stdout);

    // Extract the JSON part from the output
    // Find the first occurrence of '{'
    let json_start = stdout.find('{').ok_or_else(|| {
        println!("No JSON found in Python script output");
        anyhow!("No JSON found in Python script output")
    })?;

    // Parse only the JSON part
    let json_str = &stdout[json_start..];
    let result: serde_json::Value = serde_json::from_str(json_str).map_err(|e| {
        println!("Failed to parse JSON output: {}", e);
        anyhow!("Failed to parse JSON output: {}", e)
    })?;

    // Check if the script was successful
    if !result["success"].as_bool().unwrap_or(false) {
        let error_msg = result["error"].as_str().unwrap_or("Unknown error");
        println!("Script reported failure: {}", error_msg);
        return Err(anyhow!("Script reported failure: {}", error_msg));
    }

    // Extract the token and user agent
    let cf_clearance = result["cf_clearance"]
        .as_str()
        .ok_or_else(|| {
            println!("Missing cf_clearance in response");
            anyhow!("Missing cf_clearance in response")
        })?
        .to_string();

    let user_agent = result["user_agent"]
        .as_str()
        .ok_or_else(|| {
            println!("Missing user_agent in response");
            anyhow!("Missing user_agent in response")
        })?
        .to_string();

    // Set the token in the cache with a 2-hour expiry
    cache.set_token(
        cf_clearance.clone(),
        user_agent.clone(),
        host.to_string(),
        120,
    )?;

    println!("Successfully refreshed Cloudflare token for {}", host);

    // Return the token
    Ok(CloudflareToken {
        cf_clearance,
        user_agent,
        host: host.to_string(),
        expiry: Utc::now() + Duration::minutes(120),
    })
}

/// Get the Cloudflare clearance token for a specific host (legacy function for compatibility)
pub async fn get_cf_clearance_legacy_for_host(host: &str) -> Result<(String, String)> {
    let token = get_cf_clearance_for_host(host).await?;
    Ok((token.cf_clearance, token.user_agent.clone()))
}

/// Legacy function for compatibility (defaults to solscan.io)
pub async fn get_cf_clearance_legacy() -> Result<(String, String)> {
    get_cf_clearance_legacy_for_host("solscan.io").await
}

/// Get the user agent from the cache for a specific host
pub fn get_user_agent_for_host(host: &str) -> Result<String> {
    let cache = TokenCache::load()?;

    Ok(cache.get_token(host).map_or_else(
        || "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36".to_string(),
        |token| token.user_agent.clone()
    ))
}

/// Get the user agent from the cache (defaults to solscan.io)
pub fn get_user_agent() -> Result<String> {
    get_user_agent_for_host("solscan.io")
}

/// Invalidate the Cloudflare clearance token for a specific host without refreshing it
pub fn invalidate_cf_clearance_for_host(host: &str) -> Result<()> {
    println!("Invalidating Cloudflare token for {}...", host);

    // Ensure cache directory exists
    if !Path::new(CACHE_DIR).exists() {
        fs::create_dir_all(CACHE_DIR)?;
    }

    // Load the token cache
    let mut cache = TokenCache::load()?;

    // Invalidate the token
    cache.invalidate_token(host)?;

    println!("Successfully invalidated Cloudflare token for {}", host);
    Ok(())
}

/// Convenience function to invalidate the Cloudflare clearance token for solscan.io
pub fn invalidate_cf_clearance() -> Result<()> {
    invalidate_cf_clearance_for_host("solscan.io")
}

/// Convenience function to invalidate the Cloudflare clearance token for gmgn.ai
pub fn invalidate_cf_clearance_gmgn() -> Result<()> {
    invalidate_cf_clearance_for_host("gmgn.ai")
}

/// Get trending tokens using a headless browser approach with caching
pub async fn get_trending_tokens_with_cache(chain: &str) -> Result<String> {
    // Check if we have a cached response
    let cache_dir = "cache";
    if !Path::new(cache_dir).exists() {
        fs::create_dir_all(cache_dir)?;
    }

    let cache_file = format!("{}/trending_tokens_{}.json", cache_dir, chain);

    // Check if the cache file exists and is less than 5 minutes old
    if Path::new(&cache_file).exists() {
        let metadata = fs::metadata(&cache_file)?;
        let modified = metadata.modified()?;
        let now = std::time::SystemTime::now();
        let duration = now.duration_since(modified)?;

        if duration.as_secs() < 300 {
            // 5 minutes
            let contents = fs::read_to_string(&cache_file)?;
            return Ok(contents);
        }
    }

    // Get fresh data using the Python script
    let result = get_trending_tokens_with_python(chain).await?;

    // Cache the result
    fs::write(&cache_file, &result)?;

    Ok(result)
}

/// Get trending tokens using the Python script
async fn get_trending_tokens_with_python(_chain: &str) -> Result<String> {
    // Ensure the scripts directory exists
    let scripts_dir = "scripts";
    if !Path::new(scripts_dir).exists() {
        fs::create_dir_all(scripts_dir)?;
    }

    // Run the Python script with the appropriate arguments
    // Note: We always request both 6h and 24h data, regardless of is_24h parameter
    let output = AsyncCommand::new("python3")
        .arg(format!("{}/debug_trending_tokens.py", scripts_dir))
        .arg("clean")
        .output()
        .await?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow!("Failed to run Python script: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    return Ok(stdout.to_string());
}

/// Get token traders using a headless browser approach with caching
pub async fn get_token_traders_with_cache(chain: &str, token_address: &str) -> Result<String> {
    // Check if we have a cached response
    let cache_dir = "cache";
    if !Path::new(cache_dir).exists() {
        fs::create_dir_all(cache_dir)?;
    }

    let cache_file = format!(
        "{}/token_traders_{}_{}.json",
        cache_dir, chain, token_address
    );

    // Check if the cache file exists and is less than 5 minutes old
    if Path::new(&cache_file).exists() {
        let metadata = fs::metadata(&cache_file)?;
        let modified = metadata.modified()?;
        let now = std::time::SystemTime::now();
        let duration = now.duration_since(modified)?;

        if duration.as_secs() < 300 {
            // 5 minutes
            let contents = fs::read_to_string(&cache_file)?;
            return Ok(contents);
        }
    }

    // Get fresh data using the Python script
    let result = get_token_traders_with_python(chain, token_address).await?;

    // Cache the result
    fs::write(&cache_file, &result)?;

    Ok(result)
}

/// Get token traders using the Python script
async fn get_token_traders_with_python(_chain: &str, token_address: &str) -> Result<String> {
    // Ensure the scripts directory exists
    let scripts_dir = "scripts";
    if !Path::new(scripts_dir).exists() {
        fs::create_dir_all(scripts_dir)?;
    }

    // Run the Python script with the appropriate arguments
    let output = AsyncCommand::new("python3")
        .arg(format!("{}/debug_token_traders.py", scripts_dir))
        .arg(token_address)
        .arg("clean")
        .output()
        .await?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow!("Failed to run Python script: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);

    // Try to parse the output as JSON
    match serde_json::from_str::<serde_json::Value>(&stdout) {
        Ok(_) => Ok(stdout.to_string()),
        Err(_e) => {
            // If it's not valid JSON, it might be an error message
            if stdout.contains("error") {
                return Err(anyhow!("Python script error: {}", stdout));
            }
            // Otherwise, return the raw output
            Ok(stdout.to_string())
        }
    }
}

/// Get token traders for multiple tokens using the Python script
pub async fn get_multiple_token_traders_with_python(
    _chain: &str,
    token_addresses: &[String],
) -> Result<HashMap<String, String>> {
    // Ensure the scripts directory exists
    let scripts_dir = "scripts";
    if !Path::new(scripts_dir).exists() {
        fs::create_dir_all(scripts_dir)?;
    }

    // Run the Python script with the appropriate arguments
    let mut command = AsyncCommand::new("python3");
    command
        .arg(format!("{}/debug_token_traders.py", scripts_dir))
        .arg("clean");

    // Add all token addresses as arguments
    for address in token_addresses {
        command.arg(address);
    }

    let output = command.output().await?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow!("Failed to run Python script: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);

    // Try to parse the output as JSON
    match serde_json::from_str::<HashMap<String, serde_json::Value>>(&stdout) {
        Ok(results) => {
            // Convert the results to a HashMap<String, String>
            let mut string_results = HashMap::new();
            for (key, value) in results {
                string_results.insert(key, value.to_string());
            }
            Ok(string_results)
        }
        Err(_e) => {
            // If it's not valid JSON, it might be an error message
            if stdout.contains("error") {
                return Err(anyhow!("Python script error: {}", stdout));
            }
            // Otherwise, return an empty HashMap
            Ok(HashMap::new())
        }
    }
}

/// Get wallet summary data using browser automation
pub async fn get_wallet_summary_data_with_browser(_chain: &str, wallet: &str) -> Result<String> {
    // Ensure the scripts directory exists
    let scripts_dir = "scripts";
    if !Path::new(scripts_dir).exists() {
        fs::create_dir_all(scripts_dir)?;
    }

    // Run the Python script with the appropriate arguments
    let output = AsyncCommand::new("python3")
        .arg(format!("{}/debug_wallet_analysis.py", scripts_dir))
        .arg(wallet)
        .arg("clean")
        .output()
        .await?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow!("Failed to run Python script: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);

    // Parse the output as JSON
    let result: HashMap<String, serde_json::Value> = serde_json::from_str(&stdout)?;

    // Extract the wallet summary data for the specific wallet
    if let Some(wallet_data) = result.get(wallet) {
        if let Some(summary) = wallet_data.get("wallet_summary") {
            return Ok(summary.to_string());
        }

        // Check if there's an error message in the response
        if let Some(error) = wallet_data.get("error") {
            return Err(anyhow!("Error analyzing wallet {}: {}", wallet, error));
        }
    }

    Err(anyhow!(
        "Failed to extract wallet summary data for {}",
        wallet
    ))
}

/// Get wallet holdings data using browser automation
pub async fn get_wallet_holdings_with_browser(_chain: &str, wallet: &str) -> Result<String> {
    // Ensure the scripts directory exists
    let scripts_dir = "scripts";
    if !Path::new(scripts_dir).exists() {
        fs::create_dir_all(scripts_dir)?;
    }

    // Run the Python script with the appropriate arguments
    let output = AsyncCommand::new("python3")
        .arg(format!("{}/debug_wallet_analysis.py", scripts_dir))
        .arg(wallet)
        .arg("clean")
        .output()
        .await?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow!("Failed to run Python script: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);

    // Parse the output as JSON
    let result: HashMap<String, serde_json::Value> = serde_json::from_str(&stdout)?;

    // Extract the wallet holdings data for the specific wallet
    if let Some(wallet_data) = result.get(wallet) {
        if let Some(holdings) = wallet_data.get("wallet_holdings") {
            return Ok(holdings.to_string());
        }

        // Check if there's an error message in the response
        if let Some(error) = wallet_data.get("error") {
            return Err(anyhow!("Error analyzing wallet {}: {}", wallet, error));
        }
    }

    Err(anyhow!(
        "Failed to extract wallet holdings data for {}",
        wallet
    ))
}

/// Analyze multiple wallets in batch using browser automation
pub async fn analyze_wallets_batch_with_browser(
    wallets: &HashSet<String>,
    scroll: bool,
) -> Result<HashMap<String, HashMap<String, serde_json::Value>>> {
    // Run the Python script with wallet addresses and clean flag
    let mut command = std::process::Command::new("python3");
    command.arg("scripts/debug_wallet_analysis.py");
    for wallet in wallets {
        command.arg(wallet);
    }
    command.arg("clean");
    if !scroll {
        command.arg("no_scroll");
    }

    let output = command.output()?;

    // Check if the script executed successfully
    if !output.status.success() {
        return Err(anyhow::anyhow!(
            "Failed to execute wallet analysis script: {}",
            String::from_utf8_lossy(&output.stderr)
        ));
    }

    // Parse the output JSON
    let output_str = String::from_utf8_lossy(&output.stdout);
    let wallet_data: HashMap<String, HashMap<String, serde_json::Value>> =
        serde_json::from_str(&output_str)?;

    Ok(wallet_data)
}

/// Get Solscan cookies and token with file-based caching
pub async fn get_solscan_cookies() -> Result<SolscanCookies> {
    // Ensure cache directory exists
    if !Path::new(CACHE_DIR).exists() {
        fs::create_dir_all(CACHE_DIR)?;
    }

    let lock_file = format!("{}/solscan_cookies.lock", CACHE_DIR);

    // Try to create the lock file - if it exists, another process is refreshing the cookies
    match OpenOptions::new()
        .write(true)
        .create_new(true)
        .open(&lock_file)
    {
        Ok(_lock_file) => {
            // We got the lock, proceed with cookie refresh if needed
            let result = get_solscan_cookies_with_lock().await;

            // Release the lock by deleting the lock file
            let _ = fs::remove_file(&lock_file);

            result
        }
        Err(_) => {
            // Another process is refreshing the cookies, wait and try to use the cached cookies
            // println!("Another process is refreshing the Solscan cookies, waiting...");

            // Wait for 1 second at a time, up to 1 minute total
            for _ in 0..120 {
                // Wait for 1 second
                tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;

                // Try to load the cookies from cache
                let cache = TokenCache::load()?;
                if let Some(token) = cache.get_token("solscan.io") {
                    // println!("Successfully loaded Solscan cookies from cache after waiting");
                    return Ok(SolscanCookies {
                        cf_clearance: token.cf_clearance,
                        token: token.user_agent.clone(), // We'll use user_agent field to store the token
                        user_agent: token.user_agent.clone(),
                        expiry: token.expiry,
                    });
                }

                // Check if lock file is gone
                if !Path::new(&lock_file).exists() {
                    break;
                }
            }

            // If we get here, we've waited the full minute or the lock is gone
            let cache = TokenCache::load()?;
            if let Some(token) = cache.get_token("solscan.io") {
                // println!("Successfully loaded Solscan cookies from cache after waiting");
                Ok(SolscanCookies {
                    cf_clearance: token.cf_clearance,
                    token: token.user_agent.clone(),
                    user_agent: token.user_agent.clone(),
                    expiry: token.expiry,
                })
            } else {
                Err(anyhow!(
                    "Failed to get Solscan cookies after waiting for 1 minute"
                ))
            }
        }
    }
}

/// Get Solscan cookies and token with an exclusive lock
async fn get_solscan_cookies_with_lock() -> Result<SolscanCookies> {
    // Load the token cache
    let mut cache = TokenCache::load()?;

    // Check if we have valid cookies
    if let Some(token) = cache.get_token("solscan.io") {
        if token.cf_clearance != "" {
            return Ok(SolscanCookies {
                cf_clearance: token.cf_clearance,
                token: token.user_agent.clone(), // We'll use user_agent field to store the token
                user_agent: token.user_agent.clone(),
                expiry: token.expiry,
            });
        }
    }

    println!("No valid Solscan cookies in cache, refreshing...");

    let message = format!("⚠️ Solscan refreshing cookies, needs manual check");
    if let Err(e) = wallet_transfer_analyzer::send_slack_notification(&message).await {
        warn!("Failed to send Slack notification: {}", e);
    }

    // Run the Python script to get cookies
    let output = Command::new("python3")
        .arg("py/get_solscan_cookies.py")
        .output()
        .map_err(|e| {
            println!("Failed to execute Python script: {}", e);
            anyhow!("Failed to execute Python script: {}", e)
        })?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("Python script error: {}", stderr);
        return Err(anyhow!("Python script failed: {}", stderr));
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    println!("Python script output: {}", stdout);

    // Parse the JSON output
    let result: serde_json::Value = serde_json::from_str(&stdout).map_err(|e| {
        println!("Failed to parse JSON output: {}", e);
        anyhow!("Failed to parse JSON output: {}", e)
    })?;

    // Check if the script was successful
    if !result["success"].as_bool().unwrap_or(false) {
        let error_msg = result["error"].as_str().unwrap_or("Unknown error");
        println!("Script reported failure: {}", error_msg);
        return Err(anyhow!("Script reported failure: {}", error_msg));
    }

    // Extract the cookies and token
    let cf_clearance = result["cf_clearance"]
        .as_str()
        .ok_or_else(|| {
            println!("Missing cf_clearance in response");
            anyhow!("Missing cf_clearance in response")
        })?
        .to_string();

    let token = result["token"]
        .as_str()
        .ok_or_else(|| {
            println!("Missing token in response");
            anyhow!("Missing token in response")
        })?
        .to_string();

    let user_agent = result["user_agent"]
        .as_str()
        .ok_or_else(|| {
            println!("Missing user_agent in response");
            anyhow!("Missing user_agent in response")
        })?
        .to_string();

    // Set the cookies in the cache with a 2-hour expiry
    cache.set_token(
        cf_clearance.clone(),
        user_agent.clone(), // Store the actual user agent
        "solscan.io".to_string(),
        720,
    )?;

    println!("Successfully refreshed Solscan cookies");

    // Return the cookies
    Ok(SolscanCookies {
        cf_clearance,
        token,
        user_agent,
        expiry: Utc::now() + Duration::minutes(720),
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_cf_clearance() {
        let cf_clearance = get_cf_clearance().await.unwrap();
        println!("cf_clearance for solscan.io: {}", cf_clearance.cf_clearance);
        assert!(!cf_clearance.cf_clearance.is_empty());
    }

    #[tokio::test]
    async fn test_get_cf_clearance_gmgn() {
        let cf_clearance = get_cf_clearance_gmgn().await.unwrap();
        println!("cf_clearance for gmgn.ai: {}", cf_clearance.cf_clearance);
        assert!(!cf_clearance.cf_clearance.is_empty());
    }
}

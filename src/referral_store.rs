use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::{self, File};
use std::path::PathBuf;

const REFERRAL_STORE_FILENAME: &str = "referral_store.json";

#[derive(Debug, Serialize, Deserialize)]
pub struct ReferralStore {
    referrals: HashMap<i64, String>,   // user_id -> referral_code
    group_inviters: HashMap<i64, i64>, // group_id -> inviter_id
}

impl ReferralStore {
    pub fn new() -> Self {
        Self {
            referrals: HashMap::new(),
            group_inviters: HashMap::new(),
        }
    }

    pub fn load() -> Result<Self> {
        let path = Self::get_store_path()?;
        Self::ensure_store_dir()?;

        if !path.exists() {
            File::create(&path)?;
            return Ok(Self::new());
        }

        let content = fs::read_to_string(path)?;
        if content.trim().is_empty() {
            return Ok(Self::new());
        }
        let store: ReferralStore = serde_json::from_str(&content)?;
        Ok(store)
    }

    pub fn save(&self) -> Result<()> {
        let path = Self::get_store_path()?;
        Self::ensure_store_dir()?;

        let content = serde_json::to_string_pretty(self)?;
        fs::write(path, content)?;
        Ok(())
    }

    fn ensure_store_dir() -> Result<()> {
        let home = dirs::home_dir().ok_or_else(|| anyhow!("Could not find home directory"))?;
        let store_dir = home.join(".solana-bot");
        if !store_dir.exists() {
            fs::create_dir_all(&store_dir)?;
        }
        Ok(())
    }

    fn get_store_path() -> Result<PathBuf> {
        let home = dirs::home_dir().ok_or_else(|| anyhow!("Could not find home directory"))?;
        Ok(home.join(".solana-bot").join(REFERRAL_STORE_FILENAME))
    }

    pub fn add_referral(&mut self, user_id: i64, referral_code: String) -> Result<()> {
        if !Self::is_valid_referral_code(&referral_code) {
            return Err(anyhow!("Invalid referral code format"));
        }
        self.referrals.insert(user_id, referral_code);
        self.save()?;
        Ok(())
    }

    pub fn add_group_inviter(&mut self, group_id: i64, inviter_id: i64) -> Result<()> {
        self.group_inviters.insert(group_id, inviter_id);
        self.save()?;
        Ok(())
    }

    pub fn get_referral_code(&self, user_id: i64) -> Option<&String> {
        self.referrals.get(&user_id)
    }

    pub fn get_group_inviter(&self, group_id: i64) -> Option<&i64> {
        self.group_inviters.get(&group_id)
    }

    pub fn is_valid_referral_code(code: &str) -> bool {
        code.len() == 7 && code.chars().all(|c| c.is_alphanumeric())
    }
}

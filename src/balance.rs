use anyhow::Result;
use log::error;
use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;
use std::{collections::HashMap, thread::sleep, time::Duration};

pub fn get_wallets_balance(
    client: &RpcClient,
    tracked_pub_keys: &Vec<Pubkey>,
) -> Result<HashMap<String, u64>> {
    let mut result = HashMap::new();
    for chunk in tracked_pub_keys.chunks(100) {
        let mut attempts = 0;
        let accounts = loop {
            match client.get_multiple_accounts(chunk) {
                Ok(accounts) => break accounts,
                Err(e) if attempts < 5 => {
                    attempts += 1;
                    error!("Failed to get accounts, retrying ({}/5): {:?}", attempts, e);
                    sleep(Duration::from_millis(500));
                }
                Err(e) => return Err(e.into()),
            }
        };
        result.extend(chunk.iter().zip(accounts.iter()).filter_map(|(tw, acc)| {
            acc.as_ref()
                .map(|account| (tw.to_string(), account.lamports))
        }));
    }
    Ok(result)
}

pub fn get_token_account_balance(client: &RpcClient, token_account: &Pubkey) -> Result<u64> {
    match client.get_token_account_balance(token_account) {
        Ok(balance) => Ok(balance.amount.parse::<u64>().unwrap_or(0)),
        Err(e) => {
            if format!("{:?}", e).contains("could not find account") {
                Ok(0)
            } else {
                Err(anyhow::anyhow!(
                    "Failed to get token account balance for {}: {:?}",
                    token_account.to_string(),
                    e
                ))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::{get_token_account_balance, get_wallets_balance, WSOL_MINT};
    use solana_client::rpc_client::RpcClient;
    use solana_sdk::pubkey::Pubkey;
    use spl_associated_token_account::get_associated_token_address;

    #[test]
    fn test_get_token_account_balance_success() {
        let client = RpcClient::new("https://api.mainnet-beta.solana.com".to_string());
        let owner = Pubkey::from_str_const("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8");
        let pool_wsol_account =
            get_associated_token_address(&owner, &Pubkey::from_str_const(WSOL_MINT));
        let result = get_token_account_balance(&client, &pool_wsol_account);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 0);
    }

    #[test]
    fn test_get_wallets_balance() {
        let client = RpcClient::new("https://api.mainnet-beta.solana.com".to_string());

        // Test with three known Solana accounts
        let addresses = vec![
            "5cBpjm5r3oVrztZUZfnCZ5Us4CEVAX1Ub9XF1ASFi9dn",
            "6a6Pf8QLucuUSGzxQ4Ktce49duEwZfSUUZWzpLAnbbT3",
            "BkqmEiyAPn4SSktSEj2d59tvXJRNFZByc1vpzkZ2oMCD",
        ];

        let pubkeys: Vec<Pubkey> = addresses
            .iter()
            .map(|addr| Pubkey::try_from(*addr).unwrap())
            .collect();

        let result = get_wallets_balance(&client, &pubkeys);
        assert!(result.is_ok(), "Failed to get wallet balances");

        let balances = result.unwrap();
        assert_eq!(balances.len(), 3, "Should have received 3 account balances");

        // Verify each address has a non-zero balance
        for address in addresses {
            assert!(
                balances.contains_key(address),
                "Balance not found for address {}",
                address
            );
            assert!(
                balances[address] > 0,
                "Balance should be greater than 0 for address {}",
                address
            );
        }
    }
}

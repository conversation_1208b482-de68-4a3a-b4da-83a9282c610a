use anyhow::Result;
use futures::future::join_all;
use log::{debug, error, info};
use once_cell::sync::Lazy;
use reqwest::Client;
use serde::Serialize;
use std::time::{SystemTime, UNIX_EPOCH};

// Base URLs for the API
const API_URLS: [&str; 3] = [
    "https://api-68779486420.asia-east1.run.app",
    "https://api-366580911590.asia-east1.run.app",
    "https://api-307061773427.asia-east1.run.app",
];

// Request body for buy signal API
#[derive(Serialize, Clone)]
pub struct UpsertBuySignalReq {
    pub token_address: String,
    pub smart_wallet_count: i32,
    pub buy_entry_price: f64,
    pub emit_time: i64,
    pub telegram_link: String,
    pub win_rate: f64,
    pub average_holding: f64,
    pub average_win_rate: f64,
    pub highest_price: f64,
    pub symbol: String,
}

// Request body for batch buy signals API
#[derive(Serialize)]
pub struct BatchBuySignalReq {
    pub signals: Vec<UpsertBuySignalReq>,
}

// Request body for sell signal API
#[derive(Serialize)]
pub struct AddSellSignalReq {
    pub token_address: String,
    pub telegram_link: String,
    pub highest_gain: f64,
}

// API client for sending signals
pub struct ApiClient {
    client: Client,
}

impl ApiClient {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
        }
    }

    // Send buy signal to API
    pub async fn send_buy_signal(&self, req: UpsertBuySignalReq) -> Result<()> {
        let endpoint = "/_v/token_signal/buy";
        let api_endpoints = API_URLS
            .iter()
            .map(|base_url| format!("{}{}", base_url, endpoint))
            .collect::<Vec<String>>();

        let futures = api_endpoints.iter().map(|url| {
            let client = &self.client;
            let req_clone = &req;
            async move {
                match client.post(url).json(req_clone).send().await {
                    Ok(response) => {
                        if !response.status().is_success() {
                            error!(
                                "Failed to send buy signal to {} for token {}: HTTP {}",
                                url,
                                req_clone.token_address,
                                response.status()
                            );
                        }
                        Ok(())
                    }
                    Err(e) => {
                        error!(
                            "Error sending buy signal to {} for token {}: {}",
                            url, req_clone.token_address, e
                        );
                        Err(anyhow::anyhow!("API request to {} failed: {}", url, e))
                    }
                }
            }
        });

        let results = join_all(futures).await;

        // If any request succeeded, consider the operation successful
        if results.iter().any(|r| r.is_ok()) {
            Ok(())
        } else {
            // All requests failed
            Err(anyhow::anyhow!("All API requests for buy signal failed"))
        }
    }

    // Send batch buy signals to API
    pub async fn batch_send_buy_signals(&self, signals: Vec<UpsertBuySignalReq>) -> Result<()> {
        let endpoint = "/_v/token_signal/buy/batch";
        let api_endpoints = API_URLS
            .iter()
            .map(|base_url| format!("{}{}", base_url, endpoint))
            .collect::<Vec<String>>();

        let req = BatchBuySignalReq { signals };

        debug!(
            "Sending batch of {} buy signals to multiple endpoints",
            req.signals.len()
        );

        let futures = api_endpoints.iter().map(|url| {
            let client = &self.client;
            let req_clone = &req;
            async move {
                match client.post(url).json(req_clone).send().await {
                    Ok(response) => {
                        if response.status().is_success() {
                            debug!(
                                "Successfully sent batch buy signals to {} for {} tokens",
                                url,
                                req_clone.signals.len()
                            );
                        } else {
                            error!(
                                "Failed to send batch buy signals to {}: HTTP {}",
                                url,
                                response.status()
                            );
                        }
                        Ok(())
                    }
                    Err(e) => {
                        error!("Error sending batch buy signals to {}: {}", url, e);
                        Err(anyhow::anyhow!("API request to {} failed: {}", url, e))
                    }
                }
            }
        });

        let results = join_all(futures).await;

        // If any request succeeded, consider the operation successful
        if results.iter().any(|r| r.is_ok()) {
            Ok(())
        } else {
            // All requests failed
            Err(anyhow::anyhow!(
                "All API requests for batch buy signals failed"
            ))
        }
    }

    // Send sell signal to API
    pub async fn send_sell_signal(&self, req: AddSellSignalReq) -> Result<()> {
        let endpoint = "/_v/token_signal/sell";
        let api_endpoints = API_URLS
            .iter()
            .map(|base_url| format!("{}{}", base_url, endpoint))
            .collect::<Vec<String>>();

        info!(
            "Sending sell signal for token {} to multiple endpoints",
            req.token_address
        );

        let futures = api_endpoints.iter().map(|url| {
            let client = &self.client;
            let req_clone = &req;
            async move {
                match client.post(url).json(req_clone).send().await {
                    Ok(response) => {
                        if response.status().is_success() {
                            info!(
                                "Successfully sent sell signal to {} for token {}",
                                url, req_clone.token_address
                            );
                        } else {
                            error!(
                                "Failed to send sell signal to {} for token {}: HTTP {}",
                                url,
                                req_clone.token_address,
                                response.status()
                            );
                        }
                        Ok(())
                    }
                    Err(e) => {
                        error!(
                            "Error sending sell signal to {} for token {}: {}",
                            url, req_clone.token_address, e
                        );
                        Err(anyhow::anyhow!("API request to {} failed: {}", url, e))
                    }
                }
            }
        });

        let results = join_all(futures).await;

        // If any request succeeded, consider the operation successful
        if results.iter().any(|r| r.is_ok()) {
            Ok(())
        } else {
            // All requests failed
            Err(anyhow::anyhow!("All API requests for sell signal failed"))
        }
    }

    // Helper to calculate current timestamp in seconds
    pub fn current_timestamp() -> i64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64
    }

    // Calculate win rate based on wallet holding ratios
    pub fn calculate_win_rate(wallet_ratios: &[f64]) -> f64 {
        if wallet_ratios.is_empty() {
            return 0.0;
        }

        // Calculate average ratio
        let sum: f64 = wallet_ratios.iter().sum();
        let avg_ratio = sum / wallet_ratios.len() as f64;

        // Map ratio to win rate (higher ratio = higher win rate)
        // Using a simple linear mapping: 1.0 ratio -> 85% win rate, 0.0 ratio -> 0% win rate
        ((avg_ratio.min(1.0).max(0.0) * 0.85) * 100.0).round() / 100.0
    }

    pub fn get_win_rate_v2(
        avg_wallet_ratio: f64,    // 0-1.0
        avg_wallet_win_rate: f64, // 0-1.0
        price_change: f64,
    ) -> f64 {
        // Convert price change to inverse score (higher price change = lower score)
        let price_score = match price_change {
            p if p <= 0.2 => 1.0, // 0-20% price increase
            p if p <= 0.5 => 0.8, // 20-50% price increase
            p if p <= 1.0 => 0.6, // 50-100% price increase
            p if p <= 2.0 => 0.4, // 100-200% price increase
            _ => 0.2,             // 200%+ price increase
        };

        // Calculate weighted components
        let holdings_component = avg_wallet_ratio * 0.5; // 50% weight
        let price_component = price_score * 0.3; // 30% weight
        let win_rate_component = avg_wallet_win_rate * 0.2; // 20% weight

        // Base score from weighted sum
        let base_score = holdings_component + price_component + win_rate_component;

        // Apply penalty for extremely low wallet win rate
        let win_rate_penalty = if avg_wallet_win_rate < 0.8 {
            0.8 - avg_wallet_win_rate
        } else {
            0.0 // No penalty for reasonable win rates
        };

        // Adjust score with penalty
        let adjusted_score = base_score * (1.0 - win_rate_penalty);

        // Ensure result is between 0.1 and 0.9
        (adjusted_score.min(0.9).max(0.1) * 100.0).round() / 100.0
    }

    pub fn get_win_rate_v3(
        avg_wallet_ratio: f64,    // 0-1.0
        avg_wallet_win_rate: f64, // 0-1.0
        price_change: f64,        // Positive if price increased, negative if decreased
    ) -> f64 {
        // Step function for wallet ratio multiplier
        let ratio_multiplier = match avg_wallet_ratio {
            r if r >= 0.8 => 1.0, // 80-100% holdings
            r if r >= 0.6 => 0.9, // 60-80% holdings
            r if r >= 0.4 => 0.8, // 40-60% holdings
            r if r >= 0.2 => 0.7, // 20-40% holdings
            _ => 0.6,             // 0-20% holdings
        };

        // Logarithmic function for price change effect
        // Using natural log of (1 + abs(price_change)) with scaling
        let price_change_factor = (1.0 + price_change.abs()).ln() * 0.4;

        // Apply different multipliers based on price change direction
        let price_multiplier = if price_change < 0.0 {
            // Price decreased - higher win probability
            1.0 + price_change_factor
        } else {
            // Price increased - lower win probability
            1.0 / (1.0 + price_change_factor)
        };

        // Calculate adjusted win rate
        let adjusted_win_rate = avg_wallet_win_rate * ratio_multiplier * price_multiplier;

        // Ensure result is between 0.1 and 0.9 and round to 0.001
        (adjusted_win_rate.min(0.8).max(0.26) * 1000.0).round() / 1000.0
    }
}

// Global instance that can be shared across threads
pub static API_CLIENT: Lazy<ApiClient> = Lazy::new(|| ApiClient::new());
// End of Selection

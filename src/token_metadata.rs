use anyhow::Result;
use log::{debug, error};
use mpl_token_metadata::accounts::Metadata;
use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

pub const TOKEN_METADATA_PROGRAM_ID: &str = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s";

pub async fn get_token_info(client: &RpcClient, mint_address: &Pubkey) -> Result<(String, u8)> {
    // Get token symbol from metadata
    let symbol = get_token_symbol(client, mint_address).await?;

    // Get decimals from mint account
    let mint_account = client.get_account(mint_address)?;
    let decimals = mint_account.data[44];

    debug!("Token Symbol: {}, Decimals: {}", symbol, decimals);
    Ok((symbol, decimals))
}

pub async fn get_token_symbol(client: &RpcClient, mint_pubkey: &Pubkey) -> Result<String> {
    // Find metadata account
    let metadata_program_id = Pubkey::from_str(TOKEN_METADATA_PROGRAM_ID)?;
    let seeds = &[
        b"metadata",
        metadata_program_id.as_ref(),
        mint_pubkey.as_ref(),
    ];
    let (metadata_address, _) = Pubkey::find_program_address(seeds, &metadata_program_id);

    // Get metadata account
    let metadata_account = client.get_account(&metadata_address)?;

    // Deserialize metadata using Metaplex SDK
    let metadata = Metadata::from_bytes(&metadata_account.data);
    match metadata {
        Ok(metadata) => {
            debug!("Token name: {}", metadata.name);
            debug!("Token symbol: {}", metadata.symbol);
            debug!("Token URI: {}", metadata.uri);
            debug!("Update authority: {}", metadata.update_authority);
            debug!("Mint: {}", metadata.mint);
            Ok(metadata.symbol.trim().trim_end_matches('\0').to_string())
        }
        Err(e) => {
            error!("Failed to deserialize metadata: {}", e);
            Err(anyhow::anyhow!("Failed to deserialize metadata"))
        }
    }
}

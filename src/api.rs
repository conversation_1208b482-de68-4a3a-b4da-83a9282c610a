use crate::state::AppState;
use actix_web::{get, put, web, HttpRequest, HttpResponse, Responder};
use log::{error, info};
use serde::de::Error;
use serde::{Deserialize, Deserializer, Serialize};
use std::env;

#[derive(Debug, Serialize)]
pub struct WalletList {
    pub wallets: Vec<String>,
}

// Custom deserialization implementation to support array format only
impl<'de> Deserialize<'de> for WalletList {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        // Deserialize directly as an array of strings
        let wallets = Vec::<String>::deserialize(deserializer)
            .map_err(|e| D::Error::custom(format!("Invalid wallet array: {}", e)))?;
        Ok(WalletList { wallets })
    }
}

fn check_auth(req: &HttpRequest) -> bool {
    if let Some(auth_header) = req.headers().get("Authorization") {
        if let Ok(auth_value) = auth_header.to_str() {
            let expected_password = env::var("AUTH_PASSWORD").unwrap_or_default();
            return auth_value == expected_password;
        }
    }
    false
}

#[get("/wallets")]
pub async fn get_wallets(req: HttpRequest, state: web::Data<AppState>) -> impl Responder {
    if !check_auth(&req) {
        return HttpResponse::Unauthorized().finish();
    }

    match state.redis.get_wallets() {
        Ok(wallets) => HttpResponse::Ok().json(wallets),
        Err(e) => {
            error!("Failed to get wallets: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

#[put("/wallets")]
pub async fn update_wallets(
    req: HttpRequest,
    state: web::Data<AppState>,
    req_body: web::Json<WalletList>,
) -> impl Responder {
    if !check_auth(&req) {
        return HttpResponse::Unauthorized().finish();
    }

    match state.redis.update_wallets(&req_body.wallets) {
        Ok(_) => HttpResponse::Ok().finish(),
        Err(e) => {
            error!("Failed to update wallets: {}", e);
            HttpResponse::InternalServerError().finish()
        }
    }
}

#[get("/health")]
pub async fn health_check() -> impl Responder {
    HttpResponse::Ok().finish()
}

#[get("/send-test-message")]
pub async fn send_test_message(req: HttpRequest, state: web::Data<AppState>) -> impl Responder {
    if !check_auth(&req) {
        return HttpResponse::Unauthorized().finish();
    }

    // Check if Telegram bot is initialized
    if let Some(telegram_bot) = &state.telegram_bot {
        match telegram_bot
            .send_message("Hello! This is a test message from the API.")
            .await
        {
            Ok(_) => {
                info!("Test message sent successfully to Telegram channel");
                HttpResponse::Ok().json(serde_json::json!({
                    "status": "success",
                    "message": "Test message sent successfully to Telegram channel"
                }))
            }
            Err(e) => {
                error!("Failed to send test message to Telegram channel: {}", e);
                HttpResponse::InternalServerError().json(serde_json::json!({
                    "status": "error",
                    "message": format!("Failed to send message: {}", e)
                }))
            }
        }
    } else {
        error!("Telegram bot is not initialized");
        HttpResponse::InternalServerError().json(serde_json::json!({
            "status": "error",
            "message": "Telegram bot is not initialized"
        }))
    }
}

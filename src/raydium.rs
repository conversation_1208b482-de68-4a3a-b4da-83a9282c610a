use anyhow::{anyhow, Result};
use borsh::{BorshDeserialize, BorshSerialize};
use log::{debug, error};
use solana_client::{
    rpc_client::RpcClient,
    rpc_config::{RpcAccountInfoConfig, RpcProgramAccountsConfig},
    rpc_filter::RpcFilterType,
};
use solana_sdk::{
    compute_budget::ComputeBudgetInstruction,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
    system_instruction,
    transaction::Transaction,
};
use spl_associated_token_account::get_associated_token_address;
use spl_associated_token_account::instruction::create_associated_token_account;
use std::{str::FromStr, thread::sleep, time::Duration};

use crate::{config::TradingPool, retry_with_backoff, PriceProvider};
use crate::{constants::WSOL_MINT, RaydiumCPMMPool, RaydiumPoolType};
use async_trait::async_trait;
use reqwest;
use serde_json;

#[derive(BorshDeserialize, BorshSerialize, Debug)]
#[repr(C)]
pub struct RaydiumOpenBookPoolState {
    pub status: u64,
    pub nonce: u64,
    pub order_num: u64,
    pub depth: u64,
    pub base_decimals: u64,
    pub quote_decimals: u64,
    pub state: u64,
    pub reset_flag: u64,
    pub min_size: u64,
    pub vol_max_cut_ratio: u64,
    pub amount_wave_ratio: u64,
    pub base_lot_size: u64,
    pub quote_lot_size: u64,
    pub min_price_multiplier: u64,
    pub max_price_multiplier: u64,
    pub system_decimal_value: u64,
    pub min_separate_numerator: u64,
    pub min_separate_denominator: u64,
    pub trade_fee_numerator: u64,
    pub trade_fee_denominator: u64,
    pub pnl_numerator: u64,
    pub pnl_denominator: u64,
    pub swap_fee_numerator: u64,
    pub swap_fee_denominator: u64,
    pub need_take_pnl_base: u64,            // This was base_need_take_pnl
    pub need_take_pnl_quote: u64,           // This was quote_need_take_pnl
    pub total_pnl_quote: u64,               // This was quote_total_pnl
    pub total_pnl_base: u64,                // This was base_total_pnl
    pub pool_total_deposit_quote: u128,     // New field
    pub pool_total_deposit_base: u128,      // New field
    pub swap_base_in_amount: u128,          // Changed from u64 to u128
    pub swap_quote_out_amount: u128,        // Changed from u64 to u128
    pub swap_base2quote_fee: u64,           // This was swap_base2_quote_fee
    pub swap_quote_in_amount: u128,         // Changed from u64 to u128
    pub swap_base_out_amount: u128,         // Changed from u64 to u128
    pub swap_quote2base_fee: u64,           // This was swap_quote2_base_fee
    pub pool_base_token_account: Pubkey,    // This was base_vault
    pub pool_quote_token_account: Pubkey,   // This was quote_vault
    pub base_mint: Pubkey,                  // This was base_mint
    pub quote_mint: Pubkey,                 // This was quote_mint
    pub lp_mint_address: Pubkey,            // This was lp_mint
    pub amm_open_orders: Pubkey,            // This was open_orders
    pub serum_market: Pubkey,               // This was market_id
    pub serum_program_id: Pubkey,           // This was market_program_id
    pub amm_target_orders: Pubkey,          // This was target_orders
    pub pool_withdraw_queue: Pubkey,        // This was withdraw_queue
    pub pool_temp_lp_token_account: Pubkey, // This was lp_vault
    pub amm_owner: Pubkey,                  // This was owner
    pub pnl_owner: Pubkey,                  // New field
}

impl RaydiumOpenBookPoolState {
    pub fn from_bytes(data: &[u8]) -> Result<Self> {
        Self::try_from_slice(data)
            .map_err(|e| anyhow!("Failed to deserialize RaydiumPoolState: {}", e))
    }
}

pub struct RaydiumOpenBookPool {
    pub state: RaydiumOpenBookPoolState,
}

#[async_trait]
impl PriceProvider for RaydiumOpenBookPool {
    async fn get_price(&self, client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64> {
        get_raydium_price(client, pool_pubkey).await
    }
}

#[async_trait]
impl TradingPool for RaydiumOpenBookPool {
    async fn build_swap_transaction(
        &self,
        client: &RpcClient,
        wallet: &Keypair,
        pool_pubkey: &Pubkey,
        input_amount: u64,
        output_amount: u64,
        token_mint: &Pubkey,
        is_buying: bool,
        jito_tip_account: &Pubkey,
        jito_tip_amount: u64,
        priority_fee_amount: u64,
        self_send_amount: u64,
    ) -> Result<Transaction> {
        build_swap_transaction(
            client,
            wallet,
            pool_pubkey,
            input_amount,
            output_amount,
            token_mint,
            &self.state.pool_base_token_account,
            &self.state.pool_quote_token_account,
            is_buying,
            jito_tip_account,
            jito_tip_amount,
            priority_fee_amount,
            self_send_amount,
        )
        .await
    }
}

/// Calculates the price of a token in SOL units from a Raydium liquidity pool
///
/// # Arguments
///
/// * `rpc_client` - A reference to the Solana RPC client
/// * `pool_pubkey` - The public key of the Raydium liquidity pool
///
/// # Returns
///
/// * `Result<f64>` - The price of the token in SOL units, or an error if the calculation fails
pub async fn get_raydium_price(rpc_client: &RpcClient, pool_pubkey: &Pubkey) -> Result<f64> {
    let mut attempts = 0;
    let account = loop {
        match rpc_client.get_account(&pool_pubkey) {
            Ok(account) => break account,
            Err(e) if attempts < 5 => {
                attempts += 1;
                error!(
                    "Failed to get account {}, retrying ({}/5): {:?}",
                    pool_pubkey.to_string(),
                    attempts,
                    e
                );
                sleep(Duration::from_millis(500));
            }
            Err(e) => return Err(e.into()),
        }
    };

    let data = account.data;

    // Parse pool state
    let pool_state = RaydiumOpenBookPoolState::from_bytes(&data)?;

    // Get token account balances
    let base_balance = rpc_client
        .get_token_account_balance(&pool_state.pool_base_token_account)?
        .amount
        .parse::<f64>()
        .map(|v| v as u64)
        .map_err(|e| anyhow!("Failed to parse base balance: {}", e))?;

    let quote_balance = rpc_client
        .get_token_account_balance(&pool_state.pool_quote_token_account)?
        .amount
        .parse::<f64>()
        .map(|v| v as u64)
        .map_err(|e| anyhow!("Failed to parse quote balance: {}", e))?;

    if base_balance == 0 || quote_balance == 0 {
        return Err(anyhow!(
            "No liquidity in pool, base: {}, quote: {}",
            base_balance,
            quote_balance
        ));
    }

    debug!(
        "Base token {}, balance: {}",
        pool_state.base_mint, base_balance
    );
    debug!(
        "Quote token {}, balance: {}",
        pool_state.quote_mint, quote_balance
    );

    // Calculate price with decimal adjustment
    // price = (base_balance / base_decimals) / (quote_balance / quote_decimals)
    let base_factor = 10u64.pow(pool_state.base_decimals as u32);
    let quote_factor = 10u64.pow(pool_state.quote_decimals as u32);
    let price =
        (base_balance as f64 / base_factor as f64) / (quote_balance as f64 / quote_factor as f64);

    // Apply swap fee
    let fee_numerator = pool_state.swap_fee_numerator as f64;
    let fee_denominator = pool_state.swap_fee_denominator as f64;
    let fee_multiplier = 1.0 - (fee_numerator / fee_denominator);
    debug!("Fee multiplier: {}", fee_multiplier);

    // Calculate final price
    if pool_state.quote_mint == Pubkey::from_str(WSOL_MINT)? {
        Ok((1.0 / price) * fee_multiplier)
    } else {
        Ok(price * fee_multiplier)
    }
}

/// Finds the address of a Raydium liquidity pool given the mint addresses of the tokens in the pool
///
/// # Arguments
///
/// * `rpc_client` - A reference to the Solana RPC client
/// * `token_mint` - The mint address of the token in the pool
/// * `wsol_mint` - The mint address of the wrapped SOL token in the pool
///
/// # Returns
///
/// * `Result<Pubkey>` - The address of the Raydium liquidity pool, or an error if the pool is not found
pub async fn find_pool_address(
    rpc_client: &RpcClient,
    token_mint: &Pubkey,
    wsol_mint: &Pubkey,
) -> Result<Pubkey> {
    // Raydium AMM program ID
    let amm_program_id = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";
    let program_id = Pubkey::from_str(amm_program_id)?;

    // Get all program accounts
    let accounts = rpc_client.get_program_accounts_with_config(
        &program_id,
        RpcProgramAccountsConfig {
            filters: Some(vec![
                RpcFilterType::DataSize(1328), // Size of Raydium pool state
            ]),
            account_config: RpcAccountInfoConfig {
                encoding: None,
                data_slice: None,
                commitment: None,
                min_context_slot: None,
            },
            with_context: None,
            sort_results: None,
        },
    )?;

    // Iterate through accounts to find matching pool
    for (pubkey, account) in accounts {
        if let Ok(pool_state) = RaydiumOpenBookPoolState::from_bytes(&account.data) {
            let pool_token_mint = Pubkey::new_from_array(pool_state.base_mint.to_bytes());
            let pool_wsol_mint = Pubkey::new_from_array(pool_state.quote_mint.to_bytes());

            if (pool_token_mint == *token_mint && pool_wsol_mint == *wsol_mint)
                || (pool_token_mint == *wsol_mint && pool_wsol_mint == *token_mint)
            {
                return Ok(pubkey);
            }
        }
    }

    Err(anyhow!(
        "No matching Raydium pool found for the given token pair"
    ))
}

/// Fetches the address of a Raydium liquidity pool from the API
///
/// # Arguments
///
/// * `token_mint` - The mint address of the token in the pool
///
/// # Returns
///
/// * `Result<Pubkey>` - The address of the Raydium liquidity pool, or an error if the pool is not found
pub async fn get_raydium_pool_address_from_api(token_mint: &Pubkey) -> Result<Pubkey> {
    if token_mint.to_string() == "7S7Njo68uY7wVhPz8dWHLDvf3KxdmUN5Ta4BRkHapump" {
        return Ok(Pubkey::from_str_const(
            "FCNg5HKEkuYrxf2FuWNPmepDfTobgcsAaHEiZ5RvFfv7",
        ));
    }

    let url = format!(
        "https://api-v3.raydium.io/pools/info/mint?mint1={}&mint2=So11111111111111111111111111111111111111112&poolType=standard&poolSortField=default&sortType=desc&pageSize=10&page=1",
        token_mint.to_string()
    );

    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .header("accept", "application/json")
        .send()
        .await?;

    let json: serde_json::Value = response.json().await?;

    if let Some(pool_id) = json["data"]["data"][0]["id"].as_str() {
        Ok(Pubkey::from_str(pool_id)?)
    } else {
        Err(anyhow!("Pool not found for token mint {}", token_mint))
    }
}

/// Builds a transaction to swap tokens in a Raydium liquidity pool
///
/// # Arguments
///
/// * `rpc_client` - A reference to the Solana RPC client
/// * `wallet` - The wallet to use for the transaction
/// * `pool_pubkey` - The public key of the Raydium liquidity pool
/// * `input_amount` - The amount of input token to swap
/// * `output_amount` - The minimum amount of output token to receive
/// * `is_buying` - Whether the swap is a buying or selling operation
///
/// # Returns
///
/// * `Result<Transaction>` - The transaction to swap tokens, or an error if the transaction cannot be built
pub async fn build_swap_transaction(
    rpc_client: &RpcClient,
    wallet: &Keypair,
    pool_pubkey: &Pubkey,
    input_amount: u64,
    output_amount: u64,
    token_mint: &Pubkey,
    pool_base_token_account: &Pubkey,
    pool_quote_token_account: &Pubkey,
    is_buying: bool,
    jito_tip_account: &Pubkey,
    jito_tip_amount: u64,
    priority_fee_amount: u64,
    self_send_amount: u64,
) -> Result<Transaction> {
    let amm_authority = Pubkey::from_str("5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1").unwrap(); // Raydium Authority V4

    // Create a temporary WSOL account
    let temp_wsol_account = Keypair::new();
    let mut lamports_to_create = retry_with_backoff(5, Duration::from_millis(500), || {
        rpc_client
            .get_minimum_balance_for_rent_exemption(165)
            .map_err(anyhow::Error::new)
    })?;
    debug!("lamports_to_create init value: {}", lamports_to_create);
    if is_buying {
        lamports_to_create += input_amount;
    }
    let create_wsol_account_ix = system_instruction::create_account(
        &wallet.pubkey(),
        &temp_wsol_account.pubkey(),
        lamports_to_create,
        165,
        &spl_token::id(),
    );

    // Initialize WSOL account
    let init_wsol_account_ix = spl_token::instruction::initialize_account(
        &spl_token::id(),
        &temp_wsol_account.pubkey(),
        &Pubkey::from_str(WSOL_MINT)?,
        &wallet.pubkey(),
    )?;

    // Get or create associated token account for destination token
    let token_account = get_associated_token_address(&wallet.pubkey(), token_mint);
    let create_token_account_ix = create_associated_token_account(
        &wallet.pubkey(),
        &wallet.pubkey(),
        token_mint,
        &spl_token::id(),
    );

    // Build swap instruction
    let mut swap_data = vec![9u8]; // Instruction discriminator for swap
    swap_data.extend_from_slice(&input_amount.to_le_bytes()); // Amount in
    swap_data.extend_from_slice(&output_amount.to_le_bytes()); // Minimum amount out
    let (user_source_token_account, user_dest_token_account) = if is_buying {
        (temp_wsol_account.pubkey(), token_account)
    } else {
        (token_account, temp_wsol_account.pubkey())
    };

    let swap_ix = Instruction {
        program_id: Pubkey::from_str("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8")?,
        accounts: vec![
            AccountMeta::new_readonly(spl_token::id(), false),
            AccountMeta::new(*pool_pubkey, false),
            AccountMeta::new_readonly(amm_authority, false),
            AccountMeta::new(*pool_pubkey, false), // doesn't matter?
            AccountMeta::new(*pool_pubkey, false), // doesn't matter?
            AccountMeta::new(*pool_base_token_account, false),
            AccountMeta::new(*pool_quote_token_account, false),
            AccountMeta::new_readonly(*pool_pubkey, false), // doesn't matter?
            AccountMeta::new(*pool_pubkey, false),          // doesn't matter?
            AccountMeta::new(*pool_pubkey, false),          // doesn't matter?
            AccountMeta::new(*pool_pubkey, false),          // doesn't matter?
            AccountMeta::new(*pool_pubkey, false),          // doesn't matter?
            AccountMeta::new(*pool_pubkey, false),          // doesn't matter?
            AccountMeta::new(*pool_pubkey, false),          // doesn't matter?
            AccountMeta::new(*pool_pubkey, false),          // doesn't matter?
            AccountMeta::new(user_source_token_account, false),
            AccountMeta::new(user_dest_token_account, false),
            AccountMeta::new(wallet.pubkey(), true), // This account needs to be a signer
        ],
        data: swap_data,
    };

    // Close temporary WSOL account
    let close_wsol_account_ix = spl_token::instruction::close_account(
        &spl_token::id(),
        &temp_wsol_account.pubkey(),
        &wallet.pubkey(),
        &wallet.pubkey(),
        &[],
    )?;

    // Set compute unit limit and price
    let set_compute_unit_limit_ix = ComputeBudgetInstruction::set_compute_unit_limit(300_000);
    let set_compute_unit_price_ix =
        ComputeBudgetInstruction::set_compute_unit_price(priority_fee_amount);

    // Build transaction
    let mut instructions = vec![
        set_compute_unit_limit_ix,
        set_compute_unit_price_ix,
        create_wsol_account_ix,
        init_wsol_account_ix,
    ];

    // Only add create token account instruction if it doesn't exist
    if is_buying && rpc_client.get_account(&token_account).is_err() {
        instructions.push(create_token_account_ix);
    }

    instructions.extend_from_slice(&[swap_ix, close_wsol_account_ix]);

    if self_send_amount > 0 {
        if is_buying {
            let self_transfer_ix =
                system_instruction::transfer(&wallet.pubkey(), &wallet.pubkey(), self_send_amount);
            instructions.push(self_transfer_ix);
        } else {
            let self_transfer_ix = spl_token::instruction::transfer(
                &spl_token::id(),
                &token_account,
                &token_account,
                &wallet.pubkey(),
                &[&wallet.pubkey()],
                self_send_amount,
            )?;
            instructions.push(self_transfer_ix);
        }
    }

    // add jito tip
    let jito_tip_ix =
        system_instruction::transfer(&wallet.pubkey(), &jito_tip_account, jito_tip_amount);
    instructions.push(jito_tip_ix);

    let recent_blockhash = retry_with_backoff(5, Duration::from_millis(500), || {
        rpc_client
            .get_latest_blockhash()
            .map_err(anyhow::Error::new)
    })?;
    let transaction = Transaction::new_signed_with_payer(
        &instructions,
        Some(&wallet.pubkey()),
        &[wallet, &temp_wsol_account],
        recent_blockhash,
    );

    Ok(transaction)
}

pub fn get_raydium_pool_implementation(
    client: &RpcClient,
    pool_pubkey: &Pubkey,
    pool_type: &RaydiumPoolType,
) -> Result<Box<dyn TradingPool>> {
    match pool_type {
        RaydiumPoolType::CPMM => Ok(Box::new(RaydiumCPMMPool)),
        RaydiumPoolType::OpenBook => {
            let pool_account = retry_with_backoff(5, Duration::from_millis(500), || {
                client.get_account(pool_pubkey).map_err(anyhow::Error::new)
            })?;
            let pool_state = RaydiumOpenBookPoolState::from_bytes(&pool_account.data)?;
            Ok(Box::new(RaydiumOpenBookPool { state: pool_state }))
        }
    }
}

pub fn get_raydium_price_provider(
    client: &RpcClient,
    pool_pubkey: &Pubkey,
    pool_type: &RaydiumPoolType,
) -> Result<Box<dyn PriceProvider>> {
    match pool_type {
        RaydiumPoolType::CPMM => Ok(Box::new(RaydiumCPMMPool)),
        RaydiumPoolType::OpenBook => {
            let pool_account = retry_with_backoff(5, Duration::from_millis(500), || {
                client.get_account(pool_pubkey).map_err(anyhow::Error::new)
            })?;
            let pool_state = RaydiumOpenBookPoolState::from_bytes(&pool_account.data)?;
            Ok(Box::new(RaydiumOpenBookPool { state: pool_state }))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;

    #[tokio::test]
    async fn test_get_raydium_pool_address_from_api() {
        let token_mint = Pubkey::from_str("6kLdw2Vx3T4Cps1ZLPC32294eZkKozaf4YHsM7cApump").unwrap();

        let result = get_raydium_pool_address_from_api(&token_mint).await;
        assert!(
            result.is_ok(),
            "Failed to get pool address: {:?}",
            result.err()
        );
        assert_eq!(
            result.unwrap(),
            Pubkey::from_str("7APeHhRUxJAjHkdJFS57pqTRJU3GhMNLSEiTYCMfuw2R").unwrap()
        )
    }
}

use std::collections::{HashMap, HashSet, VecDeque};
use std::time::{Duration, SystemTime};
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};

// Constants for strategy configuration
pub const DEFAULT_MIN_WALLETS: usize = 3;
pub const DEFAULT_TIME_WINDOW_MINUTES: u64 = 15;
pub const DEFAULT_BASE_POSITION_SIZE: f64 = 0.1; // 10% of capital
pub const DEFAULT_MAX_POSITION_SIZE: f64 = 0.3;  // Max 30% of capital
pub const DEFAULT_RISK_FACTOR: f64 = 0.5;        // Conservative risk factor
pub const DEFAULT_BALANCE_THRESHOLD: f64 = 0.9;  // 90% balance threshold for qualified wallets

/// Struct to store wallet buy information with timestamp
#[derive(Debug, Clone)]
pub struct WalletBuyInfo {
    pub wallet: String,
    pub timestamp: SystemTime,
    pub token_amount: u64,
    pub current_balance: u64,
    pub initial_balance: u64,
}

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct WalletStats {
    pub total_trades: u32,
    pub winning_trades: u32,
    pub avg_hold_time: Duration,
    pub avg_profit_percentage: f64,
    pub weight: f64, // Bayesian weight based on performance
}

#[derive(Clone, Debug)]
pub struct TokenPosition {
    pub token: String,
    pub entry_price: f64,
    pub current_price: f64,
    pub amount: u64,
    pub entry_time: SystemTime,
    pub following_wallets: Vec<String>,
    pub bought_sol: f64,
}

#[derive(Debug, Clone)]
pub enum SignalType {
    Buy,
    Sell(f64), // Sell percentage (0.0-1.0)
}

#[derive(Debug, Clone)]
pub struct TradeSignal {
    pub token: String,
    pub signal_type: SignalType,
    pub position_size: f64, // As a percentage of total capital
    pub following_wallets: Vec<String>,
    pub price: f64,
}

#[derive(Debug, Clone)]
pub struct SellDecision {
    pub token: String,
    pub sell_amount: u64,
    pub current_balance: u64,
    pub target_hold_amount: u64,
    pub hold_ratio: f64,
    pub tracked_ratio: f64,
    pub wallet_ratios: Vec<f64>,
}

/// Core strategy class that manages smart money following logic
pub struct SmartMoneyStrategy {
    // Configuration
    pub min_wallets_threshold: usize,
    pub time_window: Duration,
    pub total_capital: u64,
    pub base_position_size: f64,
    pub max_position_size: f64,
    pub risk_factor: f64,
    pub balance_threshold: f64,
    
    // State
    pub wallet_stats: HashMap<String, WalletStats>,
    pub recent_buys: HashMap<String, VecDeque<WalletBuyInfo>>,
    pub current_positions: HashMap<String, TokenPosition>,
    pub historical_trades: Vec<(String, f64)>, // (token, profit_percentage)
    pub active_trades: HashSet<String>,
}

impl Default for SmartMoneyStrategy {
    fn default() -> Self {
        Self::new(
            DEFAULT_MIN_WALLETS,
            DEFAULT_TIME_WINDOW_MINUTES,
            10_000_000_000, // 10 SOL in lamports as default
            DEFAULT_BASE_POSITION_SIZE,
            DEFAULT_MAX_POSITION_SIZE,
            DEFAULT_RISK_FACTOR,
            DEFAULT_BALANCE_THRESHOLD,
        )
    }
}

impl SmartMoneyStrategy {
    pub fn new(
        min_wallets: usize,
        time_window_minutes: u64,
        total_capital: u64,
        base_position_size: f64,
        max_position_size: f64,
        risk_factor: f64,
        balance_threshold: f64,
    ) -> Self {
        SmartMoneyStrategy {
            min_wallets_threshold: min_wallets,
            time_window: Duration::from_secs(time_window_minutes * 60),
            total_capital,
            base_position_size,
            max_position_size,
            risk_factor,
            balance_threshold,
            wallet_stats: HashMap::new(),
            recent_buys: HashMap::new(),
            current_positions: HashMap::new(),
            historical_trades: Vec::new(),
            active_trades: HashSet::new(),
        }
    }

    /// Initialize wallet stats with default values
    pub fn initialize_wallets(&mut self, wallets: Vec<String>) {
        for wallet in wallets {
            if self.wallet_stats.contains_key(&wallet) {
                continue;
            }
            self.wallet_stats.insert(
                wallet,
                WalletStats {
                    total_trades: 0,
                    winning_trades: 0,
                    avg_hold_time: Duration::from_secs(0),
                    avg_profit_percentage: 0.0,
                    weight: 1.0, // Start with equal weights
                }
            );
        }
    }

    /// Set total capital (e.g., when wallet balance changes)
    pub fn set_total_capital(&mut self, total_capital: u64) {
        self.total_capital = total_capital;
    }

    /// Check if a token is already being traded
    pub fn is_token_trading(&self, token: &str) -> bool {
        self.active_trades.contains(token) || self.current_positions.contains_key(token)
    }

    /// Record a new buy transaction from a tracked wallet
    pub fn record_buy(&mut self, token: &str, wallet: String, token_amount: u64) -> Result<()> {
        let recent_buys = self.recent_buys.entry(token.to_string()).or_default();
        
        // Check if we've already recorded this wallet for this token
        if !recent_buys.iter().any(|buy| buy.wallet == wallet) {
            recent_buys.push_back(WalletBuyInfo {
                wallet,
                timestamp: SystemTime::now(),
                token_amount,
                current_balance: token_amount,
                initial_balance: token_amount,
            });
        }
        
        Ok(())
    }

    /// Update a wallet's current token balance
    pub fn update_wallet_balance(&mut self, token: &str, wallet: &str, current_balance: u64) -> Result<()> {
        if let Some(recent_buys) = self.recent_buys.get_mut(token) {
            for buy_info in recent_buys.iter_mut() {
                if buy_info.wallet == *wallet {
                    buy_info.current_balance = current_balance;
                    return Ok(());
                }
            }
        }
        
        Err(anyhow!("Wallet not found in recent buys for this token"))
    }

    /// Clean up old transactions outside our time window
    fn clean_old_transactions(&mut self, token: &str) {
        let time_window_ago = SystemTime::now()
            .checked_sub(self.time_window)
            .unwrap_or_else(SystemTime::now);
        
        if let Some(recent_buys) = self.recent_buys.get_mut(token) {
            // Remove buys older than our time window
            while !recent_buys.is_empty() {
                if let Some(front) = recent_buys.front() {
                    if front.timestamp < time_window_ago {
                        recent_buys.pop_front();
                        continue;
                    }
                }
                break;
            }
        }
    }

    /// Get recent buys for a token, filtering out wallets that sold significant portions
    pub fn get_recent_buys(&mut self, token: &str) -> Vec<WalletBuyInfo> {
        // First clean up old transactions
        self.clean_old_transactions(token);
        
        if let Some(recent_buys) = self.recent_buys.get(token) {
            // Return a copy of the buys, filtering out wallets that sold too much
            return recent_buys
                .iter()
                .filter(|buy| {
                    let balance_ratio = buy.current_balance as f64 / buy.initial_balance as f64;
                    balance_ratio >= self.balance_threshold
                })
                .cloned()
                .collect();
        }
        
        Vec::new()
    }

    /// Get unique wallets that bought a token
    pub fn get_unique_wallets_for_token(&mut self, token: &str) -> Vec<String> {
        let recent_buys = self.get_recent_buys(token);
        let mut unique_wallets = HashSet::new();
        
        for buy in recent_buys {
            unique_wallets.insert(buy.wallet);
        }
        
        unique_wallets.into_iter().collect()
    }

    /// Check if we should buy a token based on smart money activity
    pub fn should_buy_token(&mut self, token: &str) -> Option<TradeSignal> {
        // Get qualified buys for this token
        let recent_buys = self.get_recent_buys(token);
        
        // Check if we have enough wallets buying this token within our time window
        if recent_buys.len() < self.min_wallets_threshold {
            return None;
        }
        
        // Get unique wallets that bought this token
        let wallets: Vec<String> = recent_buys.iter()
            .map(|buy| buy.wallet.clone())
            .collect();
        
        // Calculate position size
        let position_size = self.calculate_position_size(token, &wallets);
        
        // Generate buy signal
        Some(TradeSignal {
            token: token.to_string(),
            signal_type: SignalType::Buy,
            position_size,
            following_wallets: wallets,
            price: 0.0, // Price will be determined at execution
        })
    }
    
    /// Calculate optimal position size based on wallet weights and risk
    pub fn calculate_position_size(&self, _token: &str, wallets: &[String]) -> f64 {
        let mut total_weight = 0.0;
        let mut weighted_win_rate = 0.0;
        
        // Calculate weighted win rate based on wallet performance
        for wallet in wallets {
            if let Some(stats) = self.wallet_stats.get(wallet) {
                total_weight += stats.weight;
                let win_rate = if stats.total_trades > 0 {
                    stats.winning_trades as f64 / stats.total_trades as f64
                } else {
                    0.5 // Default to 50% if no history
                };
                weighted_win_rate += win_rate * stats.weight;
            }
        }
        
        if total_weight > 0.0 {
            weighted_win_rate /= total_weight;
        } else {
            weighted_win_rate = 0.5; // Default
        }
        
        // Calculate position size
        // For new tokens or tokens with low data, be more conservative
        let position_multiplier = if self.historical_trades.len() < 5 {
            0.8 // More conservative for early trades
        } else {
            1.0
        };
        
        // Use Kelly Criterion with adjustments
        let kelly_fraction = (2.0 * weighted_win_rate - 1.0) * self.risk_factor * position_multiplier;
        
        // Calculate final position size
        let position_size = self.base_position_size * (1.0 + kelly_fraction);
        
        // Cap at max position size and ensure it's positive
        position_size.max(0.0).min(self.max_position_size)
    }
    
    /// Register a new position after buying
    pub fn register_position(
        &mut self,
        token: String,
        entry_price: f64,
        amount: u64,
        bought_sol: f64,
        following_wallets: Vec<String>,
    ) {
        let position = TokenPosition {
            token: token.clone(),
            entry_price,
            current_price: entry_price,
            amount,
            entry_time: SystemTime::now(),
            following_wallets,
            bought_sol,
        };
        
        self.current_positions.insert(token.clone(), position);
        self.active_trades.insert(token);
    }
    
    /// Determine optimal sell amount based on wallet balance ratios
    pub fn calculate_sell_decision(
        &self,
        token: &str,
        current_balance: u64,
        wallet_balance_ratios: Vec<f64>,
        bought_amount: u64,
    ) -> SellDecision {
        // Calculate the tracked ratio (average of lowest two ratios)
        let tracked_ratio = calculate_tracked_ratio(wallet_balance_ratios.clone());
        
        // Calculate how much token we should hold now
        // Round down to nearest 0.1 (10%) increment
        let hold_ratio = (tracked_ratio / 0.1).floor() * 0.1;
        let target_hold_amount = (bought_amount as f64 * hold_ratio) as u64;
        
        // Calculate sell amount if needed
        let sell_amount = if current_balance > target_hold_amount {
            current_balance - target_hold_amount
        } else {
            0
        };
        
        SellDecision {
            token: token.to_string(),
            sell_amount,
            current_balance,
            target_hold_amount,
            hold_ratio,
            tracked_ratio,
            wallet_ratios: wallet_balance_ratios,
        }
    }
    
    /// Update the strategy with trade results
    pub fn update_trade_results(&mut self, token: &str, profit_percentage: f64) -> Result<()> {
        if let Some(position) = self.current_positions.remove(token) {
            // Store the trade result
            self.historical_trades.push((token.to_string(), profit_percentage));
            
            let win = profit_percentage > 0.0;
            let hold_time = position.entry_time
                .elapsed()
                .unwrap_or(Duration::from_secs(0));
            
            // Update stats for each wallet
            for wallet in position.following_wallets {
                if let Some(stats) = self.wallet_stats.get_mut(&wallet) {
                    // Update trade statistics
                    stats.total_trades += 1;
                    if win {
                        stats.winning_trades += 1;
                    }
                    
                    // Update average hold time
                    let old_weight = (stats.total_trades - 1) as f64 / stats.total_trades as f64;
                    let new_weight = 1.0 / stats.total_trades as f64;
                    
                    let old_seconds = stats.avg_hold_time.as_secs_f64();
                    let new_seconds = hold_time.as_secs_f64();
                    let updated_seconds = old_seconds * old_weight + new_seconds * new_weight;
                    stats.avg_hold_time = Duration::from_secs_f64(updated_seconds);
                    
                    // Update average profit
                    stats.avg_profit_percentage = 
                        stats.avg_profit_percentage * old_weight + profit_percentage * new_weight;
                    
                    // Update wallet weight using Bayesian inference
                    self.update_wallet_weight(&wallet);
                }
            }
            
            // Remove from active trades
            self.active_trades.remove(token);
            
            // Clean up recent buys for this token
            self.recent_buys.remove(token);
            
            // Adjust strategy based on performance
            self.adjust_strategy();
            
            Ok(())
        } else {
            Err(anyhow!("No position found for token {}", token))
        }
    }
    
    /// Update wallet weight using Bayesian inference
    fn update_wallet_weight(&mut self, wallet: &str) {
        if let Some(stats) = self.wallet_stats.get_mut(wallet) {
            if stats.total_trades == 0 {
                return;
            }
            
            let win_rate = stats.winning_trades as f64 / stats.total_trades as f64;
            let avg_profit = stats.avg_profit_percentage;
            
            // Use a combination of win rate and average profit for weighting
            let performance_score = win_rate * (1.0 + avg_profit / 100.0);
            
            // Sigmoid function to keep weights in a reasonable range
            stats.weight = 2.0 / (1.0 + (-performance_score * 2.0).exp());
        }
    }
    
    /// Dynamically adjust strategy parameters based on performance
    fn adjust_strategy(&mut self) {
        // Need enough data to make adjustments
        if self.historical_trades.len() < 10 {
            return;
        }
        
        // Calculate win rate
        let wins = self.historical_trades.iter()
            .filter(|(_, profit)| *profit > 0.0)
            .count();
        let win_rate = wins as f64 / self.historical_trades.len() as f64;
        
        // Adjust min wallets threshold based on performance
        if win_rate < 0.4 && self.min_wallets_threshold < 5 {
            // If win rate is poor, increase threshold to be more selective
            self.min_wallets_threshold += 1;
        } else if win_rate > 0.7 && self.min_wallets_threshold > 2 {
            // If win rate is excellent, we can be less selective
            self.min_wallets_threshold -= 1;
        }
        
        // Adjust risk factor based on performance
        let avg_profit = self.historical_trades.iter()
            .map(|(_, profit)| *profit)
            .sum::<f64>() / self.historical_trades.len() as f64;
            
        if avg_profit > 10.0 {
            // If performing well, can take more risk
            self.risk_factor = (self.risk_factor * 1.1).min(1.0);
        } else if avg_profit < 0.0 {
            // If losing money, reduce risk
            self.risk_factor *= 0.9;
        }
    }
    
    /// Get the recommended SOL amount to spend based on token and available capital
    pub fn calculate_buy_amount(&self, _token: &str, total_sol_balance: u64, pool_sol_amount: u64) -> f64 {
        // Check if we have enough SOL
        if total_sol_balance < 1_000_000_000 { // 1 SOL
            return 0.0;
        }
        
        // Calculate based on wallet balance (10% of balance)
        let balance_amount = total_sol_balance as f64 / 1_000_000_000.0 / 10.0;
        
        // Calculate based on pool size (1.5% of pool)
        let pool_amount = pool_sol_amount as f64 / 1_000_000_000.0 * 0.015;
        
        // Take the smaller amount and round to nearest 0.1 SOL
        (balance_amount.min(pool_amount) / 0.1).floor() * 0.1
    }
}

/// Calculate the tracked ratio (average of two lowest balance ratios)
pub fn calculate_tracked_ratio(mut numbers: Vec<f64>) -> f64 {
    if numbers.is_empty() {
        return 0.0;
    }
    if numbers.len() == 1 {
        return numbers[0];
    }
    numbers.sort_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal));
    (numbers[0] + numbers[1]) / 2.0
}

/// Calculate wallet balance ratios from current and highest balances
pub fn calculate_wallet_ratios(
    current_balances: &HashMap<String, u64>,
    highest_balances: &HashMap<String, u64>
) -> Vec<f64> {
    let mut ratios = Vec::new();
    
    for (wallet, &highest) in highest_balances {
        if highest > 0 {
            let current = *current_balances.get(wallet).unwrap_or(&0);
            let ratio = current as f64 / highest as f64;
            ratios.push(ratio);
        }
    }
    
    ratios
}

/// Build exit strategy factors based on wallet balances and time
pub fn calculate_exit_factors(
    balance_ratio: f64,
    wallets_selling: usize,
    min_wallets_threshold: usize,
    elapsed_minutes: u64
) -> (f64, f64, f64) {
    // Balance factor: the lower the balance ratio, the more we sell
    let balance_factor = 1.0 - balance_ratio.powf(2.0);
    
    // Wallet factor: more wallets selling = more we sell
    let wallet_factor = (wallets_selling as f64) / (min_wallets_threshold as f64);
    
    // Time factor: exponential decay function
    let time_factor = if elapsed_minutes < 30 {
        0.0
    } else {
        1.0 - (-0.01 * (elapsed_minutes as f64 - 30.0)).exp()
    };
    
    (balance_factor, wallet_factor, time_factor)
}
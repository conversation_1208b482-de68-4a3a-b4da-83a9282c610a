use anyhow::Result;
use jito_sdk_rust::JitoJsonRpcSDK;
use log::{debug, error, warn};
use serde_json::json;
use solana_client::{rpc_client::RpcClient, rpc_config::RpcSimulateTransactionConfig};
use solana_sdk::{commitment_config::CommitmentConfig, transaction::Transaction};

pub async fn send_tx_via_jito(
    tx: &Transaction,
    jito_sdk: &JitoJsonRpcSDK,
) -> Result<Option<String>> {
    let serialized_tx = bs58::encode(bincode::serialize(&tx)?).into_string();
    let bundle = json!([serialized_tx]);
    match jito_sdk.send_bundle(Some(bundle), None).await {
        Ok(result) => {
            if result["result"].as_str().is_none() {
                if format!("{:?}", result).contains("Rate limit exceeded") {
                    return Ok(None);
                }
                warn!("Sending bundle no result: {:?}", result);
                return Ok(None);
            }
            Ok(result["result"].as_str().map(|s| s.to_string()))
        }
        Err(e) => {
            error!("Error sending bundle: {:?}", e);
            Err(anyhow::anyhow!("Error sending bundle: {:?}", e))
        }
    }
}

pub fn simulate_transaction(client: &RpcClient, transaction: &Transaction) -> Result<()> {
    debug!("Simulating transaction...");
    let sim_config = RpcSimulateTransactionConfig {
        sig_verify: false,
        replace_recent_blockhash: true,
        commitment: Some(CommitmentConfig::confirmed()),
        accounts: None,
        encoding: None,
        min_context_slot: None,
        inner_instructions: false,
    };

    let simulation = client.simulate_transaction_with_config(transaction, sim_config)?;
    debug!("Simulation result: {:?}", simulation.value);

    if let Some(err) = simulation.value.err {
        error!("Transaction simulation failed: {:?}", err);
        if let Some(logs) = simulation.value.logs {
            error!("Error logs:");
            for log in logs {
                error!("{}", log);
            }
        }
        return Err(anyhow::anyhow!("Transaction simulation failed"));
    }

    Ok(())
}

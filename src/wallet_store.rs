use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::{self, File};
use std::path::PathBuf;

#[derive(Debug, Serialize, Deserialize)]
pub struct WalletStore {
    wallets: Vec<String>,
    #[serde(default)]
    chain_wallets: HashMap<String, Vec<String>>,
    #[serde(default)]
    wallet_profits: HashMap<String, f64>,
    #[serde(default)]
    wallet_win_rates: HashMap<String, f64>,
    #[serde(default)]
    removed_wallets: Vec<String>,
    #[serde(default)]
    blacklist: Vec<String>,
    #[serde(default)]
    filename: String,
}

impl WalletStore {
    pub fn new(filename: &str) -> Self {
        Self {
            wallets: vec![],
            chain_wallets: HashMap::new(),
            wallet_profits: HashMap::new(),
            wallet_win_rates: HashMap::new(),
            removed_wallets: vec![],
            blacklist: vec![],
            filename: filename.to_string(),
        }
    }

    pub fn load(filename: &str) -> Result<Self> {
        let mut store = Self::new(filename);
        let path = store.get_store_path()?;
        Self::ensure_store_dir()?;

        if !path.exists() {
            File::create(&path)?;
            return Ok(store);
        }

        let content = fs::read_to_string(path)?;
        if !content.trim().is_empty() {
            store = serde_json::from_str(&content)?;
            store.filename = filename.to_string();
        }
        Ok(store)
    }

    pub fn save(&self) -> Result<()> {
        let path = self.get_store_path()?;
        Self::ensure_store_dir()?;

        let content = serde_json::to_string_pretty(self)?;
        fs::write(path, content)?;
        Ok(())
    }

    fn ensure_store_dir() -> Result<()> {
        let home = dirs::home_dir().ok_or_else(|| anyhow!("Could not find home directory"))?;
        let store_dir = home.join(".solana-bot");
        if !store_dir.exists() {
            fs::create_dir_all(&store_dir)?;
        }
        Ok(())
    }

    pub fn get_wallets(&self) -> &[String] {
        &self.wallets
    }
    pub fn get_chain_wallets(&self, chain: &str) -> &[String] {
        self.chain_wallets
            .get(chain)
            .map(|v| v.as_slice())
            .unwrap_or(&[])
    }

    pub fn add_wallet(&mut self, wallet: String) -> Result<bool> {
        if self.wallets.contains(&wallet) {
            return Ok(false);
        }
        self.wallets.push(wallet.clone());
        if !self.wallet_profits.contains_key(&wallet) {
            self.wallet_profits.insert(wallet, 0.0);
        }
        self.save()?;
        Ok(true)
    }

    pub fn remove_wallet(&mut self, wallet: &str) -> Result<bool> {
        if let Some(pos) = self.wallets.iter().position(|x| x == wallet) {
            self.wallets.remove(pos);
            self.wallet_profits.remove(wallet);
            self.removed_wallets.push(wallet.to_string());
            self.blacklist.push(wallet.to_string());
            self.save()?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    pub fn get_removed_wallets(&self) -> &[String] {
        &self.removed_wallets
    }

    pub fn record_profit(&mut self, wallet: &str, profit: f64) -> Result<()> {
        let current_profit = self.wallet_profits.entry(wallet.to_string()).or_insert(0.0);
        *current_profit += profit;
        self.save()?;
        Ok(())
    }

    pub fn get_wallet_profit(&self, wallet: &str) -> f64 {
        self.wallet_profits.get(wallet).copied().unwrap_or(0.0)
    }

    pub fn get_all_profits(&self) -> &HashMap<String, f64> {
        &self.wallet_profits
    }

    pub fn record_win_rate(&mut self, wallet: &str, win_rate: f64) -> Result<()> {
        self.wallet_win_rates.insert(wallet.to_string(), win_rate);
        self.save()?;
        Ok(())
    }

    pub fn get_wallet_win_rate(&self, wallet: &str) -> f64 {
        self.wallet_win_rates.get(wallet).copied().unwrap_or(0.0)
    }

    pub fn get_average_win_rate(&self, wallets: &[String]) -> f64 {
        if wallets.is_empty() {
            return 0.5;
        }
        let mut sum = 0.0;
        let mut count = 0;
        for wallet in wallets {
            let win_rate = self.get_wallet_win_rate(wallet);
            if win_rate > 0.0 {
                sum += win_rate;
                count += 1;
            }
        }
        if count == 0 {
            return 0.5;
        }
        sum / count as f64
    }

    fn get_store_path(&self) -> Result<PathBuf> {
        let home = dirs::home_dir().ok_or_else(|| anyhow!("Could not find home directory"))?;
        Ok(home.join(".solana-bot").join(&self.filename))
    }
}

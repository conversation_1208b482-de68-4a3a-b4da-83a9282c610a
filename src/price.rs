use anyhow::{anyhow, Result};
use log::debug;
use reqwest;
use serde_json::Value;

async fn fetch_price(crypto_id: &str) -> Result<f64> {
    let url = format!(
        "https://api.coingecko.com/api/v3/simple/price?ids={}&vs_currencies=usd",
        crypto_id
    );
    debug!("Fetching {} price from {}", crypto_id, url);

    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko)")
        .build()?;

    let response = client.get(&url).send().await?;
    let status = response.status();
    debug!("Response status: {}", status);

    if !status.is_success() {
        let error_text = response.text().await?;
        return Err(anyhow!(
            "Failed to get {} price. Status: {}, Error: {}",
            crypto_id,
            status,
            error_text
        ));
    }

    let text = response.text().await?;
    let json: Value = serde_json::from_str(&text)?;
    let price = json[crypto_id]["usd"]
        .as_f64()
        .ok_or_else(|| anyhow!("Failed to parse {} price from JSON: {}", crypto_id, json))?;

    debug!("Parsed {} price: ${}", crypto_id, price);
    Ok(price)
}

pub async fn get_sol_price() -> Result<f64> {
    fetch_price("solana").await
}

pub async fn get_bnb_price() -> Result<f64> {
    fetch_price("binancecoin").await
}

pub fn format_number(num: f64) -> String {
    let abs_num = num.abs();
    let (value, suffix) = if abs_num >= 1_000_000_000.0 {
        (abs_num / 1_000_000_000.0, "B")
    } else if abs_num >= 1_000_000.0 {
        (abs_num / 1_000_000.0, "M")
    } else if abs_num >= 1_000.0 {
        (abs_num / 1_000.0, "K")
    } else {
        (abs_num, "")
    };

    let formatted = if abs_num == 0.0 {
        "0".to_string()
    } else if abs_num >= 1.0 {
        format!("{:.2}{}", value, suffix)
    } else if abs_num >= 0.001 {
        format!("{:.6}", value)
    } else {
        format!("{:.8}", value)
    };

    if num.is_sign_negative() {
        format!("-{}", formatted)
    } else {
        formatted
    }
}

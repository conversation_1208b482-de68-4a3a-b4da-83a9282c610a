[workspace]
members = [
    "event-proto",
    "kryptogo-vixen-pumpfun-parser",
    "common-program-parsers",
    "stream-processor-bin",
    "block-processor",
    "event-handlers"
]
resolver = "2"

[workspace.dependencies]
clap = { version = "4.5.39", features = ["derive"] }
tokio = { version = "1.45.1", features = ["full"] }
toml = "0.8.22"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "fmt", "json", "time"] }
tracing-stackdriver = "0.10.0"
yellowstone-vixen = { git = "https://github.com/kryptogo/yellowstone-vixen", features = ["prometheus", "stream"] }
yellowstone-vixen-core = { git = "https://github.com/kryptogo/yellowstone-vixen" }
yellowstone-vixen-mock = { git = "https://github.com/kryptogo/yellowstone-vixen" }
yellowstone-vixen-parser = { git = "https://github.com/kryptogo/yellowstone-vixen", features = ["proto"] }
yellowstone-grpc-proto = "6.1.0"
yellowstone-vixen-proto = { git = "https://github.com/kryptogo/yellowstone-vixen" }
axum = "0.8.4"
solana-program = "^2.1.6"
prost = "0.13.5"
solana-sdk = "2.2.0"
solana-client = "2.2.0"
solana-transaction-status = "2.2.7"
solana-rpc-client-api = "2.2.7"
solana-commitment-config = "2.2.1"
env_logger = "0.11.8"
google-cloud-pubsub = "0.30.0"
google-cloud-googleapis = "0.16.0"
google-cloud-gax = "0.19.2"
serde = "1.0.219"
serde_json = "1.0.140"
thiserror = "2.0.12"
futures = "0.3.31"
common-program-parsers = { path = "common-program-parsers" }
event-proto = { path = "event-proto" }

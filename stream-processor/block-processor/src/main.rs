use std::sync::Arc;
use std::{
    env,
    time::{Duration, Instant},
};

use anyhow::Result;
use clap::Parser;
use common_program_parsers::{
    system_program_parser::SystemProgramParser,
    token_extension_program::InstructionParser as TokenExtensionIxParser,
    token_program::InstructionParser as TokenIxParser,
};
use event_handlers::{PubSubClient, TransferEventHandler};
use rayon::prelude::*;
use solana_client::{rpc_client::RpcClient, rpc_config::RpcBlockConfig};
use solana_commitment_config::CommitmentConfig;
use solana_transaction_status::{TransactionDetails, UiTransactionEncoding};
use tokio::sync::Semaphore;
use yellowstone_vixen::{
    handler::DynPipeline, instruction::InstructionPipeline, metrics::NullMetrics, Pipeline,
};
use yellowstone_vixen_core::instruction::InstructionUpdate;

mod transaction_converter;
use transaction_converter::TransactionConverter;

type BoxPipeline<'h, T> = Box<dyn DynPipeline<T> + Send + Sync + 'h>;

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// Starting block number (inclusive)
    #[arg(long)]
    from_block: u64,

    /// Ending block number (inclusive)
    #[arg(long)]
    to_block: u64,

    /// Number of parallel threads
    #[arg(long, default_value = "10")]
    threads: usize,
}

struct BlockProcessor {
    rpc_client: Arc<RpcClient>,
    pipeline: InstructionPipeline<NullMetrics>,
}

impl BlockProcessor {
    async fn new(
        rpc_url: &str,
        pipelines: Vec<BoxPipeline<'static, InstructionUpdate>>,
    ) -> Result<Self> {
        let rpc_client = Arc::new(RpcClient::new(rpc_url.to_string()));
        let pipeline = InstructionPipeline::new(pipelines, &NullMetrics);
        if pipeline.is_none() {
            return Err(anyhow::anyhow!("Failed to create pipeline"));
        }

        Ok(Self {
            rpc_client,
            pipeline: pipeline.unwrap(),
        })
    }

    async fn process_block(&self, slot: u64) -> Result<()> {
        let start = Instant::now();
        tracing::info!("Processing block {}", slot);

        // Get block from RPC
        let rpc_start = Instant::now();
        let block = self.rpc_client.get_block_with_config(
            slot,
            RpcBlockConfig {
                encoding: Some(UiTransactionEncoding::Json),
                transaction_details: Some(TransactionDetails::Full),
                rewards: Some(false),
                commitment: Some(CommitmentConfig::confirmed()),
                max_supported_transaction_version: Some(0),
            },
        )?;
        let rpc_duration = rpc_start.elapsed();
        tracing::info!("RPC fetch for block {} took {:?}", slot, rpc_duration);

        let txs = match block.transactions {
            Some(txs) => txs,
            None => return Err(anyhow::anyhow!("No transactions found")),
        };

        // Convert transactions to TransactionUpdate format in parallel using rayon
        let convert_start = Instant::now();
        let tx_updates: Vec<_> = txs
            .par_iter()
            .enumerate()
            .filter_map(|(index, tx)| {
                match TransactionConverter::convert_to_transaction_update(tx, slot, index as u64) {
                    Ok(tx_update) => Some(tx_update),
                    Err(e) => {
                        tracing::error!("Failed to convert to TransactionUpdate: {:?}", e);
                        None
                    }
                }
            })
            .collect();
        let convert_duration = convert_start.elapsed();
        tracing::info!(
            "Converted {} transactions for block {} in {:?}",
            tx_updates.len(),
            slot,
            convert_duration
        );

        // Process transactions in batches
        let pipeline_start = Instant::now();
        for chunk in tx_updates.chunks(10) {
            let futures: Vec<_> = chunk
                .iter()
                .map(|tx_update| self.pipeline.handle(tx_update))
                .collect();

            let results = futures::future::join_all(futures).await;
            for result in results {
                if let Err(e) = result {
                    return Err(anyhow::anyhow!("Pipeline error: {:?}", e));
                }
            }
        }
        let pipeline_duration = pipeline_start.elapsed();
        tracing::info!(
            "Pipeline processing for block {} took {:?}",
            slot,
            pipeline_duration
        );

        let total_duration = start.elapsed();
        tracing::info!(
            "Total processing time for block {}: {:?} (RPC: {:?}, Convert: {:?}, Pipeline: {:?})",
            slot,
            total_duration,
            rpc_duration,
            convert_duration,
            pipeline_duration
        );

        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();

    let args = Args::parse();
    let rpc_url =
        env::var("RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());

    // Create the same pipelines as in stream-processor-bin
    let event_publisher = Arc::new(PubSubClient::new().await?);
    let transfer_handler =
        TransferEventHandler::new(event_publisher.clone(), 1000, Duration::from_millis(3000));

    let ix_pipelines: Vec<BoxPipeline<'static, InstructionUpdate>> = vec![
        Box::new(Pipeline::new(
            SystemProgramParser,
            [transfer_handler.clone()],
        )),
        Box::new(Pipeline::new(TokenIxParser, [transfer_handler.clone()])),
        Box::new(Pipeline::new(
            TokenExtensionIxParser,
            [transfer_handler.clone()],
        )),
    ];

    let processor = Arc::new(BlockProcessor::new(&rpc_url, ix_pipelines).await?);
    let semaphore = Arc::new(Semaphore::new(args.threads));

    // Create a stream of block numbers
    let blocks: Vec<u64> = (args.from_block..=args.to_block).collect();

    let total_start = Instant::now();

    // Process blocks in parallel using tokio
    let mut handles = Vec::new();
    for chunk in blocks.chunks(args.threads) {
        let mut chunk_handles = Vec::new();
        for &block in chunk {
            let processor = processor.clone();
            let semaphore = semaphore.clone();
            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                match processor.process_block(block).await {
                    Ok(_) => tracing::info!("Successfully processed block {}", block),
                    Err(e) => tracing::error!("Error processing block {}: {:?}", block, e),
                }
            });
            chunk_handles.push(handle);
        }
        handles.extend(chunk_handles);
    }

    // Wait for all blocks to be processed
    for handle in handles {
        handle.await?;
    }

    let total_duration = total_start.elapsed();
    tracing::info!("Total execution time: {:?}", total_duration);

    if let Err(e) = transfer_handler.shutdown().await {
        tracing::error!("Error during shutdown: {:?}", e);
    }

    Ok(())
}

use anyhow::Result;
use base64::{engine::general_purpose::STANDARD, Engine as _};
use solana_transaction_status::{
    option_serializer::OptionSerializer, EncodedTransaction, EncodedTransactionWithStatusMeta,
    UiAddressTableLookup, UiInnerInstructions, UiInstruction, UiMessage, UiRawMessage, UiTransaction, UiTransactionStatusMeta,
    UiTransactionTokenBalance,
};
use yellowstone_grpc_proto::prelude::{
    CompiledInstruction, InnerInstruction, InnerInstructions, Message, MessageAddressTableLookup,
    ReturnData, SubscribeUpdateTransactionInfo, TokenBalance, Transaction, TransactionError,
    TransactionStatusMeta, UiTokenAmount,
};
use yellowstone_vixen::bs58;

pub struct TransactionConverter;

impl TransactionConverter {
    pub fn convert_to_transaction_update(
        tx: &EncodedTransactionWithStatusMeta,
        slot: u64,
        index: u64,
    ) -> Result<yellowstone_vixen_core::TransactionUpdate> {
        let mut tx_info = SubscribeUpdateTransactionInfo::default();
        tx_info.index = index;

        let json_tx = Self::get_json_transaction(tx)?;
        Self::set_signature(&mut tx_info, json_tx);
        let raw_msg = Self::get_message(json_tx)?;

        let mut solana_tx = Transaction::default();
        let message = Self::convert_message(raw_msg)?;
        Self::set_signatures(&mut solana_tx, json_tx);
        solana_tx.message = Some(message);
        tx_info.transaction = Some(solana_tx);

        if let Some(meta) = &tx.meta {
            let solana_meta = Self::convert_metadata(meta)?;
            tx_info.meta = Some(solana_meta);
        }

        let mut tx_update = yellowstone_vixen_core::TransactionUpdate::default();
        tx_update.slot = slot;
        tx_update.transaction = Some(tx_info);
        Ok(tx_update)
    }

    fn get_json_transaction(tx: &EncodedTransactionWithStatusMeta) -> Result<&UiTransaction> {
        match &tx.transaction {
            EncodedTransaction::Json(json_tx) => Ok(json_tx),
            _ => Err(anyhow::anyhow!("Invalid transaction encoding")),
        }
    }

    fn get_message(json_tx: &UiTransaction) -> Result<&UiRawMessage> {
        match &json_tx.message {
            UiMessage::Raw(raw_msg) => Ok(raw_msg),
            _ => Err(anyhow::anyhow!("Invalid transaction message")),
        }
    }

    fn set_signature(tx_info: &mut SubscribeUpdateTransactionInfo, json_tx: &UiTransaction) {
        if let Some(signature) = json_tx.signatures.first() {
            tx_info.signature = bs58::decode(signature).into_vec().unwrap_or_default();
        }
    }

    fn set_signatures(solana_tx: &mut Transaction, json_tx: &UiTransaction) {
        solana_tx.signatures = json_tx
            .signatures
            .iter()
            .map(|sig| bs58::decode(sig).into_vec().unwrap_or_default())
            .collect();
    }

    fn convert_message(raw_msg: &UiRawMessage) -> Result<Message> {
        let mut message = Message::default();

        // Set account keys
        message.account_keys = raw_msg
            .account_keys
            .iter()
            .map(|acc| bs58::decode(acc).into_vec().unwrap_or_default())
            .collect();

        // Set recent blockhash
        message.recent_blockhash = bs58::decode(&raw_msg.recent_blockhash)
            .into_vec()
            .unwrap_or_default();

        // Set instructions
        message.instructions = raw_msg
            .instructions
            .iter()
            .map(|ix| {
                let mut compiled_ix = CompiledInstruction::default();
                compiled_ix.program_id_index = ix.program_id_index as u32;
                compiled_ix.accounts = ix.accounts.clone();
                compiled_ix.data = bs58::decode(&ix.data).into_vec().unwrap_or_default();
                compiled_ix
            })
            .collect();

        // Set address table lookups if present
        if let Some(lookups) = &raw_msg.address_table_lookups {
            message.address_table_lookups = Self::convert_address_table_lookups(lookups);
        }

        Ok(message)
    }

    fn convert_address_table_lookups(
        lookups: &[UiAddressTableLookup],
    ) -> Vec<MessageAddressTableLookup> {
        lookups
            .iter()
            .map(|lookup| {
                let mut table_lookup = MessageAddressTableLookup::default();
                table_lookup.account_key = bs58::decode(&lookup.account_key)
                    .into_vec()
                    .unwrap_or_default();
                table_lookup.writable_indexes = lookup.writable_indexes.clone();
                table_lookup.readonly_indexes = lookup.readonly_indexes.clone();
                table_lookup
            })
            .collect()
    }

    fn convert_metadata(meta: &UiTransactionStatusMeta) -> Result<TransactionStatusMeta> {
        let mut solana_meta = TransactionStatusMeta::default();

        // Convert error if present
        if let Some(err) = &meta.err {
            solana_meta.err = Some(TransactionError {
                err: err.to_string().into_bytes(),
            });
        }

        // Set basic metadata fields
        solana_meta.fee = meta.fee;
        solana_meta.pre_balances = meta.pre_balances.clone();
        solana_meta.post_balances = meta.post_balances.clone();

        // Convert inner instructions
        if let OptionSerializer::Some(inner_ixs) = &meta.inner_instructions {
            solana_meta.inner_instructions = Self::convert_inner_instructions(inner_ixs);
        }

        // Convert log messages
        if let OptionSerializer::Some(logs) = &meta.log_messages {
            solana_meta.log_messages = logs.clone();
        }

        // Convert token balances
        if let OptionSerializer::Some(pre_balances) = &meta.pre_token_balances {
            solana_meta.pre_token_balances = Self::convert_token_balances(pre_balances);
        }

        if let OptionSerializer::Some(post_balances) = &meta.post_token_balances {
            solana_meta.post_token_balances = Self::convert_token_balances(post_balances);
        }

        // Set compute units consumed if present
        if let OptionSerializer::Some(units) = meta.compute_units_consumed {
            solana_meta.compute_units_consumed = Some(units);
        }

        if let OptionSerializer::Some(loaded_addresses) = &meta.loaded_addresses {
            solana_meta.loaded_readonly_addresses = loaded_addresses
                .readonly
                .iter()
                .map(|addr| bs58::decode(addr).into_vec().unwrap_or_default())
                .collect();
            solana_meta.loaded_writable_addresses = loaded_addresses
                .writable
                .iter()
                .map(|addr| bs58::decode(addr).into_vec().unwrap_or_default())
                .collect();
        }

        if let OptionSerializer::Some(return_data) = &meta.return_data {
            solana_meta.return_data = Some(ReturnData {
                program_id: bs58::decode(&return_data.program_id)
                    .into_vec()
                    .unwrap_or_default(),
                data: STANDARD.decode(&return_data.data.0).unwrap_or_default(),
            });
        }
        Ok(solana_meta)
    }

    fn convert_inner_instructions(inner_ixs: &[UiInnerInstructions]) -> Vec<InnerInstructions> {
        inner_ixs
            .iter()
            .map(|ixs| {
                let mut inner = InnerInstructions::default();
                inner.index = ixs.index as u32;
                inner.instructions = ixs
                    .instructions
                    .iter()
                    .map(|ix| {
                        let mut inner_ix = InnerInstruction::default();
                        if let UiInstruction::Compiled(compiled) = ix {
                            inner_ix.program_id_index = compiled.program_id_index as u32;
                            inner_ix.accounts = compiled.accounts.clone();
                            inner_ix.data =
                                bs58::decode(&compiled.data).into_vec().unwrap_or_default();
                            inner_ix.stack_height = compiled.stack_height;
                        }
                        inner_ix
                    })
                    .collect();
                inner
            })
            .collect()
    }

    fn convert_token_balances(balances: &[UiTransactionTokenBalance]) -> Vec<TokenBalance> {
        balances
            .iter()
            .map(|balance| {
                let mut token_balance = TokenBalance::default();
                token_balance.account_index = balance.account_index as u32;
                token_balance.mint = balance.mint.clone();
                token_balance.owner = match &balance.owner {
                    OptionSerializer::Some(owner) => owner.clone(),
                    _ => "".to_string(),
                };
                token_balance.program_id = match &balance.program_id {
                    OptionSerializer::Some(program_id) => program_id.clone(),
                    _ => "".to_string(),
                };
                token_balance.ui_token_amount = Some(UiTokenAmount {
                    ui_amount: balance.ui_token_amount.ui_amount.unwrap_or_default(),
                    decimals: balance.ui_token_amount.decimals as u32,
                    amount: balance.ui_token_amount.amount.clone(),
                    ui_amount_string: balance.ui_token_amount.ui_amount_string.clone(),
                });
                token_balance
            })
            .collect()
    }
}

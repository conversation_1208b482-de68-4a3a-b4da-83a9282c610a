[package]
name = "block-processor"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
clap = { workspace = true }
tokio = { workspace = true }
solana-client = { workspace = true }
solana-transaction-status = { workspace = true }
solana-commitment-config = { workspace = true }
common-program-parsers = { workspace = true }
event-handlers = { path = "../event-handlers" }
event-proto = { workspace = true }
yellowstone-vixen = { workspace = true }
yellowstone-vixen-core = { workspace = true }
yellowstone-grpc-proto = { workspace = true }
env_logger = { workspace = true }
futures = { workspace = true }
tracing.workspace = true
base64 = "0.22.1"
rayon = "1.8"

[dev-dependencies]
yellowstone-vixen-mock.workspace = true

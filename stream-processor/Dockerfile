# Build stage
FROM rust:1.87.0-slim-bullseye AS builder

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    make \
    build-essential \
    curl \
    unzip \
    && PB_REL="https://github.com/protocolbuffers/protobuf/releases" \
    && curl -LO $PB_REL/download/v30.2/protoc-30.2-linux-x86_64.zip \
    && unzip protoc-30.2-linux-x86_64.zip -d /usr/local \
    && rm protoc-30.2-linux-x86_64.zip \
    && rm -rf /var/lib/apt/lists/*

# Copy source code
COPY . .

# Build the application with mounted cache
# RUN --mount=type=bind,source=/tmp/target-cache,target=/app/target \
#     cargo build --release
RUN cargo build --release

# Runtime stage
FROM debian:bullseye-slim

WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl1.1 \
    && rm -rf /var/lib/apt/lists/*

# Copy the binary from builder
COPY --from=builder /app/target/release/stream-processor /app/stream-processor
COPY --from=builder /app/target/release/block-processor /app/block-processor

# Run as non-root user
RUN useradd -m -u 1000 appuser
USER appuser

# Expose health check port
EXPOSE 8080

# Run the application
CMD ["/app/stream-processor"]

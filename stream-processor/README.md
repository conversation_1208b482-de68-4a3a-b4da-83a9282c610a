# Stream Processor

## Development and Testing

1. Run local test with mock Solana node

    ```bash
    cargo test --package stream-processor --bin stream-processor -- tests --show-output 
    ```

2. Run local yellowstone-parser with live Solana node

    ```bash
    export SDKROOT=$(xcrun --sdk macosx --show-sdk-path)
    RUST_LOG=info cargo clean
    RUST_LOG=info cargo run -- --config "./Vixen.toml"
    ```

## Development

### proto

```bash
cd event-proto
cargo build # build proto files
```

### Style & Lint

```bash
cargo machete # check unused imported dependencies
cargo clippy --fix
cargo fmt --check
# or use following commands to fix it in place: 
# cargo machete --fix
# cargo clippy --fix --allow-dirty
```

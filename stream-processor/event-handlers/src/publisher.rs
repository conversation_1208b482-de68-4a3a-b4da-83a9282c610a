use google_cloud_googleapis::pubsub::v1::PubsubMessage;
use google_cloud_pubsub::client::{Client, ClientConfig};
use google_cloud_pubsub::publisher::Publisher;
use prost::Message;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;

#[derive(Debug, thiserror::Error)]
pub enum MessagingError {
    #[error("Pub/Sub client initialization failed: {0}")]
    Initialization(String),
    #[error("Failed to publish message: {0}")]
    PublishError(String),
}

#[derive(Clone, Debug)]
pub struct PubSubClient {
    client: Arc<Client>,
    publishers: Arc<Mutex<HashMap<String, Publisher>>>,
}

impl PubSubClient {
    /// Creates a new client.
    pub async fn new() -> Result<Self, MessagingError> {
        let config = ClientConfig::default()
            .with_auth()
            .await
            .map_err(|e| MessagingError::Initialization(e.to_string()))?;
        let client = Client::new(config)
            .await
            .map_err(|e| MessagingError::Initialization(e.to_string()))?;

        Ok(Self {
            client: Arc::new(client),
            publishers: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    /// Publishes a protobuf message to a topic.
    pub async fn publish_proto<T: Message + Send + Sync>(
        &self,
        topic_id: &str,
        message_data: &T,
    ) -> Result<(), MessagingError> {
        let bytes = message_data.encode_to_vec();
        let message = PubsubMessage {
            data: bytes,
            ..Default::default()
        };

        let publisher = {
            let mut publishers = self.publishers.lock().await;
            if !publishers.contains_key(topic_id) {
                let topic = self.client.topic(topic_id);
                publishers.insert(topic_id.to_string(), topic.new_publisher(None));
            }
            publishers.get(topic_id).unwrap().clone()
        };

        publisher
            .publish(message)
            .await
            .get()
            .await
            .map_err(|e| MessagingError::PublishError(e.to_string()))?;

        Ok(())
    }

    /// Shuts down the client gracefully.
    pub async fn shutdown(&self) -> Result<(), MessagingError> {
        let mut publishers = self.publishers.lock().await;
        for (_, publisher) in publishers.iter_mut() {
            publisher.shutdown().await;
        }
        Ok(())
    }
}

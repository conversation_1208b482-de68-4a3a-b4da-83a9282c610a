use common_program_parsers::{
    token_extension_program::{
        TokenExtensionProgramIx, TokenExtensionProgramIxInfo, TransferFeeIx,
    },
    token_program::{TokenProgramIx, TokenProgramIxInfo},
};
use std::collections::VecDeque;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;
use yellowstone_vixen::{<PERSON><PERSON>, HandlerResult};

use crate::publisher::PubSubClient;
use common_program_parsers::system_program_parser::SolTransfer;
use event_proto::{
    BatchedSolanaEvent, EventMetadata, SolTransferEvent, SolanaEvent, TokenTransferEvent,
};

const MAX_BATCH_SIZE: usize = 3000;

#[derive(Clone, Debug)]
pub struct TransferEventHandler {
    publisher: Arc<PubSubClient>,
    batch: Arc<Mutex<VecDeque<SolanaEvent>>>,
    min_batch_size: usize,
}

impl TransferEventHandler {
    pub fn new(publisher: Arc<PubSubClient>, min_batch_size: usize, flush_interval: Duration) -> Self {
        let handler = Self {
            publisher,
            batch: Arc::new(Mutex::new(VecDeque::new())),
            min_batch_size,
        };

        // Spawn background task to periodically flush batches
        let handler_clone = handler.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(flush_interval);
            loop {
                interval.tick().await;
                if let Err(e) = handler_clone.flush_batch(false).await {
                    tracing::error!("Failed to flush batch: {:?}", e);
                }
            }
        });

        handler
    }

    pub async fn shutdown(&self) -> HandlerResult<()> {
        tracing::info!("Shutting down TransferEventHandler, flushing remaining data...");

        // Keep flushing until batch is empty
        loop {
            let batch_len = {
                let batch = self.batch.lock().await;
                batch.len()
            };

            if batch_len == 0 {
                tracing::info!("All data flushed successfully");
                break;
            }

            tracing::info!("Flushing remaining {} events", batch_len);
            if let Err(e) = self.flush_batch(true).await {
                tracing::error!("Failed to flush batch during shutdown: {:?}", e);
                return Err(e);
            }

            // Small delay to prevent tight loop
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        Ok(())
    }

    async fn add_to_batch(&self, event: SolanaEvent) -> HandlerResult<()> {
        let should_flush = {
            let mut batch = self.batch.lock().await;
            batch.push_back(event);
            batch.len() >= MAX_BATCH_SIZE
        };

        if should_flush {
            self.flush_batch(true).await?;
        }
        Ok(())
    }

    async fn flush_batch(&self, force: bool) -> HandlerResult<()> {
        let mut batch = self.batch.lock().await;
        if batch.len() < self.min_batch_size && !force {
            return Ok(());
        }

        let events: Vec<SolanaEvent> = batch.drain(..).collect();
        let batched_event = BatchedSolanaEvent { events };
        tracing::info!("Flushing batch of {} events", batched_event.events.len());

        if !batched_event.events.is_empty() {
            self.publisher
                .publish_proto("solana-events", &batched_event)
                .await?;
        }
        Ok(())
    }
}

impl Handler<SolTransfer> for TransferEventHandler {
    async fn handle(&self, parsed: &SolTransfer) -> HandlerResult<()> {
        let event = SolTransferEvent {
            metadata: Some(EventMetadata {
                signature: parsed.signature.clone(),
                slot: parsed.slot,
                ix_index: parsed.ix_index,
            }),
            from_wallet: parsed.from.to_string(),
            to_wallet: parsed.to.to_string(),
            amount_lamports: parsed.lamports,
        };

        let solana_event = SolanaEvent {
            event: Some(event_proto::solana_event::Event::SolTransfer(event)),
        };
        self.add_to_batch(solana_event).await
    }
}

impl Handler<TokenProgramIxInfo> for TransferEventHandler {
    async fn handle(&self, parsed: &TokenProgramIxInfo) -> HandlerResult<()> {
        let event = match &parsed.ix {
            TokenProgramIx::TransferChecked(accounts, data) => TokenTransferEvent {
                metadata: Some(EventMetadata {
                    signature: parsed.signature.clone(),
                    slot: parsed.slot,
                    ix_index: parsed.ix_index,
                }),
                token_mint: accounts.mint.to_string(),
                from_wallet: match &accounts.source_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                to_wallet: match &accounts.destination_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                amount: data.amount.to_string(),
            },
            TokenProgramIx::Transfer(accounts, data, token_mint) => TokenTransferEvent {
                metadata: Some(EventMetadata {
                    signature: parsed.signature.clone(),
                    slot: parsed.slot,
                    ix_index: parsed.ix_index,
                }),
                token_mint: match token_mint {
                    Some(mint) => mint.clone(),
                    None => return Ok(()),
                },
                from_wallet: match &accounts.source_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                to_wallet: match &accounts.destination_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                amount: data.amount.to_string(),
            },
            _ => return Ok(()),
        };

        let solana_event = SolanaEvent {
            event: Some(event_proto::solana_event::Event::TokenTransfer(event)),
        };
        self.add_to_batch(solana_event).await
    }
}

impl Handler<TokenExtensionProgramIxInfo> for TransferEventHandler {
    async fn handle(&self, parsed: &TokenExtensionProgramIxInfo) -> HandlerResult<()> {
        let event = match &parsed.ix {
            TokenExtensionProgramIx::TokenProgramIx(TokenProgramIx::TransferChecked(
                accounts,
                data,
            )) => TokenTransferEvent {
                metadata: Some(EventMetadata {
                    signature: parsed.signature.clone(),
                    slot: parsed.slot,
                    ix_index: parsed.ix_index,
                }),
                token_mint: accounts.mint.to_string(),
                from_wallet: match &accounts.source_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                to_wallet: match &accounts.destination_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                amount: data.amount.to_string(),
            },
            TokenExtensionProgramIx::TransferFeeIx(TransferFeeIx::TransferCheckedWithFee(
                accounts,
                data,
            )) => TokenTransferEvent {
                metadata: Some(EventMetadata {
                    signature: parsed.signature.clone(),
                    slot: parsed.slot,
                    ix_index: parsed.ix_index,
                }),
                token_mint: accounts.mint.to_string(),
                from_wallet: match &accounts.source_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                to_wallet: match &accounts.destination_owner {
                    Some(owner) => owner.to_string(),
                    None => return Ok(()),
                },
                amount: data.amount.to_string(),
            },
            _ => return Ok(()),
        };

        let solana_event = SolanaEvent {
            event: Some(event_proto::solana_event::Event::TokenTransfer(event)),
        };
        self.add_to_batch(solana_event).await
    }
}

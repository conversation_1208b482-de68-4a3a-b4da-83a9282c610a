# System Program Parser

## Tests

### Basic Integration Test

```bash
GRPC_URL=34.146.40.18:10001 GRPC_AUTH_TOKEN=your_token GRPC_TIMEOUT=30 cargo test --test live_grpc_test test_system_program_parser_with_live_grpc -- --ignored --nocapture
```

### Memory Profiling Test

Run this test to monitor memory usage over 1 minute:

```bash
GRPC_URL=34.146.40.18:10001 GRPC_AUTH_TOKEN=your_token GRPC_TIMEOUT=30 cargo test --test live_grpc_test test_system_program_parser_memory_profile -- --ignored --nocapture
```

The memory profiling test will:

1. Run for exactly 1 minute
2. Record memory usage every second
3. Record memory usage every 100 transfers
4. Print a detailed report of memory usage over time
5. Show transfer statistics

This helps identify potential memory leaks by showing:

- Memory growth over time
- Memory spikes during high transfer volumes
- Overall memory efficiency

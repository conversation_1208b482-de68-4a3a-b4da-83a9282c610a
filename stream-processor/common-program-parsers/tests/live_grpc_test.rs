use common_program_parsers::{
    system_program_parser::{<PERSON><PERSON><PERSON><PERSON><PERSON>, SystemProgramParser},
    token_program::{In<PERSON><PERSON><PERSON><PERSON>, TokenProgramIx, TokenProgramIxInfo},
};
use parking_lot::Mutex;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::timeout;
use tracing::{error, info, warn};
use yellowstone_vixen::{
    CommitmentLevel, Handler, HandlerResult, Pipeline, bs58,
    config::{BufferConfig, OptConfig, VixenConfig, YellowstoneConfig},
};
use yellowstone_vixen_core::Parser;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
struct TestStats {
    transfer_count: usize,
    total_lamports: u64,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct TestTransferHandler {
    stats: Arc<Mutex<TestStats>>,
}

impl TestTransferHandler {
    fn new() -> Self {
        Self {
            stats: Arc::new(Mutex::new(TestStats::default())),
        }
    }

    fn get_stats(&self) -> TestStats {
        self.stats.lock().clone()
    }
}

impl Handler<SolTransfer> for TestTransferHandler {
    async fn handle(&self, transfer: &SolTransfer) -> HandlerResult<()> {
        let mut stats = self.stats.lock();
        stats.transfer_count += 1;
        stats.total_lamports += transfer.lamports;

        info!(
            "System Program transfer: From: {} To: {} Amount: {} lamports ({} SOL) Total transfers: {}",
            transfer.from,
            transfer.to,
            transfer.lamports,
            transfer.lamports as f64 / 1_000_000_000.0,
            stats.transfer_count
        );

        Ok(())
    }
}

#[derive(Debug, Default, Clone)]
struct TestTokenStats {
    transfer_count: usize,
}

#[derive(Debug, Clone)]
struct TestTokenHandler {
    stats: Arc<Mutex<TestTokenStats>>,
}

impl TestTokenHandler {
    fn new() -> Self {
        Self {
            stats: Arc::new(Mutex::new(TestTokenStats::default())),
        }
    }

    fn get_stats(&self) -> TestTokenStats {
        self.stats.lock().clone()
    }
}

impl Handler<TokenProgramIxInfo> for TestTokenHandler {
    async fn handle(&self, parsed: &TokenProgramIxInfo) -> HandlerResult<()> {
        if let TokenProgramIx::Transfer(accounts, data, token_mint) = &parsed.ix {
            let mut stats = self.stats.lock();
            stats.transfer_count += 1;

            info!(
                "Token Transfer Event: Signature: {} Slot: {}",
                bs58::encode(&parsed.signature).into_string(),
                parsed.slot
            );
            let token_mint_str = token_mint.as_ref().map_or("".to_string(), |m| m.clone());
            if accounts.source_owner.as_ref().is_none() {
                error!(
                    "Missing source {} owner for token {} transfer in {}",
                    accounts.source,
                    token_mint_str,
                    bs58::encode(&parsed.signature).into_string()
                );
            } else if accounts.destination_owner.as_ref().is_none() {
                error!(
                    "Missing destination {} owner for token {} transfer in {}",
                    accounts.destination,
                    token_mint_str,
                    bs58::encode(&parsed.signature).into_string()
                );
            } else {
                info!(
                    "  From: {} To: {} Amount: {} Token: {}",
                    accounts.source_owner.as_ref().unwrap(),
                    accounts.destination_owner.as_ref().unwrap(),
                    data.amount,
                    token_mint_str
                );
            }
        }
        Ok(())
    }
}

/// Integration test: Direct gRPC connection test for SystemProgramParser
///
/// Environment variables:
/// - GRPC_URL: gRPC service address
/// - GRPC_AUTH_TOKEN: authentication token
/// - GRPC_TIMEOUT: timeout in seconds
#[tokio::test]
#[ignore]
async fn test_system_program_parser_with_live_grpc()
-> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();
    let grpc_url = std::env::var("GRPC_URL").ok();
    let grpc_auth_token = std::env::var("GRPC_AUTH_TOKEN").ok();
    let grpc_timeout = std::env::var("GRPC_TIMEOUT")
        .unwrap_or_else(|_| "30".to_string())
        .parse::<u64>()
        .unwrap_or(30);

    info!("Starting SystemProgramParser integration test");
    info!("gRPC URL: {:?}", grpc_url);
    info!(
        "Auth Token: {}",
        if grpc_auth_token.is_some() {
            "Set"
        } else {
            "Not set"
        }
    );
    info!("Timeout: {} seconds", grpc_timeout);

    let grpc_url = match grpc_url {
        Some(url) => {
            if !url.starts_with("http://") && !url.starts_with("https://") {
                format!("http://{}", url)
            } else {
                url
            }
        }
        None => {
            error!("GRPC_URL environment variable not set");
            return Err("GRPC_URL environment variable is required"
                .to_string()
                .into());
        }
    };

    info!("Processed gRPC URL: {}", grpc_url);

    let vixen_config = VixenConfig {
        yellowstone: YellowstoneConfig {
            endpoint: grpc_url,
            x_token: grpc_auth_token,
            timeout: grpc_timeout,
        },
        buffer: BufferConfig { jobs: None },
        metrics: OptConfig::default(),
    };

    let test_handler = TestTransferHandler::new();
    let parser = SystemProgramParser;

    info!("SystemProgramParser ID: {}", parser.id());
    info!("SystemProgramParser prefilter: {:?}", parser.prefilter());

    let vixen_runtime = yellowstone_vixen::Runtime::builder()
        .commitment_level(CommitmentLevel::Confirmed)
        .instruction(Pipeline::new(parser, [test_handler.clone()]))
        .build(vixen_config);

    info!("Starting Yellowstone Vixen runtime, waiting for System Program transfers...");

    let test_duration = Duration::from_secs(1);

    match timeout(test_duration, vixen_runtime.try_run_async()).await {
        Ok(Ok(())) => {
            info!("Vixen runtime stopped normally");
        }
        Ok(Err(e)) => {
            error!("Vixen runtime error: {:?}", e);
            return Err(e.into());
        }
        Err(_) => {
            warn!("Test timeout, but this may be normal since we're listening to live data");
        }
    }

    let stats = test_handler.get_stats();
    info!("Test statistics:");
    info!("  - Transfers received: {}", stats.transfer_count);
    info!("  - Total lamports: {} lamports", stats.total_lamports);
    info!(
        "  - Average transfer amount: {} lamports",
        if stats.transfer_count > 0 {
            stats.total_lamports / stats.transfer_count as u64
        } else {
            0
        }
    );

    if stats.transfer_count > 0 {
        info!(
            "Test successful! SystemProgramParser parsed {} System Program transfers",
            stats.transfer_count
        );
        Ok(())
    } else {
        warn!(
            "No System Program transfers received during test period. This may be normal depending on network activity."
        );
        Ok(())
    }
}

/// Integration test: Direct gRPC connection test for Token Program Parser
///
/// Environment variables:
/// - GRPC_URL: gRPC service address
/// - GRPC_AUTH_TOKEN: authentication token
/// - GRPC_TIMEOUT: timeout in seconds
#[tokio::test]
#[ignore]
async fn test_token_program_parser_with_live_grpc()
-> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();
    let grpc_url = std::env::var("GRPC_URL").ok();
    let grpc_auth_token = std::env::var("GRPC_AUTH_TOKEN").ok();
    let grpc_timeout = std::env::var("GRPC_TIMEOUT")
        .unwrap_or_else(|_| "30".to_string())
        .parse::<u64>()
        .unwrap_or(30);

    info!("Starting Token Program Parser integration test");
    info!("gRPC URL: {:?}", grpc_url);
    info!(
        "Auth Token: {}",
        if grpc_auth_token.is_some() {
            "Set"
        } else {
            "Not set"
        }
    );
    info!("Timeout: {} seconds", grpc_timeout);

    let grpc_url = match grpc_url {
        Some(url) => {
            if !url.starts_with("http://") && !url.starts_with("https://") {
                format!("http://{}", url)
            } else {
                url
            }
        }
        None => {
            error!("GRPC_URL environment variable not set");
            return Err("GRPC_URL environment variable is required"
                .to_string()
                .into());
        }
    };

    info!("Processed gRPC URL: {}", grpc_url);

    let vixen_config = VixenConfig {
        yellowstone: YellowstoneConfig {
            endpoint: grpc_url,
            x_token: grpc_auth_token,
            timeout: grpc_timeout,
        },
        buffer: BufferConfig { jobs: None },
        metrics: OptConfig::default(),
    };

    let test_handler = TestTokenHandler::new();
    let parser = InstructionParser;

    info!("Token Program Parser ID: {}", parser.id());
    info!("Token Program Parser prefilter: {:?}", parser.prefilter());

    let vixen_runtime = yellowstone_vixen::Runtime::builder()
        .commitment_level(CommitmentLevel::Confirmed)
        .instruction(Pipeline::new(parser, [test_handler.clone()]))
        .build(vixen_config);

    info!("Starting Yellowstone Vixen runtime, waiting for Token Program transfers...");

    let test_duration = Duration::from_secs(1);

    match timeout(test_duration, vixen_runtime.try_run_async()).await {
        Ok(Ok(())) => {
            info!("Vixen runtime stopped normally");
        }
        Ok(Err(e)) => {
            error!("Vixen runtime error: {:?}", e);
            return Err(e.into());
        }
        Err(_) => {
            warn!("Test timeout, but this may be normal since we're listening to live data");
        }
    }

    let stats = test_handler.get_stats();
    info!("Test statistics:");
    info!("  - Token transfers received: {}", stats.transfer_count);

    if stats.transfer_count > 0 {
        info!(
            "Test successful! Token Program Parser parsed {} token transfers",
            stats.transfer_count
        );
        Ok(())
    } else {
        warn!(
            "No token transfers received during test period. This may be normal depending on network activity."
        );
        Ok(())
    }
}

use std::borrow::Cow;
use yellowstone_vixen_core::{
    <PERSON><PERSON><PERSON>, Prefi<PERSON>, ProgramParser, Pubkey, instruction::InstructionUpdate,
};

// System program is the zero address - using lazy evaluation since Pubkey::new is not const
fn system_program_id() -> Pubkey {
    Pubkey::new([0; 32])
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct SolTransfer {
    pub slot: u64,
    pub signature: Vec<u8>,
    pub ix_index: u32,
    pub from: Pubkey,
    pub to: Pubkey,
    pub lamports: u64,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>)]
pub struct SystemProgramParser;

impl Parser for SystemProgramParser {
    type Input = InstructionUpdate;
    type Output = SolTransfer;

    fn id(&self) -> Cow<str> {
        "parser::system_program".into()
    }

    fn prefilter(&self) -> Prefilter {
        Prefilter::builder()
            .transaction_accounts([system_program_id()])
            .build()
            .unwrap()
    }

    async fn parse(&self, ix: &Self::Input) -> yellowstone_vixen_core::ParseResult<Self::Output> {
        // filter out failed transactions
        if ix.shared.err.is_some() {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        // System transfer instruction has discriminator [2, 0, 0, 0] followed by u64 lamports
        if ix.data.len() < 12 {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        // Check if this is a transfer instruction (discriminator = 2)
        let instruction_type = u32::from_le_bytes([ix.data[0], ix.data[1], ix.data[2], ix.data[3]]);
        if instruction_type != 2 {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        // Extract lamports (8 bytes after the 4-byte discriminator)
        let lamports = u64::from_le_bytes([
            ix.data[4],
            ix.data[5],
            ix.data[6],
            ix.data[7],
            ix.data[8],
            ix.data[9],
            ix.data[10],
            ix.data[11],
        ]);

        if ix.accounts.len() < 2 {
            return Err(yellowstone_vixen_core::ParseError::from(
                "Invalid transfer instruction: not enough accounts".to_owned(),
            ));
        }

        let from = ix.accounts[0];
        let to = ix.accounts[1];

        Ok(SolTransfer {
            slot: ix.shared.slot,
            signature: ix.shared.signature.clone(),
            ix_index: ix.ix_index as u32,
            from,
            to,
            lamports,
        })
    }
}

impl ProgramParser for SystemProgramParser {
    fn program_id(&self) -> Pubkey {
        system_program_id()
    }
}

use axum::{Router, routing::get};
use clap::Parser;
use common_program_parsers::{
    system_program_parser::SystemProgramParser,
    token_extension_program::InstructionParser as TokenExtensionIxParser,
    token_program::InstructionParser as TokenIxParser,
};
use event_handlers::{PubSubClient, TransferEventHandler};
use std::time::Duration;
use std::{net::SocketAddr, sync::Arc};
use tokio::task::Join<PERSON>andle;
use yellowstone_vixen::{
    Pipeline,
    config::{BufferConfig, OptConfig, VixenConfig, YellowstoneConfig},
};

mod logging;

#[derive(clap::Parser)]
#[command(version, author, about)]
pub struct Opts {
    #[arg(long, env = "GRPC_URL")]
    grpc_url: String,
    #[arg(long, env = "GRPC_AUTH_TOKEN")]
    grpc_auth_token: Option<String>,
    #[arg(long, env = "GRPC_TIMEOUT")]
    grpc_timeout: u64,
    #[arg(long, env = "HEALTH_CHECK_PORT", default_value = "8080")]
    health_check_port: u16,
}

async fn health_check() -> &'static str {
    "OK"
}

async fn start_health_check_server(port: u16) -> JoinHandle<()> {
    let app = Router::new().route("/health", get(health_check));
    let addr = SocketAddr::from(([0, 0, 0, 0], port));

    tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(addr).await.unwrap();
        tracing::info!("Health check server listening on {}", addr);
        axum::serve(listener, app).await.unwrap();
    })
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    logging::init_logging();

    let opts = Opts::parse();
    let vixen_config = VixenConfig {
        yellowstone: YellowstoneConfig {
            endpoint: opts.grpc_url,
            x_token: opts.grpc_auth_token,
            timeout: opts.grpc_timeout,
        },
        buffer: BufferConfig { jobs: None },
        metrics: OptConfig::default(),
    };

    let event_publisher = Arc::new(PubSubClient::new().await?);
    let transfer_handler =
        TransferEventHandler::new(event_publisher.clone(), 100, Duration::from_millis(100));

    let vixen_runtime = yellowstone_vixen::Runtime::builder()
        .commitment_level(yellowstone_vixen::CommitmentLevel::Processed)
        .instruction(Pipeline::new(
            SystemProgramParser,
            [transfer_handler.clone()],
        ))
        .instruction(Pipeline::new(TokenIxParser, [transfer_handler.clone()]))
        .instruction(Pipeline::new(
            TokenExtensionIxParser,
            [transfer_handler.clone()],
        ))
        .build(vixen_config);

    tracing::info!("Starting Yellowstone Vixen runtime...");

    // Start Vixen runtime first
    let runtime_future = vixen_runtime.try_run_async();

    // Wait a bit to ensure Vixen runtime is ready
    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

    // Now start the health check server
    let _health_check_join_handle = start_health_check_server(opts.health_check_port).await;

    // Run both the runtime and timeout concurrently
    tokio::select! {
        _ = runtime_future => {
            tracing::info!("Yellowstone Vixen runtime stopped gracefully.");
            // Flush any remaining data in the transfer handler
            if let Err(e) = transfer_handler.shutdown().await {
                tracing::error!("Failed to flush remaining data during shutdown: {:?}", e);
            }
            event_publisher.shutdown().await?;
            Ok(())
        }
    }
}

use tracing_subscriber::{Registry, filter::LevelFilter, layer::SubscriberExt};

pub fn init_logging() {
    let filter = LevelFilter::INFO;

    let stackdriver = tracing_stackdriver::layer();
    // let fmt_layer = fmt::layer(); // Use a simple formatting layer instead

    let subscriber = Registry::default().with(filter).with(stackdriver);
    // let subscriber = Registry::default().with(filter).with(fmt_layer);

    tracing::subscriber::set_global_default(subscriber)
        .expect("Failed to set global default subscriber");
}

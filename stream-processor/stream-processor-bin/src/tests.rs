use yellowstone_vixen_core::Parser;
use yellowstone_vixen_mock::FixtureData;
use yellowstone_vixen_mock::run_account_parse;
use yellowstone_vixen_mock::{account_fixture, tx_fixture};
use common_program_parsers::{
    token_extension_program::InstructionParser as TokenExtensionProgramIxParser,
    token_program::{AccountParser as TokenProgramAccParser, TokenProgramState},
};

#[tokio::test]
async fn test_account_parsing() {
    let parser = TokenProgramAccParser;
    let account = account_fixture!("3SmPYPvZfEmroktLiJsgaNENuPEud3Z52zSfLQ1zJdkK", &parser);

    let TokenProgramState::Mint(mint) = account else {
        panic!("Unexpected account state");
    };

    assert_eq!(mint.decimals, 10);
    println!("Parsed account: {:?}", mint);
}

#[tokio::test]
async fn test_instruction_parsing() {
    let parser = TokenExtensionProgramIxParser;
    let ixs = tx_fixture!(
        "44gWEyKUkeUabtJr4eT3CQEkFGrD4jMdwUV6Ew5MR5K3RGizs9iwbkb5Q4T3gnAaSgHxn3ERQ8g5YTXuLP1FrWnt",
        &parser
    );

    let Some(first_ix) = ixs.first() else {
        panic!("No instructions found");
    };

    println!("Parsed instruction: {:?}", first_ix);
}

use kryptogo_vixen_pumpfun_parser::instructions_parser::PumpProgramIx;
use yellowstone_vixen::{<PERSON><PERSON>, HandlerResult};

#[derive(Debug)]
pub struct PriceCalculator {}

impl Handler<PumpProgramIx> for PriceCalculator {
    async fn handle(&self, ix: &PumpProgramIx) -> HandlerResult<()> {
        match ix {
            PumpProgramIx::Buy(accs, data, trade_event) => {
                let sol_cost = match trade_event {
                    Some(trade_event) => trade_event.sol_amount,
                    None => data.max_sol_cost,
                };
                let price = sol_cost as f64 / data.amount as f64;
                tracing::info!(
                    "PumpFun Buy: {} tokens for max {} SOL ({} SOL/token) - User: {}",
                    data.amount,
                    data.max_sol_cost as f64 / 1e9,
                    price / 1e9,
                    accs.user
                );
            }
            PumpProgramIx::Sell(accs, data, trade_event) => {
                let sol_output = match trade_event {
                    Some(trade_event) => trade_event.sol_amount,
                    None => data.min_sol_output,
                };
                let price = sol_output as f64 / data.amount as f64;
                tracing::info!(
                    "PumpFun Sell: {} tokens for min {} SOL ({} SOL/token) - User: {}",
                    data.amount,
                    data.min_sol_output as f64 / 1e9,
                    price / 1e9,
                    accs.user
                );
            }
            _ => {}
        };
        Ok(())
    }
}

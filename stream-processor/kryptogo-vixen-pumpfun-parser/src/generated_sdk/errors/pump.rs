//! This code was AUTOGENERATED using the codama library.
//! Please DO NOT EDIT THIS FILE, instead use visitors
//! to add features, then rerun codama to update it.
//!
//! <https://github.com/codama-idl/codama>
//!

use num_derive::FromPrimitive;
use thiserror::Error;

#[derive(<PERSON><PERSON>, Debug, Eq, Error, FromPrimitive, PartialEq)]
pub enum PumpError {
    /// 6000 - The given account is not authorized to execute this instruction.
    #[error("The given account is not authorized to execute this instruction.")]
    NotAuthorized = 0x1770,
    /// 6001 - The program is already initialized.
    #[error("The program is already initialized.")]
    AlreadyInitialized = 0x1771,
    /// 6002 - slippage: Too much SOL required to buy the given amount of tokens.
    #[error("slippage: Too much SOL required to buy the given amount of tokens.")]
    TooMuchSolRequired = 0x1772,
    /// 6003 - slippage: Too little SOL received to sell the given amount of tokens.
    #[error("slippage: Too little SOL received to sell the given amount of tokens.")]
    TooLittleSolReceived = 0x1773,
    /// 6004 - The mint does not match the bonding curve.
    #[error("The mint does not match the bonding curve.")]
    MintDoesNotMatchBondingCurve = 0x1774,
    /// 6005 - The bonding curve has completed and liquidity migrated to raydium.
    #[error("The bonding curve has completed and liquidity migrated to raydium.")]
    BondingCurveComplete = 0x1775,
    /// 6006 - The bonding curve has not completed.
    #[error("The bonding curve has not completed.")]
    BondingCurveNotComplete = 0x1776,
    /// 6007 - The program is not initialized.
    #[error("The program is not initialized.")]
    NotInitialized = 0x1777,
    /// 6008 - Withdraw too frequent
    #[error("Withdraw too frequent")]
    WithdrawTooFrequent = 0x1778,
}

impl solana_program::program_error::PrintProgramError for PumpError {
    fn print<E>(&self) {
        solana_program::msg!(&self.to_string());
    }
}

impl<T> solana_program::decode_error::DecodeError<T> for PumpError {
    fn type_of() -> &'static str {
        "PumpError"
    }
}

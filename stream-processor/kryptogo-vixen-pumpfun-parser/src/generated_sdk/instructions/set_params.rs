//! This code was AUTOGENERATED using the codama library.
//! Please DO NOT EDIT THIS FILE, instead use visitors
//! to add features, then rerun codama to update it.
//!
//! <https://github.com/codama-idl/codama>
//!

use borsh::BorshDeserialize;
use borsh::BorshSerialize;
use solana_program::pubkey::Pubkey;

/// Accounts.
#[derive(Debug)]
pub struct SetParams {
    pub global: solana_program::pubkey::Pubkey,

    pub user: solana_program::pubkey::Pubkey,

    pub system_program: solana_program::pubkey::Pubkey,

    pub event_authority: solana_program::pubkey::Pubkey,

    pub program: solana_program::pubkey::Pubkey,
}

impl SetParams {
    pub fn instruction(
        &self,
        args: SetParamsInstructionArgs,
    ) -> solana_program::instruction::Instruction {
        self.instruction_with_remaining_accounts(args, &[])
    }
    #[allow(clippy::arithmetic_side_effects)]
    #[allow(clippy::vec_init_then_push)]
    pub fn instruction_with_remaining_accounts(
        &self,
        args: SetParamsInstructionArgs,
        remaining_accounts: &[solana_program::instruction::AccountMeta],
    ) -> solana_program::instruction::Instruction {
        let mut accounts = Vec::with_capacity(5 + remaining_accounts.len());
        accounts.push(solana_program::instruction::AccountMeta::new(
            self.global,
            false,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new(
            self.user, true,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new_readonly(
            self.system_program,
            false,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new_readonly(
            self.event_authority,
            false,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new_readonly(
            self.program,
            false,
        ));
        accounts.extend_from_slice(remaining_accounts);
        let mut data = borsh::to_vec(&SetParamsInstructionData::new()).unwrap();
        let mut args = borsh::to_vec(&args).unwrap();
        data.append(&mut args);

        solana_program::instruction::Instruction {
            program_id: crate::PUMP_ID,
            accounts,
            data,
        }
    }
}

#[derive(BorshSerialize, BorshDeserialize, Clone, Debug, Eq, PartialEq)]
#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
pub struct SetParamsInstructionData {
    discriminator: [u8; 8],
}

impl SetParamsInstructionData {
    pub fn new() -> Self {
        Self {
            discriminator: [165, 31, 134, 53, 189, 180, 130, 255],
        }
    }
}

impl Default for SetParamsInstructionData {
    fn default() -> Self {
        Self::new()
    }
}

#[derive(BorshSerialize, BorshDeserialize, Clone, Debug, Eq, PartialEq)]
#[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
pub struct SetParamsInstructionArgs {
    pub fee_recipient: Pubkey,
    pub initial_virtual_token_reserves: u64,
    pub initial_virtual_sol_reserves: u64,
    pub initial_real_token_reserves: u64,
    pub token_total_supply: u64,
    pub fee_basis_points: u64,
}

/// Instruction builder for `SetParams`.
///
/// ### Accounts:
///
///   0. `[writable]` global
///   1. `[writable, signer]` user
///   2. `[optional]` system_program (default to `11111111111111111111111111111111`)
///   3. `[optional]` event_authority (default to `Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1`)
///   4. `[optional]` program (default to `6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P`)
#[derive(Clone, Debug, Default)]
pub struct SetParamsBuilder {
    global: Option<solana_program::pubkey::Pubkey>,
    user: Option<solana_program::pubkey::Pubkey>,
    system_program: Option<solana_program::pubkey::Pubkey>,
    event_authority: Option<solana_program::pubkey::Pubkey>,
    program: Option<solana_program::pubkey::Pubkey>,
    fee_recipient: Option<Pubkey>,
    initial_virtual_token_reserves: Option<u64>,
    initial_virtual_sol_reserves: Option<u64>,
    initial_real_token_reserves: Option<u64>,
    token_total_supply: Option<u64>,
    fee_basis_points: Option<u64>,
    __remaining_accounts: Vec<solana_program::instruction::AccountMeta>,
}

impl SetParamsBuilder {
    pub fn new() -> Self {
        Self::default()
    }
    #[inline(always)]
    pub fn global(&mut self, global: solana_program::pubkey::Pubkey) -> &mut Self {
        self.global = Some(global);
        self
    }
    #[inline(always)]
    pub fn user(&mut self, user: solana_program::pubkey::Pubkey) -> &mut Self {
        self.user = Some(user);
        self
    }
    /// `[optional account, default to '11111111111111111111111111111111']`
    #[inline(always)]
    pub fn system_program(&mut self, system_program: solana_program::pubkey::Pubkey) -> &mut Self {
        self.system_program = Some(system_program);
        self
    }
    /// `[optional account, default to 'Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1']`
    #[inline(always)]
    pub fn event_authority(
        &mut self,
        event_authority: solana_program::pubkey::Pubkey,
    ) -> &mut Self {
        self.event_authority = Some(event_authority);
        self
    }
    /// `[optional account, default to '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P']`
    #[inline(always)]
    pub fn program(&mut self, program: solana_program::pubkey::Pubkey) -> &mut Self {
        self.program = Some(program);
        self
    }
    #[inline(always)]
    pub fn fee_recipient(&mut self, fee_recipient: Pubkey) -> &mut Self {
        self.fee_recipient = Some(fee_recipient);
        self
    }
    #[inline(always)]
    pub fn initial_virtual_token_reserves(
        &mut self,
        initial_virtual_token_reserves: u64,
    ) -> &mut Self {
        self.initial_virtual_token_reserves = Some(initial_virtual_token_reserves);
        self
    }
    #[inline(always)]
    pub fn initial_virtual_sol_reserves(&mut self, initial_virtual_sol_reserves: u64) -> &mut Self {
        self.initial_virtual_sol_reserves = Some(initial_virtual_sol_reserves);
        self
    }
    #[inline(always)]
    pub fn initial_real_token_reserves(&mut self, initial_real_token_reserves: u64) -> &mut Self {
        self.initial_real_token_reserves = Some(initial_real_token_reserves);
        self
    }
    #[inline(always)]
    pub fn token_total_supply(&mut self, token_total_supply: u64) -> &mut Self {
        self.token_total_supply = Some(token_total_supply);
        self
    }
    #[inline(always)]
    pub fn fee_basis_points(&mut self, fee_basis_points: u64) -> &mut Self {
        self.fee_basis_points = Some(fee_basis_points);
        self
    }
    /// Add an additional account to the instruction.
    #[inline(always)]
    pub fn add_remaining_account(
        &mut self,
        account: solana_program::instruction::AccountMeta,
    ) -> &mut Self {
        self.__remaining_accounts.push(account);
        self
    }
    /// Add additional accounts to the instruction.
    #[inline(always)]
    pub fn add_remaining_accounts(
        &mut self,
        accounts: &[solana_program::instruction::AccountMeta],
    ) -> &mut Self {
        self.__remaining_accounts.extend_from_slice(accounts);
        self
    }
    #[allow(clippy::clone_on_copy)]
    pub fn instruction(&self) -> solana_program::instruction::Instruction {
        let accounts = SetParams {
            global: self.global.expect("global is not set"),
            user: self.user.expect("user is not set"),
            system_program: self
                .system_program
                .unwrap_or(solana_program::pubkey!("11111111111111111111111111111111")),
            event_authority: self.event_authority.unwrap_or(solana_program::pubkey!(
                "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1"
            )),
            program: self.program.unwrap_or(solana_program::pubkey!(
                "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
            )),
        };
        let args = SetParamsInstructionArgs {
            fee_recipient: self
                .fee_recipient
                .clone()
                .expect("fee_recipient is not set"),
            initial_virtual_token_reserves: self
                .initial_virtual_token_reserves
                .clone()
                .expect("initial_virtual_token_reserves is not set"),
            initial_virtual_sol_reserves: self
                .initial_virtual_sol_reserves
                .clone()
                .expect("initial_virtual_sol_reserves is not set"),
            initial_real_token_reserves: self
                .initial_real_token_reserves
                .clone()
                .expect("initial_real_token_reserves is not set"),
            token_total_supply: self
                .token_total_supply
                .clone()
                .expect("token_total_supply is not set"),
            fee_basis_points: self
                .fee_basis_points
                .clone()
                .expect("fee_basis_points is not set"),
        };

        accounts.instruction_with_remaining_accounts(args, &self.__remaining_accounts)
    }
}

/// `set_params` CPI accounts.
pub struct SetParamsCpiAccounts<'a, 'b> {
    pub global: &'b solana_program::account_info::AccountInfo<'a>,

    pub user: &'b solana_program::account_info::AccountInfo<'a>,

    pub system_program: &'b solana_program::account_info::AccountInfo<'a>,

    pub event_authority: &'b solana_program::account_info::AccountInfo<'a>,

    pub program: &'b solana_program::account_info::AccountInfo<'a>,
}

/// `set_params` CPI instruction.
pub struct SetParamsCpi<'a, 'b> {
    /// The program to invoke.
    pub __program: &'b solana_program::account_info::AccountInfo<'a>,

    pub global: &'b solana_program::account_info::AccountInfo<'a>,

    pub user: &'b solana_program::account_info::AccountInfo<'a>,

    pub system_program: &'b solana_program::account_info::AccountInfo<'a>,

    pub event_authority: &'b solana_program::account_info::AccountInfo<'a>,

    pub program: &'b solana_program::account_info::AccountInfo<'a>,
    /// The arguments for the instruction.
    pub __args: SetParamsInstructionArgs,
}

impl<'a, 'b> SetParamsCpi<'a, 'b> {
    pub fn new(
        program: &'b solana_program::account_info::AccountInfo<'a>,
        accounts: SetParamsCpiAccounts<'a, 'b>,
        args: SetParamsInstructionArgs,
    ) -> Self {
        Self {
            __program: program,
            global: accounts.global,
            user: accounts.user,
            system_program: accounts.system_program,
            event_authority: accounts.event_authority,
            program: accounts.program,
            __args: args,
        }
    }
    #[inline(always)]
    pub fn invoke(&self) -> solana_program::entrypoint::ProgramResult {
        self.invoke_signed_with_remaining_accounts(&[], &[])
    }
    #[inline(always)]
    pub fn invoke_with_remaining_accounts(
        &self,
        remaining_accounts: &[(
            &'b solana_program::account_info::AccountInfo<'a>,
            bool,
            bool,
        )],
    ) -> solana_program::entrypoint::ProgramResult {
        self.invoke_signed_with_remaining_accounts(&[], remaining_accounts)
    }
    #[inline(always)]
    pub fn invoke_signed(
        &self,
        signers_seeds: &[&[&[u8]]],
    ) -> solana_program::entrypoint::ProgramResult {
        self.invoke_signed_with_remaining_accounts(signers_seeds, &[])
    }
    #[allow(clippy::arithmetic_side_effects)]
    #[allow(clippy::clone_on_copy)]
    #[allow(clippy::vec_init_then_push)]
    pub fn invoke_signed_with_remaining_accounts(
        &self,
        signers_seeds: &[&[&[u8]]],
        remaining_accounts: &[(
            &'b solana_program::account_info::AccountInfo<'a>,
            bool,
            bool,
        )],
    ) -> solana_program::entrypoint::ProgramResult {
        let mut accounts = Vec::with_capacity(5 + remaining_accounts.len());
        accounts.push(solana_program::instruction::AccountMeta::new(
            *self.global.key,
            false,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new(
            *self.user.key,
            true,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new_readonly(
            *self.system_program.key,
            false,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new_readonly(
            *self.event_authority.key,
            false,
        ));
        accounts.push(solana_program::instruction::AccountMeta::new_readonly(
            *self.program.key,
            false,
        ));
        remaining_accounts.iter().for_each(|remaining_account| {
            accounts.push(solana_program::instruction::AccountMeta {
                pubkey: *remaining_account.0.key,
                is_signer: remaining_account.1,
                is_writable: remaining_account.2,
            })
        });
        let mut data = borsh::to_vec(&SetParamsInstructionData::new()).unwrap();
        let mut args = borsh::to_vec(&self.__args).unwrap();
        data.append(&mut args);

        let instruction = solana_program::instruction::Instruction {
            program_id: crate::PUMP_ID,
            accounts,
            data,
        };
        let mut account_infos = Vec::with_capacity(6 + remaining_accounts.len());
        account_infos.push(self.__program.clone());
        account_infos.push(self.global.clone());
        account_infos.push(self.user.clone());
        account_infos.push(self.system_program.clone());
        account_infos.push(self.event_authority.clone());
        account_infos.push(self.program.clone());
        remaining_accounts
            .iter()
            .for_each(|remaining_account| account_infos.push(remaining_account.0.clone()));

        if signers_seeds.is_empty() {
            solana_program::program::invoke(&instruction, &account_infos)
        } else {
            solana_program::program::invoke_signed(&instruction, &account_infos, signers_seeds)
        }
    }
}

/// Instruction builder for `SetParams` via CPI.
///
/// ### Accounts:
///
///   0. `[writable]` global
///   1. `[writable, signer]` user
///   2. `[]` system_program
///   3. `[]` event_authority
///   4. `[]` program
#[derive(Clone, Debug)]
pub struct SetParamsCpiBuilder<'a, 'b> {
    instruction: Box<SetParamsCpiBuilderInstruction<'a, 'b>>,
}

impl<'a, 'b> SetParamsCpiBuilder<'a, 'b> {
    pub fn new(program: &'b solana_program::account_info::AccountInfo<'a>) -> Self {
        let instruction = Box::new(SetParamsCpiBuilderInstruction {
            __program: program,
            global: None,
            user: None,
            system_program: None,
            event_authority: None,
            program: None,
            fee_recipient: None,
            initial_virtual_token_reserves: None,
            initial_virtual_sol_reserves: None,
            initial_real_token_reserves: None,
            token_total_supply: None,
            fee_basis_points: None,
            __remaining_accounts: Vec::new(),
        });
        Self { instruction }
    }
    #[inline(always)]
    pub fn global(
        &mut self,
        global: &'b solana_program::account_info::AccountInfo<'a>,
    ) -> &mut Self {
        self.instruction.global = Some(global);
        self
    }
    #[inline(always)]
    pub fn user(&mut self, user: &'b solana_program::account_info::AccountInfo<'a>) -> &mut Self {
        self.instruction.user = Some(user);
        self
    }
    #[inline(always)]
    pub fn system_program(
        &mut self,
        system_program: &'b solana_program::account_info::AccountInfo<'a>,
    ) -> &mut Self {
        self.instruction.system_program = Some(system_program);
        self
    }
    #[inline(always)]
    pub fn event_authority(
        &mut self,
        event_authority: &'b solana_program::account_info::AccountInfo<'a>,
    ) -> &mut Self {
        self.instruction.event_authority = Some(event_authority);
        self
    }
    #[inline(always)]
    pub fn program(
        &mut self,
        program: &'b solana_program::account_info::AccountInfo<'a>,
    ) -> &mut Self {
        self.instruction.program = Some(program);
        self
    }
    #[inline(always)]
    pub fn fee_recipient(&mut self, fee_recipient: Pubkey) -> &mut Self {
        self.instruction.fee_recipient = Some(fee_recipient);
        self
    }
    #[inline(always)]
    pub fn initial_virtual_token_reserves(
        &mut self,
        initial_virtual_token_reserves: u64,
    ) -> &mut Self {
        self.instruction.initial_virtual_token_reserves = Some(initial_virtual_token_reserves);
        self
    }
    #[inline(always)]
    pub fn initial_virtual_sol_reserves(&mut self, initial_virtual_sol_reserves: u64) -> &mut Self {
        self.instruction.initial_virtual_sol_reserves = Some(initial_virtual_sol_reserves);
        self
    }
    #[inline(always)]
    pub fn initial_real_token_reserves(&mut self, initial_real_token_reserves: u64) -> &mut Self {
        self.instruction.initial_real_token_reserves = Some(initial_real_token_reserves);
        self
    }
    #[inline(always)]
    pub fn token_total_supply(&mut self, token_total_supply: u64) -> &mut Self {
        self.instruction.token_total_supply = Some(token_total_supply);
        self
    }
    #[inline(always)]
    pub fn fee_basis_points(&mut self, fee_basis_points: u64) -> &mut Self {
        self.instruction.fee_basis_points = Some(fee_basis_points);
        self
    }
    /// Add an additional account to the instruction.
    #[inline(always)]
    pub fn add_remaining_account(
        &mut self,
        account: &'b solana_program::account_info::AccountInfo<'a>,
        is_writable: bool,
        is_signer: bool,
    ) -> &mut Self {
        self.instruction
            .__remaining_accounts
            .push((account, is_writable, is_signer));
        self
    }
    /// Add additional accounts to the instruction.
    ///
    /// Each account is represented by a tuple of the `AccountInfo`, a `bool` indicating whether the account is writable or not,
    /// and a `bool` indicating whether the account is a signer or not.
    #[inline(always)]
    pub fn add_remaining_accounts(
        &mut self,
        accounts: &[(
            &'b solana_program::account_info::AccountInfo<'a>,
            bool,
            bool,
        )],
    ) -> &mut Self {
        self.instruction
            .__remaining_accounts
            .extend_from_slice(accounts);
        self
    }
    #[inline(always)]
    pub fn invoke(&self) -> solana_program::entrypoint::ProgramResult {
        self.invoke_signed(&[])
    }
    #[allow(clippy::clone_on_copy)]
    #[allow(clippy::vec_init_then_push)]
    pub fn invoke_signed(
        &self,
        signers_seeds: &[&[&[u8]]],
    ) -> solana_program::entrypoint::ProgramResult {
        let args = SetParamsInstructionArgs {
            fee_recipient: self
                .instruction
                .fee_recipient
                .clone()
                .expect("fee_recipient is not set"),
            initial_virtual_token_reserves: self
                .instruction
                .initial_virtual_token_reserves
                .clone()
                .expect("initial_virtual_token_reserves is not set"),
            initial_virtual_sol_reserves: self
                .instruction
                .initial_virtual_sol_reserves
                .clone()
                .expect("initial_virtual_sol_reserves is not set"),
            initial_real_token_reserves: self
                .instruction
                .initial_real_token_reserves
                .clone()
                .expect("initial_real_token_reserves is not set"),
            token_total_supply: self
                .instruction
                .token_total_supply
                .clone()
                .expect("token_total_supply is not set"),
            fee_basis_points: self
                .instruction
                .fee_basis_points
                .clone()
                .expect("fee_basis_points is not set"),
        };
        let instruction = SetParamsCpi {
            __program: self.instruction.__program,

            global: self.instruction.global.expect("global is not set"),

            user: self.instruction.user.expect("user is not set"),

            system_program: self
                .instruction
                .system_program
                .expect("system_program is not set"),

            event_authority: self
                .instruction
                .event_authority
                .expect("event_authority is not set"),

            program: self.instruction.program.expect("program is not set"),
            __args: args,
        };
        instruction.invoke_signed_with_remaining_accounts(
            signers_seeds,
            &self.instruction.__remaining_accounts,
        )
    }
}

#[derive(Clone, Debug)]
struct SetParamsCpiBuilderInstruction<'a, 'b> {
    __program: &'b solana_program::account_info::AccountInfo<'a>,
    global: Option<&'b solana_program::account_info::AccountInfo<'a>>,
    user: Option<&'b solana_program::account_info::AccountInfo<'a>>,
    system_program: Option<&'b solana_program::account_info::AccountInfo<'a>>,
    event_authority: Option<&'b solana_program::account_info::AccountInfo<'a>>,
    program: Option<&'b solana_program::account_info::AccountInfo<'a>>,
    fee_recipient: Option<Pubkey>,
    initial_virtual_token_reserves: Option<u64>,
    initial_virtual_sol_reserves: Option<u64>,
    initial_real_token_reserves: Option<u64>,
    token_total_supply: Option<u64>,
    fee_basis_points: Option<u64>,
    /// Additional instruction accounts `(AccountInfo, is_writable, is_signer)`.
    __remaining_accounts: Vec<(
        &'b solana_program::account_info::AccountInfo<'a>,
        bool,
        bool,
    )>,
}

//! This code was AUTOGENERATED using the codama library.
//! Please DO NOT EDIT THIS FILE, instead use visitors
//! to add features, then rerun codama to update it.
//!
//! <https://github.com/codama-idl/codama>
//!

use crate::instructions::{
    Buy as BuyIxAccounts, BuyInstructionArgs as BuyIxData, Create as CreateIxAccounts,
    CreateInstructionArgs as CreateIxData, Initialize as InitializeIxAccounts,
    Sell as SellIxAccounts, SellInstructionArgs as SellIxData, SetParams as SetParamsIxAccounts,
    SetParamsInstructionArgs as SetParamsIxData, Withdraw as WithdrawIxAccounts,
};
use crate::types::TradeEvent;
use crate::ID;
use base64::{engine::general_purpose::STANDARD as BASE64, Engine as _};
use borsh::BorshDeserialize;

/// Pump Instructions
#[derive(Debug)]
#[cfg_attr(feature = "tracing", derive(strum_macros::Display))]
pub enum PumpProgramIx {
    Initialize(InitializeIxAccounts),
    SetParams(SetParamsIxAccounts, SetParamsIxData),
    Create(CreateIxAccounts, CreateIxData),
    Buy(BuyIxAccounts, BuyIxData, Option<TradeEvent>),
    Sell(SellIxAccounts, SellIxData, Option<TradeEvent>),
    Withdraw(WithdrawIxAccounts),
}

#[derive(Debug, Copy, Clone)]
pub struct InstructionParser;

impl yellowstone_vixen_core::Parser for InstructionParser {
    type Input = yellowstone_vixen_core::instruction::InstructionUpdate;
    type Output = PumpProgramIx;

    fn id(&self) -> std::borrow::Cow<str> {
        "Pump::InstructionParser".into()
    }

    fn prefilter(&self) -> yellowstone_vixen_core::Prefilter {
        yellowstone_vixen_core::Prefilter::builder()
            .transaction_accounts([ID])
            .build()
            .unwrap()
    }

    async fn parse(
        &self,
        ix_update: &yellowstone_vixen_core::instruction::InstructionUpdate,
    ) -> yellowstone_vixen_core::ParseResult<Self::Output> {
        // filter out failed transactions
        if ix_update.shared.err.is_some() {
            return Err(yellowstone_vixen_core::ParseError::Filtered);
        }

        if ix_update.program.equals_ref(ID) {
            InstructionParser::parse_impl(ix_update)
        } else {
            Err(yellowstone_vixen_core::ParseError::Filtered)
        }
    }
}

impl yellowstone_vixen_core::ProgramParser for InstructionParser {
    #[inline]
    fn program_id(&self) -> yellowstone_vixen_core::Pubkey {
        ID.to_bytes().into()
    }
}

impl InstructionParser {
    pub(crate) fn parse_impl(
        ix: &yellowstone_vixen_core::instruction::InstructionUpdate,
    ) -> yellowstone_vixen_core::ParseResult<PumpProgramIx> {
        let accounts_len = ix.accounts.len();

        let ix_discriminator: [u8; 8] = ix.data[0..8].try_into()?;
        let mut ix_data = &ix.data[8..];
        let ix = match ix_discriminator {
            [175, 175, 109, 31, 13, 152, 155, 237] => {
                check_min_accounts_req(accounts_len, 3)?;
                let ix_accounts = InitializeIxAccounts {
                    global: ix.accounts[0].0.into(),
                    user: ix.accounts[1].0.into(),
                    system_program: ix.accounts[2].0.into(),
                };
                Ok(PumpProgramIx::Initialize(ix_accounts))
            }
            [165, 31, 134, 53, 189, 180, 130, 255] => {
                check_min_accounts_req(accounts_len, 5)?;
                let ix_accounts = SetParamsIxAccounts {
                    global: ix.accounts[0].0.into(),
                    user: ix.accounts[1].0.into(),
                    system_program: ix.accounts[2].0.into(),
                    event_authority: ix.accounts[3].0.into(),
                    program: ix.accounts[4].0.into(),
                };
                let de_ix_data: SetParamsIxData = BorshDeserialize::deserialize(&mut ix_data)?;
                Ok(PumpProgramIx::SetParams(ix_accounts, de_ix_data))
            }
            [24, 30, 200, 40, 5, 28, 7, 119] => {
                check_min_accounts_req(accounts_len, 14)?;
                let ix_accounts = CreateIxAccounts {
                    mint: ix.accounts[0].0.into(),
                    mint_authority: ix.accounts[1].0.into(),
                    bonding_curve: ix.accounts[2].0.into(),
                    associated_bonding_curve: ix.accounts[3].0.into(),
                    global: ix.accounts[4].0.into(),
                    mpl_token_metadata: ix.accounts[5].0.into(),
                    metadata: ix.accounts[6].0.into(),
                    user: ix.accounts[7].0.into(),
                    system_program: ix.accounts[8].0.into(),
                    token_program: ix.accounts[9].0.into(),
                    associated_token_program: ix.accounts[10].0.into(),
                    rent: ix.accounts[11].0.into(),
                    event_authority: ix.accounts[12].0.into(),
                    program: ix.accounts[13].0.into(),
                };
                let de_ix_data: CreateIxData = BorshDeserialize::deserialize(&mut ix_data)?;
                Ok(PumpProgramIx::Create(ix_accounts, de_ix_data))
            }
            [102, 6, 61, 18, 1, 218, 235, 234] => {
                check_min_accounts_req(accounts_len, 12)?;
                let ix_accounts = BuyIxAccounts {
                    global: ix.accounts[0].0.into(),
                    fee_recipient: ix.accounts[1].0.into(),
                    mint: ix.accounts[2].0.into(),
                    bonding_curve: ix.accounts[3].0.into(),
                    associated_bonding_curve: ix.accounts[4].0.into(),
                    associated_user: ix.accounts[5].0.into(),
                    user: ix.accounts[6].0.into(),
                    system_program: ix.accounts[7].0.into(),
                    token_program: ix.accounts[8].0.into(),
                    rent: ix.accounts[9].0.into(),
                    event_authority: ix.accounts[10].0.into(),
                    program: ix.accounts[11].0.into(),
                };
                let de_ix_data: BuyIxData = BorshDeserialize::deserialize(&mut ix_data)?;

                let trade_event = ix.shared.log_messages.iter().find_map(|log| {
                    log.strip_prefix("Program data: ")
                        .and_then(|base64_data| BASE64.decode(base64_data).ok())
                        .filter(|data| data.starts_with(&[189, 219, 127, 211, 78, 230, 97, 238]))
                        .and_then(|data| TradeEvent::try_from_slice(&data[8..]).ok())
                        .filter(|event| event.is_buy && event.token_amount == de_ix_data.amount)
                });

                Ok(PumpProgramIx::Buy(ix_accounts, de_ix_data, trade_event))
            }
            [51, 230, 133, 164, 1, 127, 131, 173] => {
                check_min_accounts_req(accounts_len, 12)?;
                let ix_accounts = SellIxAccounts {
                    global: ix.accounts[0].0.into(),
                    fee_recipient: ix.accounts[1].0.into(),
                    mint: ix.accounts[2].0.into(),
                    bonding_curve: ix.accounts[3].0.into(),
                    associated_bonding_curve: ix.accounts[4].0.into(),
                    associated_user: ix.accounts[5].0.into(),
                    user: ix.accounts[6].0.into(),
                    system_program: ix.accounts[7].0.into(),
                    associated_token_program: ix.accounts[8].0.into(),
                    token_program: ix.accounts[9].0.into(),
                    event_authority: ix.accounts[10].0.into(),
                    program: ix.accounts[11].0.into(),
                };
                let de_ix_data: SellIxData = BorshDeserialize::deserialize(&mut ix_data)?;

                let trade_event = ix.shared.log_messages.iter().find_map(|log| {
                    log.strip_prefix("Program data: ")
                        .and_then(|base64_data| BASE64.decode(base64_data).ok())
                        .filter(|data| data.starts_with(&[189, 219, 127, 211, 78, 230, 97, 238]))
                        .and_then(|data| TradeEvent::try_from_slice(&data[8..]).ok())
                        .filter(|event| !event.is_buy && event.token_amount == de_ix_data.amount)
                });

                Ok(PumpProgramIx::Sell(ix_accounts, de_ix_data, trade_event))
            }
            [183, 18, 70, 156, 148, 109, 161, 34] => {
                check_min_accounts_req(accounts_len, 12)?;
                let ix_accounts = WithdrawIxAccounts {
                    global: ix.accounts[0].0.into(),
                    last_withdraw: ix.accounts[1].0.into(),
                    mint: ix.accounts[2].0.into(),
                    bonding_curve: ix.accounts[3].0.into(),
                    associated_bonding_curve: ix.accounts[4].0.into(),
                    associated_user: ix.accounts[5].0.into(),
                    user: ix.accounts[6].0.into(),
                    system_program: ix.accounts[7].0.into(),
                    token_program: ix.accounts[8].0.into(),
                    rent: ix.accounts[9].0.into(),
                    event_authority: ix.accounts[10].0.into(),
                    program: ix.accounts[11].0.into(),
                };
                Ok(PumpProgramIx::Withdraw(ix_accounts))
            }
            _ => Err(yellowstone_vixen_core::ParseError::from(
                "Invalid Instruction discriminator".to_owned(),
            )),
        };

        #[cfg(feature = "tracing")]
        match &ix {
            Ok(ix) => {
                tracing::info!(
                    name: "correctly_parsed_instruction",
                    name = "ix_update",
                    program = ID.to_string(),
                    ix = ix.to_string()
                );
            }
            Err(e) => {
                tracing::info!(
                    name: "incorrectly_parsed_instruction",
                    name = "ix_update",
                    program = ID.to_string(),
                    ix = "error",
                    discriminator = ?ix_discriminator,
                    error = ?e
                );
            }
        }

        ix
    }
}

pub fn check_min_accounts_req(
    actual: usize,
    expected: usize,
) -> yellowstone_vixen_core::ParseResult<()> {
    if actual < expected {
        Err(yellowstone_vixen_core::ParseError::from(format!(
            "Too few accounts provided: expected {expected}, got {actual}"
        )))
    } else {
        Ok(())
    }
}

// #[cfg(feature = "proto")]
mod proto_parser {
    use super::{InstructionParser, PumpProgramIx};
    use crate::{proto_def, proto_helpers::proto_types_parsers::IntoProto};
    use yellowstone_vixen_core::proto::ParseProto;

    use super::InitializeIxAccounts;
    impl IntoProto<proto_def::InitializeIxAccounts> for InitializeIxAccounts {
        fn into_proto(self) -> proto_def::InitializeIxAccounts {
            proto_def::InitializeIxAccounts {
                global: self.global.to_string(),
                user: self.user.to_string(),
                system_program: self.system_program.to_string(),
            }
        }
    }
    use super::SetParamsIxAccounts;
    impl IntoProto<proto_def::SetParamsIxAccounts> for SetParamsIxAccounts {
        fn into_proto(self) -> proto_def::SetParamsIxAccounts {
            proto_def::SetParamsIxAccounts {
                global: self.global.to_string(),
                user: self.user.to_string(),
                system_program: self.system_program.to_string(),
                event_authority: self.event_authority.to_string(),
                program: self.program.to_string(),
            }
        }
    }
    use super::SetParamsIxData;
    impl IntoProto<proto_def::SetParamsIxData> for SetParamsIxData {
        fn into_proto(self) -> proto_def::SetParamsIxData {
            proto_def::SetParamsIxData {
                fee_recipient: self.fee_recipient.to_string(),
                initial_virtual_token_reserves: self.initial_virtual_token_reserves,
                initial_virtual_sol_reserves: self.initial_virtual_sol_reserves,
                initial_real_token_reserves: self.initial_real_token_reserves,
                token_total_supply: self.token_total_supply,
                fee_basis_points: self.fee_basis_points,
            }
        }
    }
    use super::CreateIxAccounts;
    impl IntoProto<proto_def::CreateIxAccounts> for CreateIxAccounts {
        fn into_proto(self) -> proto_def::CreateIxAccounts {
            proto_def::CreateIxAccounts {
                mint: self.mint.to_string(),
                mint_authority: self.mint_authority.to_string(),
                bonding_curve: self.bonding_curve.to_string(),
                associated_bonding_curve: self.associated_bonding_curve.to_string(),
                global: self.global.to_string(),
                mpl_token_metadata: self.mpl_token_metadata.to_string(),
                metadata: self.metadata.to_string(),
                user: self.user.to_string(),
                system_program: self.system_program.to_string(),
                token_program: self.token_program.to_string(),
                associated_token_program: self.associated_token_program.to_string(),
                rent: self.rent.to_string(),
                event_authority: self.event_authority.to_string(),
                program: self.program.to_string(),
            }
        }
    }
    use super::CreateIxData;
    impl IntoProto<proto_def::CreateIxData> for CreateIxData {
        fn into_proto(self) -> proto_def::CreateIxData {
            proto_def::CreateIxData {
                name: self.name,
                symbol: self.symbol,
                uri: self.uri,
                creator: self.creator.to_string(),
            }
        }
    }
    use super::BuyIxAccounts;
    impl IntoProto<proto_def::BuyIxAccounts> for BuyIxAccounts {
        fn into_proto(self) -> proto_def::BuyIxAccounts {
            proto_def::BuyIxAccounts {
                global: self.global.to_string(),
                fee_recipient: self.fee_recipient.to_string(),
                mint: self.mint.to_string(),
                bonding_curve: self.bonding_curve.to_string(),
                associated_bonding_curve: self.associated_bonding_curve.to_string(),
                associated_user: self.associated_user.to_string(),
                user: self.user.to_string(),
                system_program: self.system_program.to_string(),
                token_program: self.token_program.to_string(),
                rent: self.rent.to_string(),
                event_authority: self.event_authority.to_string(),
                program: self.program.to_string(),
            }
        }
    }
    use super::BuyIxData;
    impl IntoProto<proto_def::BuyIxData> for BuyIxData {
        fn into_proto(self) -> proto_def::BuyIxData {
            proto_def::BuyIxData {
                amount: self.amount,
                max_sol_cost: self.max_sol_cost,
            }
        }
    }
    use super::SellIxAccounts;
    impl IntoProto<proto_def::SellIxAccounts> for SellIxAccounts {
        fn into_proto(self) -> proto_def::SellIxAccounts {
            proto_def::SellIxAccounts {
                global: self.global.to_string(),
                fee_recipient: self.fee_recipient.to_string(),
                mint: self.mint.to_string(),
                bonding_curve: self.bonding_curve.to_string(),
                associated_bonding_curve: self.associated_bonding_curve.to_string(),
                associated_user: self.associated_user.to_string(),
                user: self.user.to_string(),
                system_program: self.system_program.to_string(),
                associated_token_program: self.associated_token_program.to_string(),
                token_program: self.token_program.to_string(),
                event_authority: self.event_authority.to_string(),
                program: self.program.to_string(),
            }
        }
    }
    use super::SellIxData;
    impl IntoProto<proto_def::SellIxData> for SellIxData {
        fn into_proto(self) -> proto_def::SellIxData {
            proto_def::SellIxData {
                amount: self.amount,
                min_sol_output: self.min_sol_output,
            }
        }
    }
    use super::WithdrawIxAccounts;
    impl IntoProto<proto_def::WithdrawIxAccounts> for WithdrawIxAccounts {
        fn into_proto(self) -> proto_def::WithdrawIxAccounts {
            proto_def::WithdrawIxAccounts {
                global: self.global.to_string(),
                last_withdraw: self.last_withdraw.to_string(),
                mint: self.mint.to_string(),
                bonding_curve: self.bonding_curve.to_string(),
                associated_bonding_curve: self.associated_bonding_curve.to_string(),
                associated_user: self.associated_user.to_string(),
                user: self.user.to_string(),
                system_program: self.system_program.to_string(),
                token_program: self.token_program.to_string(),
                rent: self.rent.to_string(),
                event_authority: self.event_authority.to_string(),
                program: self.program.to_string(),
            }
        }
    }

    impl IntoProto<proto_def::ProgramIxs> for PumpProgramIx {
        fn into_proto(self) -> proto_def::ProgramIxs {
            match self {
                PumpProgramIx::Initialize(acc) => proto_def::ProgramIxs {
                    ix_oneof: Some(proto_def::program_ixs::IxOneof::Initialize(
                        proto_def::InitializeIx {
                            accounts: Some(acc.into_proto()),
                        },
                    )),
                },
                PumpProgramIx::SetParams(acc, data) => proto_def::ProgramIxs {
                    ix_oneof: Some(proto_def::program_ixs::IxOneof::SetParams(
                        proto_def::SetParamsIx {
                            accounts: Some(acc.into_proto()),
                            data: Some(data.into_proto()),
                        },
                    )),
                },
                PumpProgramIx::Create(acc, data) => proto_def::ProgramIxs {
                    ix_oneof: Some(proto_def::program_ixs::IxOneof::Create(
                        proto_def::CreateIx {
                            accounts: Some(acc.into_proto()),
                            data: Some(data.into_proto()),
                        },
                    )),
                },
                PumpProgramIx::Buy(acc, data, trade_event) => proto_def::ProgramIxs {
                    ix_oneof: Some(proto_def::program_ixs::IxOneof::Buy(proto_def::BuyIx {
                        accounts: Some(acc.into_proto()),
                        data: Some(data.into_proto()),
                        trade_event: trade_event.map(|e| e.into_proto()),
                    })),
                },
                PumpProgramIx::Sell(acc, data, trade_event) => proto_def::ProgramIxs {
                    ix_oneof: Some(proto_def::program_ixs::IxOneof::Sell(proto_def::SellIx {
                        accounts: Some(acc.into_proto()),
                        data: Some(data.into_proto()),
                        trade_event: trade_event.map(|e| e.into_proto()),
                    })),
                },
                PumpProgramIx::Withdraw(acc) => proto_def::ProgramIxs {
                    ix_oneof: Some(proto_def::program_ixs::IxOneof::Withdraw(
                        proto_def::WithdrawIx {
                            accounts: Some(acc.into_proto()),
                        },
                    )),
                },
            }
        }
    }

    impl ParseProto for InstructionParser {
        type Message = proto_def::ProgramIxs;

        fn output_into_message(value: Self::Output) -> Self::Message {
            value.into_proto()
        }
    }
}

[package]
name = "kryptogo-vixen-pumpfun-parser"
version = "0.1.0"
edition = "2021"

[dependencies]
tonic = { version = "0.12.1", features = ["gzip", "zstd"] }
borsh = "^0.10"
solana-program = { workspace = true }
yellowstone-vixen-core = { workspace = true, features = ["proto"] }
num-derive = "0.4"
thiserror = "1.0.64"
tracing = { version = "0.1.40", optional = true }
strum_macros = { version = "0.24", optional = true }
base64 = "0.22.0"
prost = { workspace = true }
num-traits = "0.2.19"

[features]
anchor = []
anchor-idl-build = []
serde = []
test-sbf = []
fetch = []
tracing = ["dep:tracing", "dep:strum_macros"]

[build-dependencies]
prost-build = "0.13.1"

[package.metadata.cargo-machete]
ignored = ["prost", "num-traits"]

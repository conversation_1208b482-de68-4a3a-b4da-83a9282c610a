//! This code was AUTOGENERATED using the codama library.
//! Please DO NOT EDIT THIS FILE, instead use visitors
//! to add features, then rerun codama to update it.
//!
//! <https://github.com/codama-idl/codama>
//!
syntax = "proto3";

package vixen.parser.pumpfun;

message InitializeIx {
	InitializeIxAccounts accounts = 1;
}

message SetParamsIx {
	SetParamsIxAccounts accounts = 1;
	SetParamsIxData data = 2;
}

message CreateIx {
	CreateIxAccounts accounts = 1;
	CreateIxData data = 2;
}

message BuyIx {
	BuyIxAccounts accounts = 1;
	BuyIxData data = 2;
	TradeEvent trade_event = 3;
}

message SellIx {
	SellIxAccounts accounts = 1;
	SellIxData data = 2;
	TradeEvent trade_event = 3;
}

message WithdrawIx {
	WithdrawIxAccounts accounts = 1;
}


message LastWithdraw {
	int64 last_withdraw_timestamp = 1;
}

message CreateEvent {
	string name = 1;
	string symbol = 2;
	string uri = 3;
	string mint = 4;
	string bonding_curve = 5;
	string user = 6;
}

message TradeEvent {
	string mint = 1;
	uint64 sol_amount = 2;
	uint64 token_amount = 3;
	bool is_buy = 4;
	string user = 5;
	int64 timestamp = 6;
	uint64 virtual_sol_reserves = 7;
	uint64 virtual_token_reserves = 8;
	uint64 real_sol_reserves = 9;
	uint64 real_token_reserves = 10;
}

message CompleteEvent {
	string user = 1;
	string mint = 2;
	string bonding_curve = 3;
	int64 timestamp = 4;
}

message SetParamsEvent {
	string fee_recipient = 1;
	uint64 initial_virtual_token_reserves = 2;
	uint64 initial_virtual_sol_reserves = 3;
	uint64 initial_real_token_reserves = 4;
	uint64 token_total_supply = 5;
	uint64 fee_basis_points = 6;
}


message BondingCurve {
	uint64 virtual_token_reserves = 1;
	uint64 virtual_sol_reserves = 2;
	uint64 real_token_reserves = 3;
	uint64 real_sol_reserves = 4;
	uint64 token_total_supply = 5;
	bool complete = 6;
}

message Global {
	bool initialized = 1;
	string authority = 2;
	string fee_recipient = 3;
	uint64 initial_virtual_token_reserves = 4;
	uint64 initial_virtual_sol_reserves = 5;
	uint64 initial_real_token_reserves = 6;
	uint64 token_total_supply = 7;
	uint64 fee_basis_points = 8;
}


message InitializeIxAccounts {
	string global = 1;
	string user = 2;
	string system_program = 3;
}


message SetParamsIxAccounts {
	string global = 1;
	string user = 2;
	string system_program = 3;
	string event_authority = 4;
	string program = 5;
}

message SetParamsIxData {
	string fee_recipient = 1;
	uint64 initial_virtual_token_reserves = 2;
	uint64 initial_virtual_sol_reserves = 3;
	uint64 initial_real_token_reserves = 4;
	uint64 token_total_supply = 5;
	uint64 fee_basis_points = 6;
}

message CreateIxAccounts {
	string mint = 1;
	string mint_authority = 2;
	string bonding_curve = 3;
	string associated_bonding_curve = 4;
	string global = 5;
	string mpl_token_metadata = 6;
	string metadata = 7;
	string user = 8;
	string system_program = 9;
	string token_program = 10;
	string associated_token_program = 11;
	string rent = 12;
	string event_authority = 13;
	string program = 14;
}

message CreateIxData {
	string name = 1;
	string symbol = 2;
	string uri = 3;
	string creator = 4;
}

message BuyIxAccounts {
	string global = 1;
	string fee_recipient = 2;
	string mint = 3;
	string bonding_curve = 4;
	string associated_bonding_curve = 5;
	string associated_user = 6;
	string user = 7;
	string system_program = 8;
	string token_program = 9;
	string rent = 10;
	string event_authority = 11;
	string program = 12;
}

message BuyIxData {
	uint64 amount = 1;
	uint64 max_sol_cost = 2;
}

message SellIxAccounts {
	string global = 1;
	string fee_recipient = 2;
	string mint = 3;
	string bonding_curve = 4;
	string associated_bonding_curve = 5;
	string associated_user = 6;
	string user = 7;
	string system_program = 8;
	string associated_token_program = 9;
	string token_program = 10;
	string event_authority = 11;
	string program = 12;
}

message SellIxData {
	uint64 amount = 1;
	uint64 min_sol_output = 2;
}

message WithdrawIxAccounts {
	string global = 1;
	string last_withdraw = 2;
	string mint = 3;
	string bonding_curve = 4;
	string associated_bonding_curve = 5;
	string associated_user = 6;
	string user = 7;
	string system_program = 8;
	string token_program = 9;
	string rent = 10;
	string event_authority = 11;
	string program = 12;
}



message ProgramState {
    oneof state_oneof {
        	BondingCurve bonding_curve = 1;
        	Global global = 2;
        }
}

message ProgramIxs {
    oneof ix_oneof {
        	InitializeIx initialize = 1;
        	SetParamsIx set_params = 2;
        	CreateIx create = 3;
        	BuyIx buy = 4;
        	SellIx sell = 5;
        	WithdrawIx withdraw = 6;
        }
}


//! This code was AUTOGENERATED using the codama library.
//! Please DO NOT EDIT THIS FILE, instead use visitors
//! to add features, then rerun codama to update it.
//!
//! <https://github.com/codama-idl/codama>
//!

use std::{env, path::PathBuf};

fn main() {
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());

    // #[cfg(feature = "proto")]
    prost_build::Config::new()
        .enable_type_names()
        .file_descriptor_set_path(out_dir.join("descriptor.bin"))
        .compile_protos(&["proto/pumpfun.proto"], &["proto"])
        .unwrap();
}

// This file is @generated by prost-build.
#[derive(<PERSON>lone, PartialEq, ::prost::Message)]
pub struct EventMetadata {
    #[prost(bytes = "vec", tag = "1")]
    pub signature: ::prost::alloc::vec::Vec<u8>,
    #[prost(uint64, tag = "2")]
    pub slot: u64,
    #[prost(uint32, tag = "3")]
    pub ix_index: u32,
}
/// Topic: solana-sol-transfers
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SolTransferEvent {
    #[prost(message, optional, tag = "1")]
    pub metadata: ::core::option::Option<EventMetadata>,
    #[prost(string, tag = "2")]
    pub from_wallet: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub to_wallet: ::prost::alloc::string::String,
    /// in lamports
    #[prost(uint64, tag = "4")]
    pub amount_lamports: u64,
}
/// Topic: solana-token-transfers
#[derive(<PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct TokenTransferEvent {
    #[prost(message, optional, tag = "1")]
    pub metadata: ::core::option::Option<EventMetadata>,
    #[prost(string, tag = "2")]
    pub token_mint: ::prost::alloc::string::String,
    #[prost(string, tag = "3")]
    pub from_wallet: ::prost::alloc::string::String,
    #[prost(string, tag = "4")]
    pub to_wallet: ::prost::alloc::string::String,
    #[prost(string, tag = "5")]
    pub amount: ::prost::alloc::string::String,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SolanaEvent {
    #[prost(oneof = "solana_event::Event", tags = "1, 2")]
    pub event: ::core::option::Option<solana_event::Event>,
}
/// Nested message and enum types in `SolanaEvent`.
pub mod solana_event {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Event {
        #[prost(message, tag = "1")]
        SolTransfer(super::SolTransferEvent),
        #[prost(message, tag = "2")]
        TokenTransfer(super::TokenTransferEvent),
    }
}
/// Topic: solana-events
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct BatchedSolanaEvent {
    #[prost(message, repeated, tag = "1")]
    pub events: ::prost::alloc::vec::Vec<SolanaEvent>,
}

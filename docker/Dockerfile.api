# docker/Dockerfile.api
FROM rust:1.85 as builder
WORKDIR /app
COPY . .
RUN cargo build --release --bin api_server

# Using Debian trixie which contains the fixed zlib for CVE-2023-45853
FROM debian:trixie-slim
RUN apt-get update && apt-get install -y --no-install-recommends \
  openssl \
  ca-certificates \
  libssl3 \
  && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/api_server /usr/local/bin/
CMD ["api_server"]
# docker/Dockerfile.telegram_bot
FROM rust:1.85 as builder
WORKDIR /app
COPY . .
RUN cargo build --release --bin telegram_bot

# Using Debian trixie which contains the fixed zlib for CVE-2023-45853
FROM debian:trixie-slim
RUN apt-get update && apt-get install -y --no-install-recommends \
  openssl \
  ca-certificates \
  libssl3 \
  && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/telegram_bot /usr/local/bin/
CMD ["telegram_bot"]

name: Build and Deploy

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  PROJECT_ID: kryptogo-wallet-data
  REGION: asia-northeast1
  REPOSITORY: kg-solana-data
  IMAGE_NAME: stream-processor
  DB_USER: solana_data
  DB_NAME: solana_data

# Add concurrency configuration
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-and-push-stream-processor:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker
        run: gcloud auth configure-docker asia-northeast1-docker.pkg.dev

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ github.ref }}
            ${{ runner.os }}-buildx-

      - name: Cache target directory
        uses: actions/cache@v3
        with:
          path: /tmp/target-cache
          key: ${{ runner.os }}-target-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-target-${{ github.ref }}
            ${{ runner.os }}-target-

      - name: Create target cache directory
        run: mkdir -p /tmp/target-cache

      - name: Build and Push Stream Processor Container
        uses: docker/build-push-action@v5
        with:
          context: ./stream-processor
          push: true
          tags: |
            asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/stream-processor:${{ github.sha }}
            asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/stream-processor:latest
          cache-from: |
            type=local,src=/tmp/.buildx-cache
            type=registry,ref=asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/stream-processor:buildcache
          cache-to: |
            type=local,dest=/tmp/.buildx-cache-new,mode=max
            type=registry,ref=asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/stream-processor:buildcache,mode=max
          build-args: |
            BUILDKIT_INLINE_CACHE=1
          platforms: linux/amd64
          provenance: false
          sbom: false
          build-contexts: |
            target=/tmp/target-cache

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  build-and-push-data-writer:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker
        run: gcloud auth configure-docker asia-northeast1-docker.pkg.dev

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ github.ref }}
            ${{ runner.os }}-buildx-

      - name: Cache target directory
        uses: actions/cache@v3
        with:
          path: /tmp/target-cache
          key: ${{ runner.os }}-target-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-target-${{ github.ref }}
            ${{ runner.os }}-target-

      - name: Create target cache directory
        run: mkdir -p /tmp/target-cache

      - name: Build and Push Data Writer Container
        uses: docker/build-push-action@v5
        with:
          context: ./data-writer
          push: true
          tags: |
            asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/data-writer:${{ github.sha }}
            asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/data-writer:latest
          cache-from: |
            type=local,src=/tmp/.buildx-cache
            type=registry,ref=asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/data-writer:buildcache
          cache-to: |
            type=local,dest=/tmp/.buildx-cache-new,mode=max
            type=registry,ref=asia-northeast1-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/data-writer:buildcache,mode=max
          build-args: |
            BUILDKIT_INLINE_CACHE=1
          platforms: linux/amd64
          provenance: false
          sbom: false
          build-contexts: |
            target=/tmp/target-cache

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  update-database:
    needs: [build-and-push-stream-processor, build-and-push-data-writer]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Install Java
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "21"

      - name: Run Database Migration
        env:
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        run: |
          chmod +x ./scripts/deploy-db.sh
          ./scripts/deploy-db.sh

  deploy-to-gke:
    needs: [update-database]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          # This is the full resource name of your Workload Identity Provider.
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/github-actions-pool/providers/github-provider"
          service_account: "<EMAIL>"

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Install gke-gcloud-auth-plugin
        run: |
          sudo apt-get update
          sudo apt-get install -y apt-transport-https ca-certificates gnupg
          echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
          curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
          sudo apt-get update && sudo apt-get install -y google-cloud-sdk-gke-gcloud-auth-plugin

      - name: Get GKE credentials
        id: get-credentials
        uses: "google-github-actions/get-gke-credentials@v2"
        with:
          cluster_name: "solana-data-cluster"
          location: "asia-northeast1"
          use_connect_gateway: "true"

      - name: Deploy to GKE
        run: |
          echo "Current context: $(kubectl config current-context)"
          echo "Deploying application..."
          kubectl apply -f kubernetes/deployment.yaml
          kubectl rollout restart deployment -n solana-data
          kubectl rollout status deployment -n solana-data

name: Build and Deploy to Cloud Run

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    tags:
      - v[0-9]+.[0-9]+.[0-9]+
    branches:
      - main
      - release-*
  workflow_dispatch:
    inputs:
      env:
        description: "Environment"
        required: true
        type: choice
        options:
          - dev
          - staging
          - prod
        default: "dev"
      version:
        description: "Version string (e.g., R40). Required for staging and prod environments."
        required: false
        type: string

jobs:
  build-dev:
    runs-on: [self-hosted, Linux]
    if: ${{ github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'dev') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_DEV }}"
      - name: Fix permission
        run: |
          git config --global --add safe.directory $PWD
          chmod +x ./scripts/build.sh
      - name: Build
        run: |
          ./scripts/build.sh --version=latest

  deploy-dev:
    needs: build-dev
    runs-on: [self-hosted, Linux]
    if: ${{ github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'dev') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Trigger Terraform Deploy
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: kryptogo/terraform-config
          event-type: deploy
          client-payload: '{"newVersion": "latest", "env": "dev"}'

  build-staging:
    runs-on: [self-hosted, Linux]
    if: ${{ startsWith(github.ref, 'refs/heads/release-') || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'staging') }}
    steps:
      - name: Check version input
        if: ${{ github.event_name == 'workflow_dispatch' }}
        run: |
          if [ -z "${{ github.event.inputs.version }}" ]; then
            echo "Version input is required for staging environment"
            exit 1
          elif ! [[ "${{ github.event.inputs.version }}" =~ ^R[0-9]+$ ]]; then
            echo "Version input must be in the format 'RXX' where XX is a number"
            exit 1
          fi
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_STAGING }}"
      - name: Fix permission
        run: |
          git config --global --add safe.directory $PWD
          chmod +x ./scripts/build.sh
      - name: Build
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            ./scripts/build.sh --version=${{ github.event.inputs.version }}
          else
            ./scripts/build.sh --version=$GITHUB_REF_NAME
          fi

  deploy-staging:
    needs: build-staging
    runs-on: [self-hosted, Linux]
    if: ${{ startsWith(github.ref, 'refs/heads/release-') || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'staging') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Trigger Terraform Deploy
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: kryptogo/terraform-config
          event-type: deploy
          client-payload: >-
            {
              "newVersion": "${{ github.event_name == 'workflow_dispatch' && github.event.inputs.version || github.ref_name }}", 
              "env": "staging"
            }

  build-prod:
    runs-on: [self-hosted, Linux]
    if: ${{ startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'prod') }}
    steps:
      - name: Check version input
        if: ${{ github.event_name == 'workflow_dispatch' }}
        run: |
          if [ -z "${{ github.event.inputs.version }}" ]; then
            echo "Version input is required for prod environment"
            exit 1
          elif ! [[ "${{ github.event.inputs.version }}" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "Version input must be in the format 'vX.Y.Z' where X, Y, Z are numbers"
            exit 1
          fi
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY }}"
      - name: Fix permission
        run: |
          git config --global --add safe.directory $PWD
          chmod +x ./scripts/build.sh
      - name: Build
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            ./scripts/build.sh --version=${{ github.event.inputs.version }}
          else
            ./scripts/build.sh --version=$GITHUB_REF_NAME
          fi

  deploy-prod:
    needs: build-prod
    runs-on: [self-hosted, Linux]
    if: ${{ startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'prod') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Trigger Terraform Deploy
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: kryptogo/terraform-config
          event-type: deploy
          client-payload: >-
            {
              "newVersion": "${{ github.event_name == 'workflow_dispatch' && github.event.inputs.version || github.ref_name }}", 
              "env": "prod"
            }

#!/bin/bash
#
# This script monitors a Solana validator and restarts it if it falls behind,
# based on a set of configurable conditions.
#

# --- Configuration ---
# The JSON RPC URL of your validator node.
RPC_URL="http://localhost:8899"

# The base path to your solana ledger.
# The user's example was '/solana/ledger', adjust if yours is different.
LEDGER_PATH="/solana/ledger"

# --- Thresholds ---
# Restart if the node is behind by more than this many slots.
SLOTS_BEHIND_THRESHOLD=50

# How long to wait (in seconds) after a restart before resuming checks.
RESTART_COOLDOWN=600

# A helper function to log messages to the system journal (and stdout).
# This makes it easy to check the script's activity with `journalctl`.
log() {
    # Log to systemd journal
    logger -t solana-monitor "$1"
    # Also echo to console for visibility when running manually
    echo "[$(date)] - $1"
}

log "Solana validator monitor started."

# --- Main Loop ---
while true; do
    # 1. Get the health status to find out how many slots the node is behind.
    health_response=$(curl -s -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}' "$RPC_URL")

    if [[ $? -ne 0 ]]; then
        log "Error: curl command to getHealth failed. Check if validator is running."
        sleep 10
        continue
    fi

    # If the '.error' field is null, the node is healthy. 'numSlotsBehind' is 0.
    # Otherwise, parse the number of slots behind. The `// 0` handles cases where the field is missing.
    if [[ $(echo "$health_response" | jq '.error') == "null" ]]; then
        numSlotsBehind=0
    else
        numSlotsBehind=$(echo "$health_response" | jq '.error.data.numSlotsBehind // 0')
    fi

    # 2. Get the current processed slot.
    slot_response=$(curl -s -X POST -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","id":1,"method":"getSlot","params":[{"commitment":"processed"}]}' "$RPC_URL")
    
    if [[ $? -ne 0 ]]; then
        log "Error: curl command to getSlot failed."
        sleep 10
        continue
    fi
    processedSlot=$(echo "$slot_response" | jq '.result')

    # 3. Find the latest snapshot slot number from the ledger directory.
    # The `2>/dev/null` suppresses errors if the directory doesn't exist yet.
    latestSnapshotSlot=$(sudo ls "$LEDGER_PATH/snapshots/" 2>/dev/null | grep -E '^[0-9]+$' | sort -n | tail -n 1)

    # 4. Validate that we received sane, numeric data before proceeding.
    if ! [[ "$numSlotsBehind" =~ ^[0-9]+$ && "$processedSlot" =~ ^[0-9]+$ && "$latestSnapshotSlot" =~ ^[0-9]+$ ]]; then
        log "Error: Failed to parse valid numeric data from RPC or snapshots. Retrying..."
        # Optional: uncomment the line below for detailed debugging
        # log "DEBUG: numSlotsBehind='$numSlotsBehind', processedSlot='$processedSlot', latestSnapshotSlot='$latestSnapshotSlot'"
        sleep 10
        continue
    fi
    
    # 5. Calculate the difference between the processed slot and the latest snapshot.
    slotDiff=$((processedSlot - latestSnapshotSlot))

    log "STATUS: Behind by $numSlotsBehind slots. Snapshot difference is $slotDiff slots."

    # 6. Check if the restart conditions are met.
    if (( numSlotsBehind > SLOTS_BEHIND_THRESHOLD )); then
        log "CONDITIONS MET: Node is behind by $numSlotsBehind slots."
        log "--> Issuing restart command for solana-validator.service..."
        
        sudo systemctl restart solana-validator.service
        
        log "Restart command sent. Cooling down for $RESTART_COOLDOWN seconds..."
        sleep "$RESTART_COOLDOWN"
    else
        # If conditions are not met, wait 10 seconds for the next check.
        sleep 10
    fi
done

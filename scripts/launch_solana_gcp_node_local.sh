#!/bin/bash

# -----------------------------------------------------------------------------
# GCP Solana 验证节点一键式部署脚本 (注入本地密钥并启动)
#
# 功能:
# 1. 下载主启动脚本。
# 2. 创建一个包含本地密钥注入逻辑和主脚本内容的最终启动脚本。
# 3. 使用最终启动脚本创建并配置 GCP 实例。
# -----------------------------------------------------------------------------

# --- 安全设置 ---
set -e    # 如果任何命令失败，则立即退出脚本
set -o pipefail # 管道中的命令失败也会导致整个管道失败

echo "=== GCP Solana 节点部署脚本启动 (含本地密钥注入) ==="

# --- 用户配置变量 (请务必修改以下所有带 YOUR_... 的值) ---

# 1. 本地 GCS 访问配置
# 修改成自己的路径
LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD="/Users/<USER>/Downloads/solana/centered-rope-298807-41925f3dad07.json"
MY_ACCOUNT="<EMAIL>"

# 2. GCP 实例配置
# 修改成自己项目ID
GCP_PROJECT_ID="kryptogo-wallet-data"
# 修改成自己的项目编号
GCP_PROJECT_NUMBER="************"
GCP_INSTANCE_NAME="solana-taiwan-node-local-$(date +%s)"
GCP_ZONE="asia-east1-b"
GCP_INSTANCE_SERVICE_ACCOUNT_EMAIL="${GCP_PROJECT_NUMBER}-<EMAIL>"

# --- 配置结束 ---

if [ ! -f "${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}" ]; then
    echo "错误：本地 JSON 密钥文件未找到于 '${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}'。" >&2
    exit 1
fi

export GOOGLE_APPLICATION_CREDENTIALS="${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}"

unset GOOGLE_APPLICATION_CREDENTIALS
echo "----------------------------------------------------"
echo ""

# ------------------------- 新增步骤开始 -------------------------
echo "--- 步骤 1.5: 创建包含密钥和主脚本的最终启动脚本 ---"
# 定义最终生成的启动脚本的路径
FINAL_STARTUP_SCRIPT_PATH="./final_bootstrap_script.sh"
# 定义密钥将被存放在 GCE 实例内部的路径
GCP_SA_KEY_PATH_IN_INSTANCE="/etc/gcp/service-account-key.json"

# 将本地密钥文件的内容读取到一个变量中
# 直接注入原始 JSON 可能会因特殊字符导致脚本错误，因此这是更稳妥的方式
SA_KEY_CONTENT=$(cat "${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}")

# 使用 heredoc 创建最终的启动脚本
# 这个脚本将会在新的 GCE 实例上以 root 用户身份执行
cat > "${FINAL_STARTUP_SCRIPT_PATH}" << EOL
#!/bin/bash
set -e
#set -u
set -o pipefail

echo "--- [Instance] 步骤 A: 注入服务账号密钥 ---"
# 在实例内创建存放密钥的目录
mkdir -p "$(dirname "${GCP_SA_KEY_PATH_IN_INSTANCE}")"

# 使用 cat 和 heredoc 将密钥内容写入到实例内的文件中
cat << 'SA_KEY_EOF' > "${GCP_SA_KEY_PATH_IN_INSTANCE}"
${SA_KEY_CONTENT}
SA_KEY_EOF

# 设置安全权限，仅拥有者可读
chmod 400 "${GCP_SA_KEY_PATH_IN_INSTANCE}"

# 为此脚本后续的所有命令设置环境变量
export GOOGLE_APPLICATION_CREDENTIALS="${GCP_SA_KEY_PATH_IN_INSTANCE}"
gcloud auth activate-service-account --key-file="${GCP_SA_KEY_PATH_IN_INSTANCE}"

# (可选) 将环境变量永久添加到系统中，以便未来通过 SSH 登录时也能生效
echo "export GOOGLE_APPLICATION_CREDENTIALS=${GCP_SA_KEY_PATH_IN_INSTANCE}" > /etc/profile.d/gcp-sa-credentials.sh

echo "密钥文件已创建于: ${GCP_SA_KEY_PATH_IN_INSTANCE}"
echo "环境变量 GOOGLE_APPLICATION_CREDENTIALS 已设置。"
echo "--- [Instance] 步骤 A 完成 ---"
echo ""
echo "--- [Instance] 步骤 B: 开始执行原始主启动脚本 ---"

EOL


cat << 'SCRIPT_EOF' >> "${FINAL_STARTUP_SCRIPT_PATH}"
# Solana 验证节点 GCP 一键式部署脚本 (针对 v0.5.8 自定义编译版本)

# Solana 安装和数据目录
SOLANA_USER="solana" # 运行 Solana 服务的用户
SOLANA_GROUP="solana" # 运行 Solana 服务的用户组
SOLANA_INSTALL_DIR="/solana/agave" # Solana 二进制文件安装目录
SOLANA_DATA_BASE_DIR="/solana"    # Solana 数据根目录
LEDGER_PATH="${SOLANA_DATA_BASE_DIR}/ledger"    # Ledger 数据路径
ACCOUNTS_PATH="${SOLANA_DATA_BASE_DIR}/accounts"  # Accounts 数据路径
KEYS_PATH="${SOLANA_DATA_BASE_DIR}/keys"        # 密钥存储路径
LOG_PATH="${SOLANA_DATA_BASE_DIR}/log"          # 日志存储路径
SOLANA_SCRIPT_DIR="${SOLANA_DATA_BASE_DIR}/bin" # Solana 启动脚本目录 (存放 validator.sh)

# Solana 版本和 GCS 下载配置 (请修改为你的 GCS Bucket 和包名)
GCS_BUCKET_NAME="gcp-solana-solution" # 替换为你的 GCS Bucket 名称
GCS_GENESIS_URL="gs://solana-statble-gcp/genesis.bin" # Mainnet Genesis 文件 GCS 路径 (如果你的版本有特定genesis, 请修改)
GCS_SYS_CONF_URL="gs://${GCS_BUCKET_NAME}/main_conf"


# 快照查找器配置
SNAPSHOT_FINDER_DIR="/opt/solana-snapshot-finder" # 快照查找工具安装目录

# --- 0. 环境检查和辅助函数 ---
set -e # 如果任何命令失败，则立即退出脚本

# 检查是否以 root 用户运行
if [ "$(id -u)" -ne 0 ]; then
  echo "错误：此脚本必须以 root 用户身份运行。" >&2
  exit 1
fi

# --- 1. 系统更新和基础软件包安装 ---
echo "正在更新系统并安装基础软件包..."
# 添加 Google Cloud 官方 GPG 密钥
curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg
# 添加 Google Cloud SDK 软件源
echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
apt-get update -y
apt-get upgrade -y
# parted 用于磁盘检查, google-cloud-sdk 用于 gsutil
apt-get install -y \
  libssl-dev libudev-dev pkg-config zlib1g-dev llvm clang cmake make \
  libncurses5-dev libncursesw5-dev xz-utils tk-dev libffi-dev liblzma-dev \
  python3-openssl git curl wget jq build-essential unzip net-tools lsof \
  iotop sysstat ncdu fail2ban htop iftop tmux libprotobuf-dev protobuf-compiler lld \
  python3-venv parted google-cloud-sdk mdadm \
  logrotate # 添加 logrotate 用于日志管理

# --- 2. 创建 Solana 用户和组 ---
echo "正在创建 Solana 用户和组..."
if ! getent group "${SOLANA_GROUP}" >/dev/null; then
    groupadd --system "${SOLANA_GROUP}"
    echo "用户组 ${SOLANA_GROUP} 已创建。"
else
    echo "用户组 ${SOLANA_GROUP} 已存在。"
fi

if ! id -u "${SOLANA_USER}" >/dev/null 2>&1; then
    useradd --system --gid "${SOLANA_GROUP}" --home-dir "${SOLANA_DATA_BASE_DIR}" --shell /bin/bash "${SOLANA_USER}"
    echo "用户 ${SOLANA_USER} 已创建。"
else
    echo "用户 ${SOLANA_USER} 已存在。"
fi
chown -R "${SOLANA_USER}:${SOLANA_GROUP}" "${SOLANA_DATA_BASE_DIR}" || true # 允许目录尚不存在

# --- 3. 磁盘设置 (针对 2 x 1.5TB Hyperdisk Extreme) ---
echo "正在设置 Solana 数据磁盘..."
mkdir -p "${LEDGER_PATH}" "${ACCOUNTS_PATH}" "${KEYS_PATH}" "${LOG_PATH}" "${SOLANA_SCRIPT_DIR}"
chown -R "${SOLANA_USER}:${SOLANA_GROUP}" "${SOLANA_DATA_BASE_DIR}"

# 格式化并挂载 Ledger 磁盘
SOLANA_DISK_ACCOUNTS=/dev/md0
SOLANA_DISK_LEDGER=/dev/md1
echo "正在创建并配置 RAID 0 阵列..."

# 创建 RAID 0 阵列用于 Accounts (5个磁盘)
echo "正在创建 Accounts RAID 0 阵列 (md0)..."
mdadm --create /dev/md0 --level=0 --raid-devices=5 \
    /dev/nvme1n1 /dev/nvme2n1 /dev/nvme3n1 /dev/nvme4n1 /dev/nvme5n1

# 创建 RAID 0 阵列用于 Ledger (5个磁盘，128K chunk)
echo "正在创建 Ledger RAID 0 阵列 (md1)..."
mdadm --create /dev/md1 --level=0 --raid-devices=5 --chunk=128K \
    /dev/nvme6n1 /dev/nvme7n1 /dev/nvme8n1 /dev/nvme9n1 /dev/nvme10n1

# 格式化并挂载 Accounts 分区
echo "正在格式化并挂载 Accounts 分区..."
mkfs.ext4 -F -m 0 -E lazy_itable_init=0,lazy_journal_init=0 /dev/md0
mount -o noatime,nodiratime /dev/md0 "${ACCOUNTS_PATH}"
sed -i '\|'"${ACCOUNTS_PATH}"'|d' /etc/fstab
echo "/dev/md0 ${ACCOUNTS_PATH} ext4 defaults,noatime,nodiratime 0 2" >> /etc/fstab

# 格式化并挂载 Ledger 分区
echo "正在格式化并挂载 Ledger 分区..."
mkfs.xfs -f -d su=128k,sw=5 -i size=512 -l version=2 /dev/md1
mount -o noatime,nodiratime,logbufs=8,logbsize=256k /dev/md1 "${LEDGER_PATH}"
sed -i '\|'"${LEDGER_PATH}"'|d' /etc/fstab
echo "/dev/md1 ${LEDGER_PATH} xfs defaults,noatime,nodiratime,logbufs=8,logbsize=256k 0 2" >> /etc/fstab

# 设置权限
chown "${SOLANA_USER}:${SOLANA_GROUP}" "${ACCOUNTS_PATH}" "${LEDGER_PATH}"
chmod 700 "${ACCOUNTS_PATH}" "${LEDGER_PATH}"

echo "RAID 阵列创建和挂载完成。"

mount -a # 重新挂载 /etc/fstab 中的所有文件系统以确保生效
echo "磁盘挂载完毕。"

# --- 4. 系统参数调整 (sysctl & limits) ---
echo "正在应用系统参数调整..."
gcloud storage cp ${GCS_SYS_CONF_URL}/90-solana.conf   /etc/sysctl.d
sysctl -p /etc/sysctl.d/90-solana.conf

gcloud storage cp ${GCS_SYS_CONF_URL}/90-solana-nofiles.conf /etc/security/limits.d

# 设置 systemd 的 DefaultLimitNOFILE
mkdir -p /etc/systemd/system.conf.d
gcloud storage cp ${GCS_SYS_CONF_URL}/limits.conf /etc/systemd/system.conf.d
systemctl daemon-reexec # 应用 systemd 更改

# --- 5. 安装 Solana 二进制文件 ---
cd /solana
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source "/root/.cargo/env"
git clone https://github.com/anza-xyz/agave
cd agave && git checkout v2.2.15
export PATH="/root/.cargo/bin:$PATH"
./scripts/cargo-install-all.sh .

cd /solana
git clone https://github.com/rpcpool/yellowstone-grpc
cd yellowstone-grpc && git checkout v7.0.0+solana.2.2.15
cargo build --release
echo '{  "libpath": "/solana/yellowstone-grpc/target/release/libyellowstone_grpc_geyser.so",  "log": {    "level": "info"  },  "tokio": {    "worker_threads": 8,    "affinity": "0-1,12-13"  },  "grpc": {    "address": "0.0.0.0:10001",    "compression": {      "accept": ["gzip", "zstd"],      "send": ["gzip", "zstd"]    },    "server_http2_adaptive_window": null,    "server_http2_keepalive_interval": null,    "server_http2_keepalive_timeout": null,    "server_initial_connection_window_size": null,    "server_initial_stream_window_size": null,    "max_decoding_message_size": "4_194_304",    "snapshot_plugin_channel_capacity": null,    "snapshot_client_channel_capacity": "50_000_000",    "channel_capacity": "100_000",    "unary_concurrency_limit": 100,    "unary_disabled": false,    "x_token": "isUtvkn8knQ7Z6C6Zw4rymgix2pY",    "replay_stored_slots": 0,    "filter_name_size_limit": 128,    "filter_names_size_limit": 4096,    "filter_names_cleanup_interval": "1s",    "filter_limits": {      "accounts": {        "max": 1,        "any": false,        "account_max": 10,        "account_reject": [],        "owner_max": 10,        "owner_reject": ["11111111111111111111111111111111"],        "data_slice_max": 2      },      "slots": {        "max": 1      },      "transactions": {        "max": 1,        "any": false,        "account_include_max": 10,        "account_include_reject": [        ],        "account_exclude_max": 10,        "account_required_max": 10      },      "transactions_status": {        "max": 1,        "any": false,        "account_include_max": 10,        "account_include_reject": [        ],        "account_exclude_max": 10,        "account_required_max": 10      },      "blocks": {        "max": 1,        "account_include_max": 10,        "account_include_any": false,        "account_include_reject": [        ],        "include_transactions": true,        "include_accounts": false,        "include_entries": false      },      "blocks_meta": {        "max": 1      },      "entries": {        "max": 1      }    }  },  "prometheus": {    "address": "0.0.0.0:8999"  }}' > /solana/yellowstone-grpc/yellowstone-grpc-geyser/config.json

chown -R "${SOLANA_USER}:${SOLANA_GROUP}" "${SOLANA_INSTALL_DIR}"
chmod +x ${SOLANA_INSTALL_DIR}/bin/*

# 定义 Solana 二进制文件路径
SOLANA_VALIDATOR_BIN="${SOLANA_INSTALL_DIR}/bin/agave-validator" # 确保这是你包中验证器二进制文件的正确名称

SOLANA_KEYGEN_BIN="${SOLANA_INSTALL_DIR}/bin/solana-keygen"

if [ ! -f "${SOLANA_VALIDATOR_BIN}" ]; then
    echo "错误: 未找到 Solana 验证器二进制文件: ${SOLANA_VALIDATOR_BIN}"
    echo "请检查你的 GCS 包 (${SOLANA_PACKAGE_NAME}) 内容和解压路径。"
    exit 1
fi

# --- 6. Solana 快照下载 ---
echo "正在设置 Solana 快照查找器..."
if [ -d "${SNAPSHOT_FINDER_DIR}" ]; then
  echo "快照查找器目录已存在，先删除旧的。"
  rm -rf "${SNAPSHOT_FINDER_DIR}"
fi


# 下载finder 代码
gcloud storage cp  gs://gcp-solana-solution/solana-snapshot-finder/solana-snapshot-finder.zip /opt
unzip /opt/solana-snapshot-finder.zip -d /opt
cd "${SNAPSHOT_FINDER_DIR}"
python3 -m venv venv
source ./venv/bin/activate
echo "正在安装快照查找器依赖..."
./venv/bin/pip3 install -r requirements.txt
deactivate
cd "$HOME" # 返回主目录

echo "尝试查找并下载最新的快照到 ${LEDGER_PATH}..."
# 确保 solana 用户有权写入 ${LEDGER_PATH} (已在磁盘设置部分完成)
# 注意: 如果 snapshot-finder.py 需要特定配置 (例如，通过环境变量或配置文件指定你的 HTTPS 端点),
# 你需要在这里或其内部进行相应设置。
sudo -u "${SOLANA_USER}" bash -c " \
  cd '${SNAPSHOT_FINDER_DIR}' && \
  source ./venv/bin/activate && \
  python3 ./snapshot-finder.py --snapshot_path '${LEDGER_PATH}' && \
  deactivate"
if [ $? -ne 0 ]; then
  echo "警告: 快照下载可能失败或未找到。节点将尝试从创世块同步或等待手动放置快照。"
else
  echo "快照下载（或尝试）完成。"
fi

echo "正在下载 Genesis Block 到 ${LEDGER_PATH}..."
gsutil cp "${GCS_GENESIS_URL}" "${LEDGER_PATH}/genesis.bin"
if [ $? -ne 0 ]; then
  echo "警告: 从 GCS 下载 Genesis Block 失败。请确保 ${GCS_GENESIS_URL} 可访问。"
  echo "如果你的节点不需要从这个特定路径下载 genesis.bin (例如，它会自行获取), 可以忽略此警告。"
else
  chown "${SOLANA_USER}:${SOLANA_GROUP}" "${LEDGER_PATH}/genesis.bin"
  echo "Genesis Block 下载完成。"
fi


# --- 7. Solana 配置 ---
echo "正在配置 Solana 验证节点..."
# 创建身份密钥 (如果不存在)
VALIDATOR_KEYPAIR_FILE="${KEYS_PATH}/validator-keypair.json"
if [ ! -f "${VALIDATOR_KEYPAIR_FILE}" ]; then
  echo "正在生成新的验证节点身份密钥..."
  sudo -u "${SOLANA_USER}" "${SOLANA_KEYGEN_BIN}" new --no-bip39-passphrase -o "${VALIDATOR_KEYPAIR_FILE}"
else
  echo "验证节点身份密钥 ${VALIDATOR_KEYPAIR_FILE} 已存在。"
fi
# 对于投票账户等其他密钥，如果需要，可以在此类似地生成:
# sudo -u "${SOLANA_USER}" "${SOLANA_KEYGEN_BIN}" new --no-bip39-passphrase -o "${KEYS_PATH}/vote-account-keypair.json"
# sudo -u "${SOLANA_USER}" "${SOLANA_KEYGEN_BIN}" new --no-bip39-passphrase -o "${KEYS_PATH}/authorized-withdrawer-keypair.json"

# 创建验证节点启动脚本
echo "正在下载验证节点启动脚本: ${SOLANA_SCRIPT_DIR}/validator.sh"

cat > "${SOLANA_SCRIPT_DIR}/validator.sh" << EOF_VALIDATOR_SH
#!/bin/bash
set -e
SOLANA_VALIDATOR_BIN=/solana/agave/bin/agave-validator
echo "Solana Validator (${SOLANA_VALIDATOR_BIN}) 正在启动..."
$SOLANA_VALIDATOR_BIN --identity /solana/keys/validator-keypair.json  --known-validator 9W3QTgBhkU4Bwg6cwnDJo6eGZ9BtZafSdu1Lo9JmWws7  --known-validator 82vucuWCTTQEz6nYe3VetnL3pJYBrfDF2gDAjec9sPUy  --known-validator B4dn3WWS95M4qNXaR5NTdkNzhzvTZVqC13E3eLrWhXLa  --known-validator EBk678aQvc3cUkfGyoehfw21JQfJXjmWuBeopYc89RSV  --known-validator 7ZjHeeYEesmBs4N6aDvCQimKdtJX2bs5boXpJmpG2bZJ  --known-validator GX6kCVtpvFTGsedV72nK5K6VzY1bTCvqFmrtHkuZHGsX  --known-validator 5d9Mdc2Zk8as8GL1AxQeXxv5htBBvC5bjfmsXC7UUWwG  --known-validator DMGGRvdRwhqrpo7LLnUzSkivaTNoGCsU4NtnL6czYKYG  --known-validator D4r6Rcua2L7nHHhdaiZe2k2bTfPg2WQqcNYpG6bugvCG  --skip-poh-verify  --experimental-poh-pinned-cpu-core 4  --poh-hashes-per-batch 512  --only-known-rpc  --no-voting  --private-rpc  --log /solana/log/solana-validator.log  --ledger /solana/ledger  --accounts /solana/accounts  --rpc-port 8899  --rpc-bind-address 0.0.0.0  --dynamic-port-range 8000-8020  --entrypoint entrypoint.mainnet-beta.solana.com:8001  --entrypoint entrypoint2.mainnet-beta.solana.com:8001  --entrypoint entrypoint3.mainnet-beta.solana.com:8001  --entrypoint entrypoint4.mainnet-beta.solana.com:8001  --entrypoint entrypoint5.mainnet-beta.solana.com:8001  --expected-genesis-hash 5eykt4UsFv8P8NJdTREpY1vzqKqZKvdpKuc147dw2N9d  --wal-recovery-mode skip_any_corrupted_record  --limit-ledger-size  --disable-banking-trace  --no-genesis-fetch  --skip-preflight-health-check  --tpu-connection-pool-size 128  --rpc-max-request-body-size *********  --no-os-network-limits-test  --rpc-blocking-threads 8  --rpc-threads 8  --snapshot-interval-slots 0  --full-snapshot-interval-slots 18000  --snapshot-packager-niceness-adjustment 20  --minimal-snapshot-download-speed *********  --block-production-method central-scheduler-greedy  --no-os-network-stats-reporting  --no-os-memory-stats-reporting  --no-os-cpu-stats-reporting  --no-os-disk-stats-reporting  --no-poh-speed-test  --no-port-check  --no-snapshot-fetch  --accounts-db-hash-threads 8  --accounts-index-flush-threads 8  --disable-accounts-disk-index  --replay-forks-threads 4  --tvu-receive-threads 4  --maximum-local-snapshot-age 1000  --tpu-use-quic  --no-skip-initial-accounts-db-clean  --geyser-plugin-config /solana/yellowstone-grpc/yellowstone-grpc-geyser/config.json
echo "Solana Validator 进程已退出."
EOF_VALIDATOR_SH

chmod +x "${SOLANA_SCRIPT_DIR}/validator.sh"
chown "${SOLANA_USER}:${SOLANA_GROUP}" "${SOLANA_SCRIPT_DIR}/validator.sh"

# --- 8. 设置 Systemd 服务 ---
echo "正在设置 Solana 验证节点 Systemd 服务..."
gcloud storage cp gs://gcp-solana-solution/main_scripts/solana-validator.service  /etc/systemd/system/solana-validator.service

# --- 9. 设置日志轮转 (logrotate) ---
echo "正在为 Solana 验证节点设置日志轮转..."
cat > /etc/logrotate.d/solana-validator << EOF_LOGROTATE
/solana/log/solana-validator.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
    create 640 solana solana
}
EOF_LOGROTATE

# --- 10. 启动 Solana 服务 ---
echo "重新加载 Systemd 守护进程并启用 Solana 服务..."
systemctl daemon-reload
systemctl enable solana-validator.service

echo "正在启动 Solana 验证节点服务..."
# 在启动前清理一次旧日志，以防万一
rm -f "${LOG_PATH}/solana-validator.log"
chown "${SOLANA_USER}:${SOLANA_GROUP}" "${LOG_PATH}" # 确保日志目录权限正确

# 启动服务
systemctl start solana-validator.service

echo "=================================================================="
echo "Solana 验证节点部署脚本执行完毕。"
echo "服务状态检查: systemctl status solana-validator.service"
echo "查看节点日志: sudo journalctl -u solana-validator -f"
echo "或: sudo -u ${SOLANA_USER} tail -f ${LOG_PATH}/solana-validator.log"
echo "=================================================================="
echo "重要提示:"
echo "1. 请将脚本中的 'YOUR_GCS_BUCKET_NAME_HERE' 替换为你的实际 GCS Bucket 名称。"
echo "2. 确保你的 Solana 二进制包 '${SOLANA_PACKAGE_NAME}' 已上传到 GCS Bucket 的根目录，"
echo "   并且其内部结构与脚本中的 tar解压命令兼容 (通常解压后直接是二进制文件或包含一个顶层目录需要用 --strip-components=1 去除)。"
echo "3. 如果你的自定义版本 '${SOLANA_VERSION}' 需要特定的 genesis.bin, 请更新 GCS_GENESIS_URL。"
echo "4. 检查 validator.sh 中的各项参数是否适用于你的 v0.5.8 版本和网络环境。"
echo "5. 如果你的 '就近Validator节点发现服务' 或 'Snapshot发现服务HTTPS端点' 需要特定集成方式，"
echo "   请相应修改 validator.sh 中的节点发现逻辑或快照下载部分。"
echo "=================================================================="

exit 0
SCRIPT_EOF

echo "最终的启动脚本已在 '${FINAL_STARTUP_SCRIPT_PATH}' 创建成功。"
echo "它包含了密钥注入逻辑和原始的 Solana 部署逻辑。"
echo "----------------------------------------------------"
echo ""
# ------------------------- 新增步骤结束 -------------------------

echo "--- 步骤 2: 创建 GCP Compute Engine 实例并应用最终的启动脚本 ---"
echo "实例名称: ${GCP_INSTANCE_NAME}"
echo "项目 ID: ${GCP_PROJECT_ID}"
echo "区域: ${GCP_ZONE}"
echo "将使用最终启动脚本: ${FINAL_STARTUP_SCRIPT_PATH}"

gcloud compute instances create "${GCP_INSTANCE_NAME}" \
    --project="${GCP_PROJECT_ID}" \
    --zone="${GCP_ZONE}" \
    --machine-type=c4a-highmem-48-lssd \
    --network-interface=network-tier=PREMIUM \
    --maintenance-policy=MIGRATE \
    --service-account="${GCP_INSTANCE_SERVICE_ACCOUNT_EMAIL}" \
    --scopes=https://www.googleapis.com/auth/cloud-platform \
    --image-family=ubuntu-2404-lts-arm64 \
    --image-project=ubuntu-os-cloud \
    --boot-disk-size=200GB \
    --boot-disk-provisioned-iops=10000 \
    --boot-disk-provisioned-throughput=290 \
    --boot-disk-type=hyperdisk-balanced \
    --shielded-vtpm \
    --shielded-integrity-monitoring \
    --metadata-from-file startup-script="${FINAL_STARTUP_SCRIPT_PATH}" \
    --account="${MY_ACCOUNT}"

echo ""
echo "GCP 实例 '${GCP_INSTANCE_NAME}' 创建命令已提交。"
echo "实例启动后将优先注入密钥，然后执行主脚本内容。"
# ... (后续的 echo 提示信息保持不变) ...
echo "  gcloud compute instances get-serial-port-output ${GCP_INSTANCE_NAME} --project ${GCP_PROJECT_ID} --zone ${GCP_ZONE} --port 1"
echo "----------------------------------------------------"
echo ""

echo "=== GCP Solana 节点部署脚本执行完毕 ==="
echo "提示: 本地生成的最终启动脚本位于 '${FINAL_STARTUP_SCRIPT_PATH}'。你可以根据需要保留或删除它。"

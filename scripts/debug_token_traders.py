#!/usr/bin/env python3
import sys
import json
import time
import os
import traceback
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hai<PERSON>

def log(message, debug=False):
    """Print debug messages to stderr if debug mode is enabled"""
    if debug:
        print(message, file=sys.stderr)

def debug_token_traders(token_addresses, keep_browser_open=False, clean_output=False):
    """Debug token traders using undetected-chromedriver"""
    if isinstance(token_addresses, str):
        token_addresses = [token_addresses]
    
    log(f"Starting debug version of token traders script for {len(token_addresses)} tokens...", not clean_output)
    
    try:
        # Configure Chrome options
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        display = os.environ.get("DISPLAY", None)
        if display:
            options.add_argument(f"--display={display}")

        # Enable performance logging
        options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        # Create a new instance of the undetected Chrome driver
        driver = uc.Chrome(headless=False, use_subprocess=True, options=options)
        
        # Set page load timeout
        driver.set_page_load_timeout(30)
        
        # Process each token address
        all_results = {}
        
        for token_address in token_addresses:
            log(f"Processing token: {token_address}", not clean_output)
            
            try:
                # Navigate to the URL
                log(f"Navigating to gmgn.ai/token/{token_address}...", not clean_output)
                driver.get(f"https://gmgn.ai/sol/token/{token_address}")
                
                # Wait for the page to load
                log("Waiting for page to load...", not clean_output)
                time.sleep(3)
                
                # Find and click the close icon (second element with class css-pt4g3d)
                log("Looking for close icon...", not clean_output)
                try:
                    # Find the SVG element with cursor-pointer class
                    close_icon = driver.find_element(By.CSS_SELECTOR, ".css-gg4vpm svg.cursor-pointer")
                    if close_icon:
                        log(f"Found close icon: {close_icon.get_attribute('outerHTML')}", not clean_output)
                        
                        # Create and dispatch a click event using JavaScript
                        driver.execute_script("""
                            const clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            arguments[0].dispatchEvent(clickEvent);
                        """, close_icon)
                        log("Clicked close icon using MouseEvent", not clean_output)
                    else:
                        log("Close icon not found", not clean_output)
                except Exception as e:
                    log(f"Error clicking close icon: {e}", not clean_output)

                # Instead of clicking Next buttons multiple times then Finish button,
                # directly click the modal background to dismiss it
                log("Looking for modal background to dismiss...", not clean_output)
                try:
                    driver.execute_script("document.querySelector('.pi-modal-wrap').click();")
                    log("Clicked modal background using JavaScript", not clean_output)
                    
                    # Wait a moment for any animations or page changes
                    time.sleep(2)
                except Exception as e:
                    log(f"Error clicking modal background: {e}", not clean_output)
                
                # Find and click the traders tab
                log("Looking for traders tab...", not clean_output)
                try:
                    # Find the traders tab
                    traders_tab = WebDriverWait(driver, 1).until(
                        EC.presence_of_element_located((By.XPATH, "//*[text()='Traders']"))
                    )
                    log("Traders tab found", not clean_output)
                    
                    # Click the traders tab using JavaScript
                    log("Clicking traders tab with JavaScript...", not clean_output)
                    driver.execute_script("""
                        const tradersElement = Array.from(document.querySelectorAll('*')).filter(el => el.textContent.trim() === 'Traders')[0];
                        if (tradersElement) {
                            tradersElement.click();
                            return true;
                        } else {
                            console.error('Traders element not found');
                            return false;
                        }
                    """)
                    
                    # Wait for the network request to complete
                    log("Waiting for network request...", not clean_output)
                    time.sleep(3)
                    
                    # Find and click the Pnl tab
                    log("Looking for Pnl tab...", not clean_output)
                    try:
                        # Find the Pnl tab
                        pnl_tab = WebDriverWait(driver, 1).until(
                            EC.presence_of_element_located((By.XPATH, "//*[text()='Pnl']"))
                        )
                        log("Pnl tab found", not clean_output)
                        
                        # Click the Pnl tab using JavaScript
                        log("Clicking Pnl tab with JavaScript...", not clean_output)
                        driver.execute_script("""
                            // Find all elements with text 'Pnl'
                            const pnlElements = Array.from(document.querySelectorAll('*')).filter(el => el.textContent.trim() === 'Pnl');
                            
                            // Look for the specific element with the classes we want
                            const targetPnlElement = pnlElements.find(el => 
                                el.classList.contains('TableMultipleSort_item__r8sgn') && 
                                el.classList.contains('cursor-pointer') && 
                                el.classList.contains('css-lj6muc')
                            );
                            
                            // If we found the specific element, click it
                            if (targetPnlElement) {
                                targetPnlElement.click();
                                return true;
                            } 
                            // Fallback to the second element if it exists (as per user's preference)
                            else if (pnlElements.length > 1) {
                                pnlElements[1].click();
                                return true;
                            }
                            // Fallback to the first element if only one exists
                            else if (pnlElements.length === 1) {
                                pnlElements[0].click();
                                return true;
                            } else {
                                console.error('Pnl element not found');
                                return false;
                            }
                        """)
                        
                        # Wait for the network request to complete
                        log("Waiting for network request...", not clean_output)
                        time.sleep(3)
                        
                        # Get the response data
                        log("Extracting response data...", not clean_output)
                        
                        # Approach 1: Try to extract data directly from the page
                        log("Trying to extract data directly from the page...", not clean_output)
                        try:
                            # Try to get data from window.__NEXT_DATA__ or similar global variables
                            page_data = driver.execute_script(r"""
                                // Try different possible locations of the data
                                if (window.__NEXT_DATA__) {
                                    return window.__NEXT_DATA__;
                                } else if (window.__INITIAL_STATE__) {
                                    return window.__INITIAL_STATE__;
                                } else if (window.__PRELOADED_STATE__) {
                                    return window.__PRELOADED_STATE__;
                                } else {
                                    // Try to find the data in any script tags
                                    const scripts = document.querySelectorAll('script');
                                    for (const script of scripts) {
                                        if (script.textContent.includes('"traders":')) {
                                            try {
                                                // Try to extract JSON from the script content
                                                const match = script.textContent.match(/\{.*"traders":.*\}/s);
                                                if (match) {
                                                    return JSON.parse(match[0]);
                                                }
                                            } catch (e) {
                                                console.error('Error parsing script content:', e);
                                            }
                                        }
                                    }
                                    return null;
                                }
                            """)
                            
                            if page_data:
                                log("Successfully extracted data from page", not clean_output)
                                if not clean_output:
                                    print(json.dumps(page_data))
                        except Exception as e:
                            log(f"Error extracting data from page: {e}", not clean_output)
                        
                        # Approach 2: Try to find the data in the page HTML
                        log("Trying to find data in page HTML...", not clean_output)
                        try:
                            # Look for script tags that might contain the data
                            scripts = driver.find_elements(By.TAG_NAME, "script")
                            for script in scripts:
                                try:
                                    content = script.get_attribute("innerHTML")
                                    if content and '"traders":' in content:
                                        log("Found script with traders data", not clean_output)
                                        # Try to extract JSON from the script content
                                        import re
                                        match = re.search(r'\{.*"traders":.*\}', content, re.DOTALL)
                                        if match:
                                            data = json.loads(match.group(0))
                                            if not clean_output:
                                                print(json.dumps(data))
                                            break
                                except Exception as e:
                                    log(f"Error processing script tag: {e}", not clean_output)
                        except Exception as e:
                            log(f"Error searching page HTML: {e}", not clean_output)
                        
                        # Approach 3: Try to use CDP to capture network traffic (as a fallback)
                        log("Trying to capture network traffic using CDP...", not clean_output)
                        try:
                            # Enable network interception
                            driver.execute_cdp_cmd('Network.enable', {})
                            
                            # Get all network requests
                            logs = driver.get_log('performance')
                            response_data = None
                            
                            for entry in logs:
                                if 'message' in entry:
                                    message = json.loads(entry['message'])
                                    if 'message' in message and 'method' in message['message']:
                                        if message['message']['method'] == 'Network.responseReceived':
                                            params = message['message']['params']
                                            request_id = params['requestId']
                                            url = params['response']['url']
                                            
                                            # Look for the correct URL pattern
                                            if '/vas/api/v1/token_traders/' in url:
                                                log(f"Found matching request: {url}", not clean_output)
                                                try:
                                                    # Try to get response body
                                                    response = driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': request_id})
                                                    if 'body' in response:
                                                        response_data = json.loads(response['body'])
                                                        log("Successfully captured response data", not clean_output)
                                                        # if clean_output:
                                                        #     print(json.dumps(response_data))
                                                        # else:
                                                        #     log(f"Response data: {json.dumps(response_data)}", not clean_output)
                                                except Exception as e:
                                                    log(f"Error getting response body: {e}", not clean_output)
                                                    # Try alternative CDP command
                                                    try:
                                                        response = driver.execute_cdp_cmd('Network.getResponseById', {'requestId': request_id})
                                                        log(f"Alternative response data: {json.dumps(response)}", not clean_output)
                                                    except Exception as e2:
                                                        log(f"Error with alternative CDP command: {e2}", not clean_output)
                        except Exception as e:
                            log(f"Error capturing network traffic: {e}", not clean_output)
                        
                        if not response_data:
                            log("Could not find response data in network traffic", not clean_output)
                    except Exception as e:
                        log(f"Error finding Pnl tab: {e}", not clean_output)
                        log("Available text elements:", not clean_output)
                        elements = driver.find_elements(By.XPATH, "//*[text()]")
                        for element in elements:
                            try:
                                text = element.text
                                if text:
                                    log(f"Text element: {text}", not clean_output)
                            except:
                                pass
                
                except (TimeoutException, NoSuchElementException) as e:
                    log(f"Error finding traders tab: {e}", not clean_output)
                    log("Available text elements:", not clean_output)
                    elements = driver.find_elements(By.XPATH, "//*[text()]")
                    for element in elements:
                        try:
                            text = element.text
                            if text:
                                log(f"Text element: {text}", not clean_output)
                        except:
                            pass
                
                # Store the result for this token
                all_results[token_address] = response_data
                
            except Exception as e:
                log(f"Error processing token {token_address}: {e}", not clean_output)
                all_results[token_address] = None
        
        # Keep the browser open for inspection if requested
        if keep_browser_open:
            log("Debug session complete. Browser will remain open for inspection.", not clean_output)
            log("Press Ctrl+C to close the browser and exit.", not clean_output)
            
            # Keep the script running
            while True:
                time.sleep(3)
        else:
            log("Debug session complete. Closing browser.", not clean_output)
            driver.quit()
            
    except Exception as e:
        log(f"Error in debug token traders script: {e}", not clean_output)
        log(traceback.format_exc(), not clean_output)
        
        # Keep the browser open for inspection in case of error if requested
        if keep_browser_open:
            log("Browser will remain open for inspection due to error.", not clean_output)
            # Keep the browser open for inspection in case of error
            while True:
                time.sleep(3)
        else:
            driver.quit()
    print(json.dumps(all_results))
    return all_results

if __name__ == "__main__":
    # Get token address from command line arguments
    if len(sys.argv) < 2:
        print("Usage: python3 debug_token_traders.py <token_address1> [token_address2 ...] [keep_open] [clean]")
        print("  <token_address1> [token_address2 ...] - One or more token addresses to analyze")
        print("  [keep_open] - Optional: 'keep_open' to keep the browser open after completion")
        print("  [clean] - Optional: 'clean' to output only the JSON data")
        sys.exit(1)
    
    # Parse command line arguments
    token_addresses = []
    keep_browser_open = False
    clean_output = False
    
    for arg in sys.argv[1:]:
        if arg.lower() == 'keep_open':
            keep_browser_open = True
        elif arg.lower() == 'clean':
            clean_output = True
        else:
            token_addresses.append(arg)
    
    if not token_addresses:
        print("Error: No token addresses provided")
        sys.exit(1)
    
    debug_token_traders(token_addresses, keep_browser_open, clean_output) 
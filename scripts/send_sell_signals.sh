#!/bin/bash

# List of wallet addresses
ADDRESSES=(
    "GFAb9ARdro6dwcYmcXsynubq5PiHHKjHifDH6NiTpump"
)

# API base URLs
API_URLS=(
    "https://api-68779486420.asia-east1.run.app"
    "https://api-366580911590.asia-east1.run.app"
    "https://api-307061773427.asia-east1.run.app"
)

# Telegram link
TELEGRAM_LINK="https://t.me/pump_hound_signal"

# Function to send a request for a specific address and API URL
send_request() {
    local address=$1
    local api_url=$2
    
    echo "Sending request for address: $address to API: $api_url"
    
    curl -X POST "$api_url/_v/token_signal/sell" \
         -H "Content-Type: application/json" \
         -d "{\"token_address\":\"$address\",\"telegram_link\":\"$TELEGRAM_LINK\"}"
    
    echo -e "\n"
    
    # Add a small delay between requests to avoid rate limiting
    sleep 1
}

# Main loop to process all addresses and API URLs
for address in "${ADDRESSES[@]}"; do
    for api_url in "${API_URLS[@]}"; do
        send_request "$address" "$api_url"
    done
done

echo "All requests completed!" 
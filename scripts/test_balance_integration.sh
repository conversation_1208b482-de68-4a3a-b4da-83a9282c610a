#!/bin/bash

# Test script for the token balance integration
# This script tests the integration between the Rust server and Go function

set -e

echo "🚀 Testing Token Balance Integration"
echo "=================================="

# Configuration
SERVER_URL="http://localhost:8088"
TEST_TOKEN="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  # USDC token mint

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Function to check if server is running
check_server() {
    echo "Checking if server is running..."
    if curl -s "$SERVER_URL/health" > /dev/null; then
        print_status "Server is running at $SERVER_URL"
    else
        print_error "Server is not running at $SERVER_URL"
        echo "Please start the server with: cargo run --bin token_analysis_server"
        exit 1
    fi
}

# Function to test basic endpoint
test_basic_endpoint() {
    echo ""
    echo "Testing basic endpoint..."
    echo "========================"
    
    local response=$(curl -s -X POST "$SERVER_URL/analyze/suspect_wallets_balances" \
        -H "Content-Type: application/json" \
        -d "{\"token\": \"$TEST_TOKEN\"}")
    
    if echo "$response" | jq -e '.status == "success"' > /dev/null 2>&1; then
        print_status "Basic endpoint test passed"
        
        # Extract and display results
        local wallet_count=$(echo "$response" | jq '.data.suspect_wallets | length')
        local balance_points=$(echo "$response" | jq '.data.balance_history.balance_points | length')
        
        echo "  - Found $wallet_count suspect wallets"
        echo "  - Generated $balance_points balance points"
        
        # Show first few wallets
        echo "  - First few suspect wallets:"
        echo "$response" | jq -r '.data.suspect_wallets[:3][]' | sed 's/^/    /'
        
    else
        print_error "Basic endpoint test failed"
        echo "Response: $response"
        return 1
    fi
}

# Function to test enhanced endpoint
test_enhanced_endpoint() {
    echo ""
    echo "Testing enhanced endpoint..."
    echo "============================"
    
    local response=$(curl -s -X POST "$SERVER_URL/analyze/suspect_wallets_balances_enhanced" \
        -H "Content-Type: application/json" \
        -d "{
            \"token\": \"$TEST_TOKEN\",
            \"from_slot\": 250000000,
            \"rpc_url\": \"https://api.mainnet-beta.solana.com\"
        }")
    
    if echo "$response" | jq -e '.status == "success"' > /dev/null 2>&1; then
        print_status "Enhanced endpoint test passed"
        
        # Extract and display results
        local wallet_count=$(echo "$response" | jq '.data.suspect_wallets | length')
        local balance_points=$(echo "$response" | jq '.data.balance_history.balance_points | length')
        local from_slot=$(echo "$response" | jq '.data.balance_history.from_slot')
        
        echo "  - Found $wallet_count suspect wallets"
        echo "  - Generated $balance_points balance points"
        echo "  - Starting from slot: $from_slot"
        
        # Show sample balance point
        echo "  - Sample balance point:"
        echo "$response" | jq '.data.balance_history.balance_points[0]' | sed 's/^/    /'
        
    else
        print_error "Enhanced endpoint test failed"
        echo "Response: $response"
        return 1
    fi
}

# Function to test error handling
test_error_handling() {
    echo ""
    echo "Testing error handling..."
    echo "========================"
    
    # Test with invalid token
    local response=$(curl -s -X POST "$SERVER_URL/analyze/suspect_wallets_balances" \
        -H "Content-Type: application/json" \
        -d '{"token": "invalid_token"}')
    
    if echo "$response" | jq -e '.status == "error"' > /dev/null 2>&1; then
        print_status "Error handling test passed"
        local error_msg=$(echo "$response" | jq -r '.message')
        echo "  - Error message: $error_msg"
    else
        print_warning "Error handling test unexpected result"
        echo "Response: $response"
    fi
}

# Function to check prerequisites
check_prerequisites() {
    echo "Checking prerequisites..."
    echo "========================"
    
    # Check if jq is installed
    if command -v jq &> /dev/null; then
        print_status "jq is installed"
    else
        print_error "jq is not installed. Please install it: brew install jq"
        exit 1
    fi
    
    # Check if curl is installed
    if command -v curl &> /dev/null; then
        print_status "curl is installed"
    else
        print_error "curl is not installed"
        exit 1
    fi
    
    # Check if Go binary exists
    local go_binary="${GO_TOKEN_BALANCE_BINARY:-/Users/<USER>/git/kg-solana-data/data-writer/cmd/token-balance/token-balance}"
    if [ -f "$go_binary" ]; then
        print_status "Go binary found at $go_binary"
    else
        print_warning "Go binary not found at $go_binary"
        echo "  Please build it with: cd /path/to/kg-solana-data/data-writer/cmd/token-balance && go build -o token-balance ."
    fi
}

# Main execution
main() {
    check_prerequisites
    echo ""
    check_server
    
    # Run tests
    if test_basic_endpoint && test_enhanced_endpoint; then
        echo ""
        print_status "All tests passed! 🎉"
        echo ""
        echo "Integration is working correctly."
        echo "You can now use the API endpoints to get suspect wallet balance history."
    else
        echo ""
        print_error "Some tests failed! 😞"
        exit 1
    fi
    
    # Optional: test error handling
    test_error_handling
    
    echo ""
    echo "Test completed!"
}

# Run main function
main "$@"

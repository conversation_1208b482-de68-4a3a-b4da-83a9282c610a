#!/bin/bash

# Test script for the token balance integration
# This script tests the integration between the Rust server and Go function

set -e

echo "🚀 Testing Token Balance Integration"
echo "=================================="

export DB_USER=gary
export DB_PASSWORD=6YYz33Dg2YPCCxrb

# Configuration
SERVER_URL="http://localhost:8088"
TEST_TOKEN="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" # USDC token mint

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Function to check if server is running
check_server() {
    echo "Checking if server is running..."
    if curl -s "$SERVER_URL/health" >/dev/null; then
        print_status "Server is running at $SERVER_URL"
    else
        print_error "Server is not running at $SERVER_URL"
        echo "Please start the server with: cargo run --bin token_analysis_server"
        exit 1
    fi
}

# Function to test basic endpoint
test_basic_endpoint() {
    echo ""
    echo "Testing basic endpoint..."
    echo "========================"

    local response=$(curl -s -X POST "$SERVER_URL/analyze/suspect_wallets_balances" \
        -H "Content-Type: application/json" \
        -d "{\"token\": \"$TEST_TOKEN\"}")

    echo "Response: $response"

    if echo "$response" | jq -e '.status == "success"' >/dev/null 2>&1; then
        print_status "Basic endpoint test passed"
    else
        print_error "Basic endpoint test failed"
        return 1
    fi
}

# Main execution
main() {
    check_server
    test_basic_endpoint
    echo ""
    echo "Test completed!"
}

# Run main function
main "$@"

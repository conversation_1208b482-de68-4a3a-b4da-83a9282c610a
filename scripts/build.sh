#!/bin/bash

set -e

REPO=asia-east1-docker.pkg.dev
PROJECT_ID_DEV="kryptogo-wallet-app-dev"
DOCKER_REPO_DEV="$REPO/$PROJECT_ID_DEV/solana-bot"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --version=*)
      VERSION="${1#*=}"
      shift
      ;;
    -v|--version)
      VERSION="$2"
      shift 2
      ;;
    *)
      # Unknown option
      shift
      ;;
  esac
done

# If VERSION is not set via arguments, check environment variable
if [ -z "$VERSION" ]; then
    echo "ERROR: Version must be specified using --version flag or VERSION environment variable"
    echo "Example: ./scripts/build.sh --version=v1.0.0"
    echo "Example: VERSION=v1.0.0 ./scripts/build.sh"
    exit 1
else
    echo "Using version: $VERSION"
fi

echo "Y" | gcloud auth configure-docker "$REPO"

# Build and push telegram bot image
echo "Building telegram_bot Docker image..."
docker build --platform linux/amd64 \
    -f docker/Dockerfile.telegram_bot \
    -t "${DOCKER_REPO_DEV}/telegram_bot:${VERSION}" .

# Build and push API server image
echo "Building api_server Docker image..."
docker build --platform linux/amd64 \
    -f docker/Dockerfile.api \
    -t "${DOCKER_REPO_DEV}/api_server:${VERSION}" .

# Push telegram bot to repository
echo "Pushing telegram_bot to container registry..."
docker push "${DOCKER_REPO_DEV}/telegram_bot:${VERSION}"

# Push API server to repository
echo "Pushing api_server to container registry..."
docker push "${DOCKER_REPO_DEV}/api_server:${VERSION}"

echo "Build and push completed successfully!"
import requests
import json
import datetime
import time

# Convert target date to Unix timestamp
target_date = datetime.datetime(2025, 3, 23, 5, 6, 39, tzinfo=datetime.timezone.utc)
target_timestamp = int(target_date.timestamp())

# BNB Chain RPC endpoint
bnb_rpc_url = "https://bsc-dataseed.binance.org/"

def make_rpc_call(method, params=None):
    """Make a JSON-RPC call to the BNB Chain node."""
    headers = {"Content-Type": "application/json"}
    payload = {
        "jsonrpc": "2.0",
        "method": method,
        "params": params if params else [],
        "id": int(time.time() * 1000)
    }
    
    try:
        response = requests.post(bnb_rpc_url, headers=headers, data=json.dumps(payload))
        response_data = response.json()
        
        if "error" in response_data:
            print(f"RPC Error: {response_data['error']}")
            return None
        
        return response_data["result"]
    except Exception as e:
        print(f"Error making RPC call: {e}")
        return None

def get_latest_block_number():
    """Get the latest block number."""
    result = make_rpc_call("eth_blockNumber")
    if result:
        return int(result, 16)
    return None

def get_block_timestamp(block_number):
    """Get the timestamp of a block by its number."""
    hex_block = hex(block_number)
    result = make_rpc_call("eth_getBlockByNumber", [hex_block, False])
    
    if result and "timestamp" in result:
        return int(result["timestamp"], 16)
    return None

def binary_search_block_by_timestamp(target_timestamp):
    """Find the block with timestamp closest to target_timestamp using binary search."""
    # Get the latest block number as the upper bound
    high = get_latest_block_number()
    if not high:
        print("Failed to get latest block number")
        return None
    
    # Set a reasonable lower bound for BNB Chain
    # Using a higher starting point for efficiency
    low = 47705901  # Starting from a known past block
    
    # Check if target timestamp is in the future
    latest_block_timestamp = get_block_timestamp(high)
    
    if not latest_block_timestamp:
        print("Failed to get latest block timestamp")
        return None
    
    if target_timestamp > latest_block_timestamp:
        print(f"\nTarget timestamp {target_timestamp} (UTC: {target_date}) is in the future!")
        print(f"Latest block {high} has timestamp {latest_block_timestamp} " + 
              f"(UTC: {datetime.datetime.fromtimestamp(latest_block_timestamp, tz=datetime.timezone.utc)})")
        print("We cannot find a block with a future timestamp.")
        print("Returning the latest block as the closest approximation.")
        return high
    
    closest_block = None
    smallest_diff = float('inf')
    
    print(f"Searching for block with timestamp closest to {target_timestamp} (UTC: {target_date})")
    
    while low <= high:
        mid = (low + high) // 2
        mid_timestamp = get_block_timestamp(mid)
        
        if mid_timestamp is None:
            # If we can't get the timestamp, try to adjust and continue
            low = mid + 1
            continue
        
        diff = abs(mid_timestamp - target_timestamp)
        
        print(f"Block {mid}: timestamp {mid_timestamp} " + 
              f"(UTC: {datetime.datetime.fromtimestamp(mid_timestamp, tz=datetime.timezone.utc)}), diff: {diff}")
        
        # Keep track of the closest block so far
        if diff < smallest_diff:
            smallest_diff = diff
            closest_block = mid
        
        if mid_timestamp < target_timestamp:
            low = mid + 1
        else:
            high = mid - 1
    
    # Verify the closest block and its neighbors
    verify_result(closest_block, target_timestamp)
    
    return closest_block

def verify_result(block_number, target_timestamp):
    """Verify the result by checking the block and its neighbors."""
    print(f"\nVerifying result around block {block_number}...")
    
    for b in range(block_number - 5, block_number + 6):
        if b >= 0:
            timestamp = get_block_timestamp(b)
            if timestamp:
                diff = abs(timestamp - target_timestamp)
                print(f"Block {b}: timestamp {timestamp} " + 
                      f"(UTC: {datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc)}), diff: {diff}")

def main():
    # Check connection by trying to get the latest block number
    latest_block = get_latest_block_number()
    if latest_block is None:
        print("Failed to connect to BNB Chain")
        return
    
    print(f"Connected to BNB Chain, current block: {latest_block}")
    print(f"Target timestamp: {target_timestamp} (UTC: {target_date})")
    
    # Start the binary search
    closest_block = binary_search_block_by_timestamp(target_timestamp)
    
    if closest_block is not None:
        block_timestamp = get_block_timestamp(closest_block)
        if block_timestamp:
            block_time = datetime.datetime.fromtimestamp(block_timestamp, tz=datetime.timezone.utc)
            
            print(f"\nResult: Block {closest_block}")
            print(f"Block timestamp: {block_timestamp} (UTC: {block_time})")
            print(f"Target timestamp: {target_timestamp} (UTC: {target_date})")
            print(f"Difference: {abs(block_timestamp - target_timestamp)} seconds")

if __name__ == "__main__":
    main()
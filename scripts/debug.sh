#!/bin/bash

# Debug script for trending tokens and token traders
# Usage: ./scripts/debug.sh <token_address> [is_24h]

# Make sure the Python scripts are executable
chmod +x scripts/debug_trending_tokens.py scripts/debug_token_traders.py

# Check if token address is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <token_address> [is_24h]"
    echo "  <token_address> - The token address to analyze"
    echo "  [is_24h] - Optional: 'true' for 24h data, 'false' for 6h data (default: false)"
    exit 1
fi

TOKEN_ADDRESS=$1
IS_24H=${2:-false}

echo "Debugging trending tokens and token traders..."
echo "Token address: $TOKEN_ADDRESS"
echo "Time period: $IS_24H"

# Run the trending tokens script in debug mode
echo "Running trending tokens script..."
python3 scripts/debug_trending_tokens.py $IS_24H clean

# Run the token traders script in debug mode
echo "Running token traders script..."
python3 scripts/debug_token_traders.py $TOKEN_ADDRESS clean

echo "Debug session complete." 
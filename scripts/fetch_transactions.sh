#!/bin/bash

for i in {1..100}
do
    curl 'https://api-v2.solscan.io/v2/account/transaction?address=6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P&page_size=40&instruction=buy&page='$((($i-1)%50+1)) \
    -H 'accept: application/json, text/plain, */*' \
    -H 'accept-language: en-US,en;q=0.9' \
    -H 'cache-control: no-cache' \
    -H 'cookie: _ga=GA1.1.*********.**********; cf_clearance=JzngRN_UQ5KPygcaEUQyX.UTQgSPn5M8To9A5VjQ_v0-**********-*******-w5S1wzz.QUsAtt3w2naFJb4mDC5P4TuUzzeOglO0RcAzi4q85tYhTiPYUUq2.QcV7I1PSVf59A.Lwvrr95ym3QFOa9USlKpATZGOIlVX6BkjvUYtsLKAEaXSAzXn27Klj_789f2ZYvihh.hDRJd4n4V5AYoZBLThR2IS7yejPQDLsXwPnkOoylBIMnANbmRnVMU2h6cINyJLYJu7Y1EbLCGhNs7jjlH2lOpNiAc.24S_1kGwZJGIAcBidsgAw3ImLNIT.BmqLHdF702jFz4f1tFRrBFtTzBfN2RW_VlHrMKGEFTivia3lGgzs53yDLz1C_k4V1I9pTZy6JX9D51.CA; _ga_PS3V7B7KV0=GS1.1.**********.147.1.**********.0.0.0' \
    -H 'dnt: 1' \
    -H 'origin: https://solscan.io' \
    -H 'pragma: no-cache' \
    -H 'priority: u=1, i' \
    -H 'referer: https://solscan.io/' \
    -H 'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'sec-fetch-dest: empty' \
    -H 'sec-fetch-mode: cors' \
    -H 'sec-fetch-site: same-site' \
    -H 'sol-aut: e=B9dls0fK62GNuxyt9acKfN0r=-0duDdKmHfDif' \
    -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' | jq > "./data/${i}.json"
    echo "Fetched data to ${i}.json"
done

#!/usr/bin/env python3
import sys
import json
import time
import os
import traceback
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains
from collections import defaultdict

def log(message, debug=False):
    """Print debug messages to stderr if debug mode is enabled"""
    if debug:
        print(message, file=sys.stderr)

class NetworkTrafficCollector:
    def __init__(self):
        self.responses = []

    def process_browser_logs(self, logs):
        for entry in logs:
            try:
                message = json.loads(entry['message'])['message']
                if message['method'] == 'Network.responseReceived':
                    response = message['params']
                    self.responses.append(response)
            except:
                continue

    def get_response_body(self, driver, *url_substrings):
        """Get response body for URLs containing all the specified substrings"""
        matching_responses = [r for r in self.responses 
                            if all(s in r['response']['url'] for s in url_substrings)]
        
        # Sort by timestamp in descending order (most recent first)
        matching_responses.sort(key=lambda x: x.get('timestamp', 0), reverse=True)
        
        for response in matching_responses:
            try:
                result = driver.execute_cdp_cmd('Network.getResponseBody', 
                                              {'requestId': response['requestId']})
                if result and 'body' in result:
                    return json.loads(result['body'])
            except Exception as e:
                log(f"Error getting response body: {e}", False)
                continue
        return None

def analyze_token(token_address, keep_browser_open=False, clean_output=False):
    """Analyze token using browser automation to get traders, holders, and dev data"""
    log(f"Starting token analysis for {token_address}...", not clean_output)
    
    try:
        # Configure Chrome options
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        display = os.environ.get("DISPLAY", None)
        if display:
            options.add_argument(f"--display={display}")

        # Enable performance logging
        options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        # Create a new instance of the undetected Chrome driver
        driver = uc.Chrome(headless=False, use_subprocess=True, options=options)
        
        # Set page load timeout
        driver.set_page_load_timeout(30)
        
        # Initialize network collector
        collector = NetworkTrafficCollector()
        
        # Initialize results dictionary
        results = {
            "traders": [],
            "holders": [],
            "devs": []
        }
        
        try:
            # Navigate to the URL
            log(f"Navigating to gmgn.ai/token/{token_address}...", not clean_output)
            driver.get(f"https://gmgn.ai/sol/token/{token_address}")
            
            # Wait for the page to load
            time.sleep(1)
            
            # Handle initial modal if present
            # try:
            #     # Find and click the close icon
            #     close_icon = driver.find_element(By.CSS_SELECTOR, ".css-gg4vpm svg.cursor-pointer")
            #     if close_icon:
            #         log(f"Found close icon: {close_icon.get_attribute('outerHTML')}", not clean_output)
                    
            #         # Create and dispatch a click event using JavaScript
            #         driver.execute_script("""
            #             const clickEvent = new MouseEvent('click', {
            #                 bubbles: true,
            #                 cancelable: true,
            #                 view: window
            #             });
            #             arguments[0].dispatchEvent(clickEvent);
            #         """, close_icon)
            #         log("Clicked close icon using MouseEvent", not clean_output)
            #     else:
            #         log("Close icon not found", not clean_output)
            #         time.sleep(1)
                
            #     # Dismiss modal by clicking background
            #     try:
            #         modal_wrap = driver.find_element(By.CLASS_NAME, "pi-modal-wrap")
            #         driver.execute_script("arguments[0].click();", modal_wrap)
            #         log("Dismissed modal", not clean_output)
            #     except:
            #         log("No modal wrap found", not clean_output)
            #         time.sleep(1)
            # except Exception as e:
            #     log(f"Error handling modal: {e}", not clean_output)

            # Process logs before clicking tabs
            collector.process_browser_logs(driver.get_log('performance'))

            # Get traders data
            log("Getting traders data...", not clean_output)
            try:
                traders_tab = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//*[text()='Traders']"))
                )
                driver.execute_script("arguments[0].click();", traders_tab)
                time.sleep(0.2)  # Wait for tab to load

                # Click on Pnl to sort by realized profit
                try:
                    pnl_tab = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Pnl')]"))
                    )
                    driver.execute_script("arguments[0].click();", pnl_tab)
                    log("Clicked Pnl tab", not clean_output)
                except Exception as e:
                    log(f"Error clicking Pnl tab: {e}", not clean_output)

                # Process logs after clicking tabs
                for i in range(30):
                    collector.process_browser_logs(driver.get_log('performance'))
                    traders_data = collector.get_response_body(driver, 
                                                             '/vas/api/v1/token_traders/sol/',
                                                             'orderby=realized_profit')
                    if traders_data and 'data' in traders_data and 'list' in traders_data['data']:
                        results['traders'] = traders_data['data']['list']
                        log("Successfully got traders data", not clean_output)
                        break
                    time.sleep(0.1)
            except Exception as e:
                log(f"Error getting traders data: {e}", not clean_output)
            
            # Get holders data
            log("Getting holders data...", not clean_output)
            try:
                holders_tab = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Holders')]"))
                )
                driver.execute_script("arguments[0].click();", holders_tab)
                time.sleep(0.2)  # Wait for API call
                
                # Process logs after clicking tab
                for i in range(30):
                    collector.process_browser_logs(driver.get_log('performance'))
                    holders_data = collector.get_response_body(driver, '/vas/api/v1/token_holders/sol/')
                    if holders_data and 'data' in holders_data and 'list' in holders_data['data']:
                        results['holders'] = holders_data['data']['list']
                        log("Successfully got holders data", not clean_output)
                        break
                    time.sleep(0.1)
            except Exception as e:
                log(f"Error getting holders data: {e}", not clean_output)

            # Get KOL data
            log("Getting KOL data...", not clean_output)
            try:
                kol_tab = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'KOL/VC')]"))
                )
                driver.execute_script("arguments[0].click();", kol_tab)
                time.sleep(0.2)  # Wait for API call
                
                # Process logs after clicking tab
                for i in range(30):
                    collector.process_browser_logs(driver.get_log('performance'))
                    kol_data = collector.get_response_body(driver, 
                                                        '/vas/api/v1/token_holders/sol/',
                                                        'tag=renowned')
                    if kol_data and 'data' in kol_data and 'list' in kol_data['data']:
                        results['kol_holders'] = kol_data['data']['list']
                        log("Successfully got kol data", not clean_output)
                        break
                    time.sleep(0.1)
            except Exception as e:
                log(f"Error getting kol data: {e}", not clean_output)
            
            # Get dev data
            log("Getting dev data...", not clean_output)
            try:
                dev_tab = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'DEV')]"))
                )
                driver.execute_script("arguments[0].click();", dev_tab)
                time.sleep(0.2)  # Wait for API call
                
                # Process logs after clicking tab
                for i in range(30):
                    collector.process_browser_logs(driver.get_log('performance'))
                    dev_data = collector.get_response_body(driver, 
                                                     '/vas/api/v1/token_holders/sol/',
                                                     'tag=dev')
                    if dev_data and 'data' in dev_data and 'list' in dev_data['data']:
                        results['devs'] = dev_data['data']['list']
                        log("Successfully got dev data", not clean_output)
                        break
                    time.sleep(0.1)
            except Exception as e:
                log(f"Error getting dev data: {e}", not clean_output)

        except Exception as e:
            log(f"Error during analysis: {e}", not clean_output)
            log(traceback.format_exc(), not clean_output)
            results['error'] = str(e)
        
        # Keep the browser open for inspection if requested
        if keep_browser_open:
            log("Debug session complete. Browser will remain open for inspection.", not clean_output)
            log("Press Ctrl+C to close the browser and exit.", not clean_output)
            while True:
                time.sleep(3)
        else:
            driver.quit()
            
    except Exception as e:
        log(f"Error in token analysis script: {e}", not clean_output)
        log(traceback.format_exc(), not clean_output)
        results = {'error': str(e)}
        
        if keep_browser_open:
            log("Browser will remain open for inspection due to error.", not clean_output)
            while True:
                time.sleep(3)
        else:
            driver.quit()
    
    # Print results as JSON
    print(json.dumps(results))
    return results

if __name__ == "__main__":
    # Get token address from command line arguments
    if len(sys.argv) < 2:
        print("Usage: python3 debug_token_analysis.py <token_address> [keep_open] [clean]")
        sys.exit(1)
    
    # Parse command line arguments
    token_address = sys.argv[1]
    keep_browser_open = 'keep_open' in sys.argv[2:]
    clean_output = 'clean' in sys.argv[2:]
    
    analyze_token(token_address, keep_browser_open, clean_output) 
#!/bin/bash

# Total block range
FROM_BLOCK=344736000
TO_BLOCK=345900000
TOTAL_BLOCKS=$((TO_BLOCK - FROM_BLOCK))

# Number of jobs to create
NUM_JOBS=10

# Calculate block range per job
BLOCKS_PER_JOB=$((TOTAL_BLOCKS / NUM_JOBS))

# Create jobs directory if it doesn't exist
mkdir -p jobs

# Generate jobs
for i in $(seq 0 $((NUM_JOBS-1))); do
    JOB_FROM=$((FROM_BLOCK + (i * BLOCKS_PER_JOB)))
    JOB_TO=$((JOB_FROM + BLOCKS_PER_JOB - 1))
    
    # For the last job, ensure we cover up to TO_BLOCK
    if [ $i -eq $((NUM_JOBS-1)) ]; then
        JOB_TO=$TO_BLOCK
    fi

    cat > "jobs/block-processor-${JOB_FROM}-${JOB_TO}.yaml" << EOF
apiVersion: batch/v1
kind: Job
metadata:
  name: block-processor-${JOB_FROM}-${JOB_TO}
  namespace: solana-data
spec:
  template:
    metadata:
      annotations:
        "cluster-autoscaler.kubernetes.io/safe-to-evict": "false"
    spec:
      serviceAccountName: default-ksa
      containers:
        - name: block-processor
          image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/stream-processor:latest
          workingDir: /app
          command: ["/app/block-processor"]
          args:
            - "--from-block"
            - "${JOB_FROM}"
            - "--to-block"
            - "${JOB_TO}"
            - "--threads"
            - "10"
          resources:
            requests:
              cpu: "0.5"
              memory: "1000Mi"
            limits:
              cpu: "1"
              memory: "1500Mi"
          env:
            - name: RPC_URL
              value: "https://solana-mainnet.g.alchemy.com/v2/********************************"
      restartPolicy: Never
  backoffLimit: 0
EOF

    echo "Created job for blocks ${JOB_FROM} to ${JOB_TO}"
done

echo "Generated ${NUM_JOBS} block processor jobs" 
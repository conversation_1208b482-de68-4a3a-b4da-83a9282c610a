#!/bin/bash

# -----------------------------------------------------------------------------
# GCP Solana 验证节点一键式部署脚本 (注入本地密钥并启动)
#
# 功能:
# 1. 下载主启动脚本。
# 2. 创建一个包含本地密钥注入逻辑和主脚本内容的最终启动脚本。
# 3. 使用最终启动脚本创建并配置 GCP 实例。
# -----------------------------------------------------------------------------

# --- 安全设置 ---
set -e    # 如果任何命令失败，则立即退出脚本
set -o pipefail # 管道中的命令失败也会导致整个管道失败

echo "=== GCP Solana 节点部署脚本启动 (含本地密钥注入) ==="

# --- 用户配置变量 (请务必修改以下所有带 YOUR_... 的值) ---

# 1. 本地 GCS 访问配置
# 修改成自己的路径
LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD="/Users/<USER>/Downloads/solana/centered-rope-298807-41925f3dad07.json"
GCS_MAIN_SCRIPT_PATH="gs://gcp-solana-solution/main_scripts/solana_startup_script.sh"
LOCAL_SCRIPT_SAVE_PATH="./downloaded_solana_startup_script.sh"
MY_ACCOUNT="<EMAIL>"

# 2. GCP 实例配置
# 修改成自己项目ID
GCP_PROJECT_ID="kryptogo-wallet-data"
# 修改成自己的项目编号
GCP_PROJECT_NUMBER="************"
GCP_INSTANCE_NAME="solana-tokyo-node-$(date +%s)"
GCP_ZONE="asia-northeast1-b"
GCP_INSTANCE_SERVICE_ACCOUNT_EMAIL="${GCP_PROJECT_NUMBER}-<EMAIL>"

# --- 配置结束 ---

echo ""
echo "--- 步骤 1: 从 GCS 下载主 Solana 启动脚本到本地 ---"
echo "使用密钥: ${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}"
echo "从 GCS 路径: ${GCS_MAIN_SCRIPT_PATH}"
echo "下载到本地: ${LOCAL_SCRIPT_SAVE_PATH}"

if [ ! -f "${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}" ]; then
    echo "错误：本地 JSON 密钥文件未找到于 '${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}'。" >&2
    exit 1
fi

export GOOGLE_APPLICATION_CREDENTIALS="${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}"

if gsutil -q cp "${GCS_MAIN_SCRIPT_PATH}" "${LOCAL_SCRIPT_SAVE_PATH}"; then
    echo "主启动脚本已成功下载到: ${LOCAL_SCRIPT_SAVE_PATH}"
else
    echo "错误：从 GCS 下载主启动脚本失败。" >&2
    unset GOOGLE_APPLICATION_CREDENTIALS
    exit 1
fi

unset GOOGLE_APPLICATION_CREDENTIALS
echo "----------------------------------------------------"
echo ""

# ------------------------- 新增步骤开始 -------------------------
echo "--- 步骤 1.5: 创建包含密钥和主脚本的最终启动脚本 ---"
# 定义最终生成的启动脚本的路径
FINAL_STARTUP_SCRIPT_PATH="./final_bootstrap_script.sh"
# 定义密钥将被存放在 GCE 实例内部的路径
GCP_SA_KEY_PATH_IN_INSTANCE="/etc/gcp/service-account-key.json"

# 将本地密钥文件的内容读取到一个变量中
# 直接注入原始 JSON 可能会因特殊字符导致脚本错误，因此这是更稳妥的方式
SA_KEY_CONTENT=$(cat "${LOCAL_JSON_KEY_PATH_FOR_SCRIPT_DOWNLOAD}")

# 使用 heredoc 创建最终的启动脚本
# 这个脚本将会在新的 GCE 实例上以 root 用户身份执行
cat > "${FINAL_STARTUP_SCRIPT_PATH}" << EOL
#!/bin/bash
set -e
#set -u
set -o pipefail

echo "--- [Instance] 步骤 A: 注入服务账号密钥 ---"
# 在实例内创建存放密钥的目录
mkdir -p "$(dirname "${GCP_SA_KEY_PATH_IN_INSTANCE}")"

# 使用 cat 和 heredoc 将密钥内容写入到实例内的文件中
cat << 'SA_KEY_EOF' > "${GCP_SA_KEY_PATH_IN_INSTANCE}"
${SA_KEY_CONTENT}
SA_KEY_EOF

# 设置安全权限，仅拥有者可读
chmod 400 "${GCP_SA_KEY_PATH_IN_INSTANCE}"

# 为此脚本后续的所有命令设置环境变量
export GOOGLE_APPLICATION_CREDENTIALS="${GCP_SA_KEY_PATH_IN_INSTANCE}"
gcloud auth activate-service-account --key-file="${GCP_SA_KEY_PATH_IN_INSTANCE}"

# (可选) 将环境变量永久添加到系统中，以便未来通过 SSH 登录时也能生效
echo "export GOOGLE_APPLICATION_CREDENTIALS=${GCP_SA_KEY_PATH_IN_INSTANCE}" > /etc/profile.d/gcp-sa-credentials.sh

echo "密钥文件已创建于: ${GCP_SA_KEY_PATH_IN_INSTANCE}"
echo "环境变量 GOOGLE_APPLICATION_CREDENTIALS 已设置。"
echo "--- [Instance] 步骤 A 完成 ---"
echo ""
echo "--- [Instance] 步骤 B: 开始执行原始主启动脚本 ---"

EOL

# 将之前下载的主脚本内容追加到新创建的引导脚本末尾
cat "${LOCAL_SCRIPT_SAVE_PATH}" >> "${FINAL_STARTUP_SCRIPT_PATH}"

echo "最终的启动脚本已在 '${FINAL_STARTUP_SCRIPT_PATH}' 创建成功。"
echo "它包含了密钥注入逻辑和原始的 Solana 部署逻辑。"
echo "----------------------------------------------------"
echo ""
# ------------------------- 新增步骤结束 -------------------------

echo "--- 步骤 2: 创建 GCP Compute Engine 实例并应用最终的启动脚本 ---"
echo "实例名称: ${GCP_INSTANCE_NAME}"
echo "项目 ID: ${GCP_PROJECT_ID}"
echo "区域: ${GCP_ZONE}"
echo "将使用最终启动脚本: ${FINAL_STARTUP_SCRIPT_PATH}"

gcloud compute instances create "${GCP_INSTANCE_NAME}" \
    --project="${GCP_PROJECT_ID}" \
    --zone="${GCP_ZONE}" \
    --machine-type=c4a-highmem-72 \
    --network-interface=network-tier=PREMIUM \
    --maintenance-policy=MIGRATE \
    --service-account="${GCP_INSTANCE_SERVICE_ACCOUNT_EMAIL}" \
    --scopes=https://www.googleapis.com/auth/cloud-platform \
    --image-family=ubuntu-2404-lts-arm64 \
    --image-project=ubuntu-os-cloud \
    --boot-disk-size=200GB \
    --boot-disk-provisioned-iops=10000 \
    --boot-disk-provisioned-throughput=290 \
    --boot-disk-type=hyperdisk-balanced \
    --create-disk=name="${GCP_INSTANCE_NAME}-ledger",device-name=solana-ledger-disk,size=1500GB,type=hyperdisk-extreme,auto-delete=yes \
    --create-disk=name="${GCP_INSTANCE_NAME}-accounts",device-name=solana-account-disk,size=1500GB,type=hyperdisk-extreme,auto-delete=yes \
    --shielded-vtpm \
    --shielded-integrity-monitoring \
    --metadata-from-file startup-script="${FINAL_STARTUP_SCRIPT_PATH}" \
    --account="${MY_ACCOUNT}"

echo ""
echo "GCP 实例 '${GCP_INSTANCE_NAME}' 创建命令已提交。"
echo "实例启动后将优先注入密钥，然后执行主脚本内容。"
# ... (后续的 echo 提示信息保持不变) ...
echo "  gcloud compute instances get-serial-port-output ${GCP_INSTANCE_NAME} --project ${GCP_PROJECT_ID} --zone ${GCP_ZONE} --port 1"
echo "----------------------------------------------------"
echo ""

echo "=== GCP Solana 节点部署脚本执行完毕 ==="
echo "提示: 本地生成的最终启动脚本位于 '${FINAL_STARTUP_SCRIPT_PATH}'。你可以根据需要保留或删除它。"

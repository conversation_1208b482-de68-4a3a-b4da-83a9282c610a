#!/bin/bash

# Function to calculate seconds until next run
calculate_sleep_time() {
    # Get current time in UTC+8
    current_time=$(TZ=Asia/Singapore date +%s)
    
    # Get today's 12pm in UTC+8
    today_noon=$(TZ=Asia/Singapore date -d "12:00:00" +%s)
    
    # Get current day of week (1-7, where 1 is Monday)
    current_day=$(TZ=Asia/Singapore date +%u)
    
    # Calculate days until next run
    if [ $current_time -gt $today_noon ]; then
        # If past noon, start counting from tomorrow
        case $current_day in
            1) days_to_add=2 ;; # Monday -> Wednesday
            2) days_to_add=1 ;; # Tuesday -> Wednesday
            3) days_to_add=2 ;; # Wednesday -> Friday
            4) days_to_add=1 ;; # Thursday -> Friday
            5) days_to_add=3 ;; # Friday -> Monday
            6) days_to_add=2 ;; # Saturday -> Monday
            7) days_to_add=1 ;; # Sunday -> Monday
        esac
    else
        # If before noon, check if today is a run day
        case $current_day in
            1|3|5) days_to_add=0 ;; # Monday, Wednesday, Friday
            2) days_to_add=1 ;; # Tuesday -> Wednesday
            4) days_to_add=1 ;; # Thursday -> Friday
            6) days_to_add=2 ;; # Saturday -> Monday
            7) days_to_add=1 ;; # Sunday -> Monday
        esac
    fi
    
    # Calculate next run time
    next_run=$(TZ=Asia/Singapore date -d "+${days_to_add} days 12:00:00" +%s)
    
    # Calculate sleep time
    sleep_time=$((next_run - current_time))
    echo $sleep_time
}

# Function to send message to Slack
send_to_slack() {
    local message="$1"
    # Split message into first line and rest
    first_line=$(echo "$message" | head -n 1)
    rest_of_message=$(echo "$message" | tail -n +2)
    
    # Format the message with code blocks
    formatted_message="$first_line\n\`\`\`\n$rest_of_message\n\`\`\`"
    
    # Escape special characters for JSON
    formatted_message=$(echo "$formatted_message" | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g')
    
    curl -X POST -H 'Content-type: application/json' \
    --data "{\"text\":\"$formatted_message\"}" \
    *******************************************************************************
}


# Calculate sleep time until next run
sleep_time=$(calculate_sleep_time)
next_run_time=$(TZ=Asia/Singapore date -d "@$(( $(TZ=Asia/Singapore date +%s) + sleep_time ))")
echo "Next run scheduled for: $next_run_time"
echo "Sleeping for $sleep_time seconds..."
sleep $sleep_time

# Main loop
while true; do
    # Record start time
    start_time=$(TZ=Asia/Singapore date +%s)
    
    # Run the analysis script and capture output
    output=$(python3 py/pump_hound_analyzer.py)
    
    # Extract the relevant part of the output
    relevant_output=$(echo "$output" | sed -n '/Top 10 tokens with highest gain/,$p')
    
    # Send to Slack
    send_to_slack "$relevant_output"
    
    # Calculate time taken for execution
    end_time=$(TZ=Asia/Singapore date +%s)
    execution_time=$((end_time - start_time))
    
    # Calculate sleep time until next run
    sleep_time=$(calculate_sleep_time)
    echo "Execution took $execution_time seconds. Sleeping for $sleep_time seconds until next run..."
    sleep $sleep_time
done

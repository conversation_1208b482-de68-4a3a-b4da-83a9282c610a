#!/bin/bash

# --- Configuration ---
START_EPOCH=700
END_EPOCH=799
BUCKET_NAME="gs://kg-old-faithful"
BASE_URL="https://files.old-faithful.net"
# Use the mount point you created
DOWNLOAD_DIR="/data"
LOG_FILE="${DOWNLOAD_DIR}/processing_log.txt"
# aria2c options for fast parallel downloading
ARIA2C_OPTS="-x 16 -s 16 -k 1M --dir=${DOWNLOAD_DIR}"

# --- Logging Function ---
# This function adds a timestamp to each log message.
log() {
  echo "$(date --iso-8601=seconds) - $1" | tee -a "${LOG_FILE}"
}

# --- Main Script ---
log "### SCRIPT STARTED ###"

for epoch in $(seq ${START_EPOCH} ${END_EPOCH}); do
  log "--- Starting Epoch ${epoch} ---"

  FILENAME="epoch-${epoch}.car"
  FILE_URL="${BASE_URL}/${epoch}/${FILENAME}"
  LOCAL_FILE_PATH="${DOWNLOAD_DIR}/${FILENAME}"
  GCS_FILE_PATH="${BUCKET_NAME}/${FILENAME}"

  # Clean up file from previous run just in case
  rm -f "${LOCAL_FILE_PATH}"

  # --- Download with Retry ---
  log "Downloading ${FILE_URL}..."
  while true; do
    # Execute download
    aria2c ${ARIA2C_OPTS} "${FILE_URL}"

    # Check if download was successful
    if [ $? -eq 0 ]; then
      log "Download for epoch ${epoch} successful."
      break # Exit retry loop
    else
      log "ERROR: Download for epoch ${epoch} failed. Retrying in 60 seconds..."
      sleep 60
    fi
  done

  # --- Upload with Retry ---
  log "Uploading ${FILENAME} to ${GCS_FILE_PATH}..."
  while true; do
    # Execute upload
    gcloud storage cp "${LOCAL_FILE_PATH}" "${GCS_FILE_PATH}"

    # Check if upload was successful
    if [ $? -eq 0 ]; then
      log "Upload for epoch ${epoch} successful."
      break # Exit retry loop
    else
      log "ERROR: Upload for epoch ${epoch} failed. Retrying in 60 seconds..."
      sleep 60
    fi
  done

  # --- Cleanup ---
  log "Cleaning up local file ${LOCAL_FILE_PATH}."
  rm -f "${LOCAL_FILE_PATH}"

  log "--- Finished Epoch ${epoch} ---"
done

log "### SCRIPT COMPLETED ###"
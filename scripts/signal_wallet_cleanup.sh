#!/bin/bash
# signal_wallet_cleanup.sh - <PERSON><PERSON><PERSON> to extract underperforming wallets and remove them

# Log file to keep track of removals
LOG_FILE="wallet_removal_log.txt"

echo "$(date): Running wallet performance check..." | tee -a $LOG_FILE

# Run the Python script and capture its output
performance_output=$(python py/signal_performance.py)

# Display the output for reference
echo "Performance analysis output:"
echo "$performance_output"
echo "---------------------------------"

# Extract wallet addresses using regex pattern matching
wallet_addresses=$(echo "$performance_output" | grep "Remove wallet" | sed -E 's/Remove wallet ([a-zA-Z0-9]{32,}):.*/\1/')

# Check if we found any wallets to remove
if [ -z "$wallet_addresses" ]; then
    echo "No wallets to remove." | tee -a $LOG_FILE
    exit 0
fi

# Convert newline-separated addresses to space-separated for the cargo command
wallet_addresses_arg=$(echo "$wallet_addresses" | tr '\n' ' ')

# Echo the wallets being removed
echo "Removing the following wallets:" | tee -a $LOG_FILE
echo "$wallet_addresses" | tee -a $LOG_FILE

# Construct the cargo command
cargo_cmd="cargo run --bin manage_wallets --release remove $wallet_addresses_arg"

# Show the command that will be executed
echo "Executing: $cargo_cmd" | tee -a $LOG_FILE

# Execute the command and log the result
if $cargo_cmd >> $LOG_FILE 2>&1; then
    echo "Successfully removed wallets." | tee -a $LOG_FILE
else
    echo "Failed to remove wallets. Check $LOG_FILE for details." | tee -a $LOG_FILE
fi

echo "Wallet cleanup completed at $(date)" | tee -a $LOG_FILE
echo "----------------------------------------" | tee -a $LOG_FILE
#!/bin/bash
set -e

# Default values
DB_USER=${DB_USER:-"solana_data"}
DB_PASSWORD=${DB_PASSWORD:-""}
DB_NAME=${DB_NAME:-"solana_data"}
PROJECT_ID=${PROJECT_ID:-"kryptogo-wallet-data"}
REGION=${REGION:-"asia-northeast1"}
INSTANCE_NAME=${INSTANCE_NAME:-"solana-data"}

# Function to check if port is in use
check_port() {
    if lsof -i :5432 > /dev/null 2>&1; then
        echo "Port 5432 is already in use. Please stop any existing PostgreSQL or Cloud SQL Proxy processes."
        exit 1
    fi
}

# Function to install cloud-sql-proxy
install_cloud_sql_proxy() {
    local os_type
    local arch
    local proxy_version="v2.17.1"

    # Detect OS and architecture
    case "$(uname -s)" in
        Linux*)  os_type="linux";;
        Darwin*) os_type="darwin";;
        *)       echo "Unsupported OS"; exit 1;;
    esac

    case "$(uname -m)" in
        x86_64)  arch="amd64";;
        arm64)   arch="arm64";;
        *)       echo "Unsupported architecture"; exit 1;;
    esac

    echo "Installing cloud-sql-proxy for $os_type/$arch..."
    curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/$proxy_version/cloud-sql-proxy.$os_type.$arch
    chmod +x cloud-sql-proxy
    sudo mv cloud-sql-proxy /usr/local/bin/cloud-sql-proxy
}

# Function to install Java
install_java() {
    echo "Installing Java..."
    if [ -n "$GITHUB_ACTIONS" ]; then
        # For GitHub Actions (Ubuntu)
        sudo apt-get update
        sudo apt-get install -y openjdk-11-jdk
    else
        # For macOS
        if ! command -v brew &> /dev/null; then
            echo "Please install Homebrew first: https://brew.sh/"
            exit 1
        fi
        brew install openjdk@11
    fi
}

# Function to install Liquibase
install_liquibase() {
    echo "Installing Liquibase..."
    # Create temporary directory for installation
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Download and extract Liquibase
    curl -L https://github.com/liquibase/liquibase/releases/download/v4.32.0/liquibase-4.32.0.tar.gz -o liquibase.tar.gz
    tar -xzf liquibase.tar.gz
    
    # Create Liquibase directory structure
    sudo rm -f /usr/local/bin/liquibase  # Remove existing executable
    sudo mkdir -p /opt/liquibase
    sudo cp -r lib /opt/liquibase/
    sudo cp -r internal /opt/liquibase/
    
    # Create the liquibase executable
    sudo tee /usr/local/bin/liquibase > /dev/null << 'EOF'
#!/bin/bash
export LIQUIBASE_HOME=/opt/liquibase
java -jar /opt/liquibase/internal/lib/liquibase-core.jar "$@"
EOF
    
    sudo chmod +x /usr/local/bin/liquibase
    
    # Clean up
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
}

# Function to install PostgreSQL JDBC driver
install_postgres_jdbc() {
    echo "Downloading PostgreSQL JDBC driver..."
    curl -L https://jdbc.postgresql.org/download/postgresql-42.7.4.jar -o postgresql-42.7.4.jar
    sudo mv postgresql-42.7.4.jar /opt/liquibase/lib/
}

# Function to start cloud-sql-proxy
start_cloud_sql_proxy() {
    # Check if cloud-sql-proxy is installed
    if ! command -v cloud-sql-proxy &> /dev/null; then
        install_cloud_sql_proxy
    fi

    # Check if port is available
    check_port

    # Get connection name
    CONNECTION_NAME=$(gcloud sql instances describe "$INSTANCE_NAME" --format="value(connectionName)" --project="$PROJECT_ID")

    # Start Cloud SQL Auth Proxy
    echo "Starting Cloud SQL Auth Proxy..."
    cloud-sql-proxy "$CONNECTION_NAME" --port 5432 &
    PROXY_PID=$!

    # Wait for proxy to start and verify connection
    echo "Waiting for proxy to start..."
    for i in {1..30}; do
        if nc -z localhost 5432 > /dev/null 2>&1; then
            echo "Cloud SQL Auth Proxy is running"
            break
        fi
        if [ "$i" -eq 30 ]; then
            echo "Failed to start Cloud SQL Auth Proxy"
            exit 1
        fi
        sleep 1
    done
}

# Function to run Liquibase update
run_liquibase_update() {
    echo "Running Liquibase update..."
    pushd data-writer
    export LIQUIBASE_HOME=/opt/liquibase

    liquibase \
        --driver=org.postgresql.Driver \
        --classpath=/opt/liquibase/lib/postgresql-42.7.4.jar \
        --changeLogFile=db/changelog/changelog.xml \
        --url="*****************************************" \
        --username="$DB_USER" \
        --password="$DB_PASSWORD" \
        update

    popd
}

# Function to cleanup
cleanup() {
    if [ -n "$PROXY_PID" ]; then
        echo "Stopping Cloud SQL Auth Proxy..."
        kill $PROXY_PID
    fi
}

# Main script execution
main() {
    # Start cloud-sql-proxy
    start_cloud_sql_proxy

    # Install Java if needed
    if ! command -v java &> /dev/null; then
        install_java
    fi

    # Install Liquibase if needed
    if ! command -v liquibase &> /dev/null; then
        install_liquibase
    fi

    # Install PostgreSQL JDBC driver if needed
    if [ ! -f "/opt/liquibase/lib/postgresql-42.7.4.jar" ]; then
        install_postgres_jdbc
    fi

    # Run Liquibase update
    run_liquibase_update

    # Cleanup
    cleanup
}

# Run main function
main

#!/bin/bash

export SOLANA_VALIDATOR_BIN="/solana/agave/bin/agave-validator"

$SOLANA_VALIDATOR_BIN \
  --identity /solana/keys/validator-keypair.json \
  --known-validator 9W3QTgBhkU4Bwg6cwnDJo6eGZ9BtZafSdu1Lo9JmWws7 \
  --known-validator 82vucuWCTTQEz6nYe3VetnL3pJYBrfDF2gDAjec9sPUy \
  --known-validator B4dn3WWS95M4qNXaR5NTdkNzhzvTZVqC13E3eLrWhXLa \
  --known-validator EBk678aQvc3cUkfGyoehfw21JQfJXjmWuBeopYc89RSV \
  --known-validator 7ZjHeeYEesmBs4N6aDvCQimKdtJX2bs5boXpJmpG2bZJ \
  --known-validator GX6kCVtpvFTGsedV72nK5K6VzY1bTCvqFmrtHkuZHGsX \
  --known-validator 5d9Mdc2Zk8as8GL1AxQeXxv5htBBvC5bjfmsXC7UUWwG \
  --known-validator DMGGRvdRwhqrpo7LLnUzSkivaTNoGCsU4NtnL6czYKYG \
  --known-validator D4r6Rcua2L7nHHhdaiZe2k2bTfPg2WQqcNYpG6bugvCG \
  --skip-poh-verify \
  --experimental-poh-pinned-cpu-core 4 \
  --poh-hashes-per-batch 512 \
  --only-known-rpc \
  --no-voting \
  --private-rpc \
  --log /solana/log/solana-validator.log \
  --ledger /solana/ledger \
  --accounts /solana/accounts \
  --rpc-port 8899 \
  --rpc-bind-address 0.0.0.0 \
  --dynamic-port-range 8000-8020 \
  --entrypoint entrypoint.mainnet-beta.solana.com:8001 \
  --entrypoint entrypoint2.mainnet-beta.solana.com:8001 \
  --entrypoint entrypoint3.mainnet-beta.solana.com:8001 \
  --entrypoint entrypoint4.mainnet-beta.solana.com:8001 \
  --entrypoint entrypoint5.mainnet-beta.solana.com:8001 \
  --expected-genesis-hash 5eykt4UsFv8P8NJdTREpY1vzqKqZKvdpKuc147dw2N9d \
  --wal-recovery-mode skip_any_corrupted_record \
  --limit-ledger-size \
  --disable-banking-trace \
  --no-genesis-fetch \
  --skip-preflight-health-check \
  --tpu-connection-pool-size 128 \
  --rpc-max-request-body-size ********* \
  --no-os-network-limits-test \
  --rpc-blocking-threads 8 \
  --rpc-threads 8 \
  --snapshot-interval-slots 0 \
  --full-snapshot-interval-slots 18000 \
  --snapshot-packager-niceness-adjustment 20 \
  --minimal-snapshot-download-speed ********* \
  --block-production-method central-scheduler-greedy \
  --no-os-network-stats-reporting \
  --no-os-memory-stats-reporting \
  --no-os-cpu-stats-reporting \
  --no-os-disk-stats-reporting \
  --no-poh-speed-test \
  --no-port-check \
  --no-snapshot-fetch \
  --accounts-db-hash-threads 8 \
  --accounts-index-flush-threads 8 \
  --disable-accounts-disk-index \
  --replay-forks-threads 4 \
  --tvu-receive-threads 4 \
  --maximum-local-snapshot-age 1000 \
  --tpu-use-quic \
  --no-skip-initial-accounts-db-clean \
  --geyser-plugin-config /solana/yellowstone-grpc/yellowstone-grpc-geyser/config.json

# --account-index: Enable an accounts index, indexed by the selected account field [possible values: program-id, spl-token-owner, spl-token-mint]
# enable -> 500GB memory may not enough

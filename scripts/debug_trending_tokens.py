#!/usr/bin/env python3
import sys
import json
import time
import os
import traceback
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hai<PERSON>

def log(message, debug=False):
    """Print debug messages to stderr if debug mode is enabled"""
    if debug:
        print(message, file=sys.stderr)

def debug_trending_tokens(is_24h=False, keep_browser_open=False, clean_output=False):
    """Debug trending tokens using undetected-chromedriver"""
    log(f"Starting debug version of trending tokens script ({'24h' if is_24h else '6h'})...", not clean_output)
    
    try:
        # Configure Chrome options
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        display = os.environ.get("DISPLAY", None)
        if display:
            options.add_argument(f"--display={display}")
        
        # Enable performance logging
        options.set_capability('goog:loggingPrefs', {'performance': 'ALL', 'browser': 'ALL'})
        
        # Create a new instance of the undetected Chrome driver
        driver = uc.Chrome(headless=False, use_subprocess=True, options=options)
        
        # Set page load timeout
        driver.set_page_load_timeout(60)
        
        try:
            # Navigate to the correct URL
            log("Navigating to gmgn.ai...", not clean_output)
            driver.get("https://gmgn.ai/trend?0mac=48h&0mim=50000&chain=sol&0im=1&0miv=200&0iv=1")
            
            # Wait for the page to load
            log("Waiting for page to load...", not clean_output)
            time.sleep(3)
            
            # Step 1: Find and click the close icon
            log("Looking for close icon...", not clean_output)
            try:
                # Find the SVG element with cursor-pointer class
                close_icon = driver.find_element(By.CSS_SELECTOR, ".css-gg4vpm svg.cursor-pointer")
                if close_icon:
                    log(f"Found close icon: {close_icon.get_attribute('outerHTML')}", not clean_output)
                    
                    # Create and dispatch a click event using JavaScript
                    driver.execute_script("""
                        const clickEvent = new MouseEvent('click', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                        });
                        arguments[0].dispatchEvent(clickEvent);
                    """, close_icon)
                    log("Clicked close icon using MouseEvent", not clean_output)
                else:
                    log("Close icon not found", not clean_output)
            except Exception as e:
                log(f"Error clicking close icon: {e}", not clean_output)
            
            # Instead of clicking Next buttons multiple times then Finish button,
            # directly click the modal background to dismiss it
            log("Looking for modal background to dismiss...", not clean_output)
            try:
                driver.execute_script("document.querySelector('.pi-modal-wrap').click();")
                log("Clicked modal background using JavaScript", not clean_output)
                
                # Wait a moment for any animations or page changes
                time.sleep(2)
            except Exception as e:
                log(f"Error clicking modal background: {e}", not clean_output)
            
            # Clear any existing logs
            driver.get_log('performance')
            
            # Step 2: Click the 6h tab and wait for network response
            log("Clicking 6h tab...", not clean_output)
            try:
                # Find and click the 6h tab using the original approach
                time_selector = "6h"
                time_element = WebDriverWait(driver, 1).until(
                    EC.presence_of_element_located((By.XPATH, f"//*[text()='{time_selector}']"))
                )
                log(f"Time selector found: {time_selector}", not clean_output)
                
                # Click the time selector
                log("Clicking time selector...", not clean_output)
                time_element.click()
                
                # Wait for network response
                log("Waiting for 6h network response...", not clean_output)
                time.sleep(3)
                
                # Extract 6h data from network requests
                log("Extracting 6h data from network requests...", not clean_output)
                six_h_data = extract_data_from_network_requests(driver, "6h", not clean_output)
                
                if six_h_data:
                    log("Successfully extracted 6h data", not clean_output)
                else:
                    log("Failed to extract 6h data", not clean_output)
            except Exception as e:
                log(f"Error with 6h tab: {e}", not clean_output)
            
            # Clear any existing logs
            driver.get_log('performance')
            
            # Step 3: Click the 24h tab and wait for network response
            log("Clicking 24h tab...", not clean_output)
            try:
                # Find and click the 24h tab using the original approach
                time_selector = "24h"
                time_element = WebDriverWait(driver, 1).until(
                    EC.presence_of_element_located((By.XPATH, f"//*[text()='{time_selector}']"))
                )
                log(f"Time selector found: {time_selector}", not clean_output)
                
                # Click the time selector
                log("Clicking time selector...", not clean_output)
                time_element.click()
                
                # Wait for network response
                log("Waiting for 24h network response...", not clean_output)
                time.sleep(3)
                
                # Extract 24h data from network requests
                log("Extracting 24h data from network requests...", not clean_output)
                twenty_four_h_data = extract_data_from_network_requests(driver, "24h", not clean_output)
                
                if twenty_four_h_data:
                    log("Successfully extracted 24h data", not clean_output)
                else:
                    log("Failed to extract 24h data", not clean_output)
            except Exception as e:
                log(f"Error with 24h tab: {e}", not clean_output)
            
            # Combine the data from both periods
            combined_data = {
                "code": 0,
                "data": {
                    "rank_6h": six_h_data.get("data", {}).get("rank", []) if six_h_data else [],
                    "rank_24h": twenty_four_h_data.get("data", {}).get("rank", []) if twenty_four_h_data else []
                }
            }
            
            # Output the combined data
            if clean_output:
                print(json.dumps(combined_data))
            else:
                print(json.dumps(combined_data, indent=2))
            
            # Keep the browser open for inspection if requested
            if keep_browser_open:
                log("Debug session complete. Browser will remain open for inspection.", not clean_output)
                log("Press Ctrl+C to close the browser and exit.", not clean_output)
                
                # Keep the script running
                while True:
                    time.sleep(3)
            else:
                log("Debug session complete. Closing browser.", not clean_output)
                driver.quit()
                
        except Exception as e:
            log(f"Error in debug trending tokens script: {e}", not clean_output)
            log(traceback.format_exc(), not clean_output)
            
            # Keep the browser open for inspection in case of error if requested
            if keep_browser_open:
                log("Browser will remain open for inspection due to error.", not clean_output)
                # Keep the browser open for inspection in case of error
                while True:
                    time.sleep(3)
            else:
                driver.quit()
                
    except Exception as e:
        log(f"Error launching browser: {e}", not clean_output)
        log(traceback.format_exc(), not clean_output)
        return

def extract_data_from_network_requests(driver, time_period, debug=False):
    """Extract data from network requests to the trending tokens API"""
    log(f"Extracting data for {time_period} period...", debug)
    
    try:
        # Enable network interception
        driver.execute_cdp_cmd('Network.enable', {})
        
        # Get all network requests
        logs = driver.get_log('performance')
        response_data = None
        
        for entry in logs:
            if 'message' in entry:
                message = json.loads(entry['message'])
                if 'message' in message and 'method' in message['message']:
                    if message['message']['method'] == 'Network.responseReceived':
                        params = message['message']['params']
                        request_id = params['requestId']
                        url = params['response']['url']
                        
                        # Check if this is the trending tokens API request
                        if '/defi/quotation/v1/rank/sol/swaps/' in url and time_period in url:
                            log(f"Found matching request for {time_period}: {url}", debug)
                            try:
                                # Try to get response body
                                response = driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': request_id})
                                if 'body' in response:
                                    response_data = json.loads(response['body'])
                                    log(f"Successfully captured response data for {time_period}", debug)
                                    return response_data
                            except Exception as e:
                                log(f"Error getting response body: {e}", debug)
        
        if not response_data:
            log(f"Could not find response data for {time_period} in network traffic", debug)
            
            # Try alternative approach: directly make the request
            log(f"Trying direct request for {time_period}...", debug)
            try:
                # Construct the URL with the correct parameters
                url = f"https://gmgn.ai/defi/quotation/v1/rank/sol/swaps/{time_period}?device_id=d91ea0bf-39d5-4aa0-810b-6de0255b57f4&client_id=gmgn_web_2025.0408.191250&from_app=gmgn&app_ver=2025.0408.191250&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en-US&fp_did=4ebbc60120df9d9dfa338a3016e5bb81&os=web&orderby=swaps&direction=desc&filters[]=renounced&filters[]=frozen&max_created=48h&min_marketcap=50000&min_volume=200000"
                
                # Execute the request using JavaScript
                response_text = driver.execute_script(f"""
                    var xhr = new XMLHttpRequest();
                    xhr.open('GET', '{url}', false);
                    xhr.setRequestHeader('accept', 'application/json, text/plain, */*');
                    xhr.setRequestHeader('accept-language', 'en-US,en;q=0.9');
                    xhr.setRequestHeader('sec-ch-ua', '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"');
                    xhr.setRequestHeader('sec-ch-ua-mobile', '?0');
                    xhr.setRequestHeader('sec-ch-ua-platform', '"macOS"');
                    xhr.setRequestHeader('sec-fetch-dest', 'empty');
                    xhr.setRequestHeader('sec-fetch-mode', 'cors');
                    xhr.setRequestHeader('sec-fetch-site', 'same-origin');
                    xhr.setRequestHeader('user-agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
                    xhr.send(null);
                    return xhr.responseText;
                """)
                
                if response_text:
                    response_data = json.loads(response_text)
                    log(f"Successfully captured response data for {time_period} using direct request", debug)
                    return response_data
            except Exception as e:
                log(f"Error with direct request: {e}", debug)
    except Exception as e:
        log(f"Error capturing network traffic: {e}", debug)
    
    return None

if __name__ == "__main__":
    # Get time period from command line arguments (true for 24h, false for 6h)
    is_24h = False
    keep_browser_open = False
    clean_output = False
    
    # Parse command line arguments
    for arg in sys.argv[1:]:
        if arg.lower() == 'true':
            is_24h = True
        elif arg.lower() == 'false':
            is_24h = False
        elif arg.lower() == 'keep_open':
            keep_browser_open = True
        elif arg.lower() == 'clean':
            clean_output = True
    
    debug_trending_tokens(is_24h, keep_browser_open, clean_output) 
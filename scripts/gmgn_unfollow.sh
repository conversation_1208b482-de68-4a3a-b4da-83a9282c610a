#!/bin/bash

# Authorization token
AUTH_TOKEN="**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Cookies
COOKIES="_ga=GA1.1.2039738004.1741880928; sid=gmgn%7C701bd0eecc9cb5d61c75bd92456ce544; *ga*UGLVBMV4Z0=GS1.2.1741880941524772.7ace014211b366a86efe8ebe7b206400.HRf0Trfbp7rSZ1RwLd%2Frig%3D%3D.NbRFLMlCd6wiUPdCU7oP2g%3D%3D.yndEpQO8YYrGFpC8EMJuzw%3D%3D.JNBHko2DxeZ1LaXrRr0GZw%3D%3D; *ga*0XM0LYXGC8=deleted; cf_clearance=ohn4k_8ESIFgX418_B8_H11qSsM5w7SHdvcH0_LNDXY-1742103321-1.2.1.1-c4Tb71MOW_Q5iSUkKSBCj2H5lBUfTAv1YPinL_R9Hsq3CHBuCAGmnltz9h0sOE7crcneYWtCnxZ5ntC4QD9F3258Pizmj6txRcBJjmSg1ghKQYnAGDq2_fKCNRfqhWZt3Um0N54mFDI4aBRcvKdz90O35hQ0txKx9anu7AnYkORRgTL6K3tt7I04.khAeMYBPxlpfUzl8D44CuAxYB3WsFLZuGW.iy.4uHEPU_q75mbAdkHYm5lZJbiQrs9ZL5chR1TD.orvFj7lCS_NXis2oWD4qwqNJvyMJLY.ChBYTfyOugb4xsY373qzGpEXw3NQZqtslXLtYQp4hcwLdQpoIOFByo.axdPBh4Fkf1Gc3rg; __cf_bm=mDr3SThoQrgLu9Ki_3D0jLW20NOLfepM7qUmgy47Sjo-1742103783-*******-ZaG5TbHop8sOg5F6TknYF8noekfiGJ5R_ysemPqRtCwvd23rjSKpnJ.rmacJFXU_KhW04bUMwG4._32rqrMG_s_uQMEnugaP6MGTX_RFm.I; *ga*0XM0LYXGC8=GS1.1.1742102197.16.1.1742103862.0.0.0"

# Array of wallet addresses to unfollow
WALLETS=(
"2NmHXhrHqo3Qv1sb7X97PVHb3YveZK2qMCbXkMhtntfC"
"2HJMgsEqKrcRNDqo1P9g9pCFEXSdi2VtKqDKg9qhikco"
"2GtBoiU6EAu9bDZFkQZ8E5BdqkFdj7uFPuVPYGXRBx1H"
"2FwCgcJmPc1qgfyC9GVzNWctSowpySU8b9coSBikHbGN"
"2Fk6NV4snbg3nX1PsPnYPz971vR2uPv83DaJAJSCy4FR"
"2FbQnQGRhJLo42Nuzc8cis8F2yazAT2vpCiRSKeH68iT"
"2Fav2ymDfdsmNNusF31NNKME4C8fjreVRgUYMkHc8dHi"
"2E4vXUY7cYdaeKk9mQqAVtzKihfTYzoWkYg4ePY71U8c"
"2DyiYhjT6DuF1n3XuVQDMwXjRkc3hbVReTM189hPg5FN"
"2DqKAcZbMn9Ms9qPAPGCZWmTW4us17SXbD1u3SxE2Tuj"
"23TDC9pbjKzvEEExqxPsfJmXuAhfD9nqSJEMTycnPCSX"
"********************************************"
"********************************************"
"********************************************"
"********************************************"
"46W2UspRnpB8x7ZajVTg2rVBo2ad3CbnXiMEV8Y96kQa"
"BieeZkdnBAgNYknzo3RH2vku7FcPkFZMZmRJANh2TpW"
"EWkGyhD7XEgzJsutnTmN6qCobBBhMuDukaGXUmMhjM7f"
"4DdrfiDHpmx55i4SPssxVzS9ZaKLb8qr45NKY9Er9nNh"
)

# Your current wallet
YOUR_WALLET="B8vg8NRWnVw6PYBkvuS92CDg13YLR349vGtBQqjvxJAB"

# Loop through each wallet and unfollow
for wallet in "${WALLETS[@]}"; do
  echo "Unfollowing wallet: $wallet"
  
  # Prepare the JSON payload
  JSON_DATA="{\"address\":\"$YOUR_WALLET\",\"network\":\"sol\",\"chain\":\"sol\",\"wallet_addresses\":[\"$wallet\"],\"remark_addresses\":[]}"
  
  # Execute the curl command
  response=$(curl 'https://gmgn.ai/api/v1/follow/unfollow_wallet?device_id=934bb6e3-adb2-494e-aad0-99da8d0b6661&client_id=gmgn_web_2025.0314.194246&from_app=gmgn&app_ver=2025.0314.194246&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en-US' \
    -H 'accept: application/json, text/plain, */*' \
    -H 'accept-language: en-US,en;q=0.9' \
    -H "authorization: Bearer $AUTH_TOKEN" \
    -H 'cache-control: no-cache' \
    -H 'content-type: application/json' \
    -b "$COOKIES" \
    -H 'dnt: 1' \
    -H 'origin: https://gmgn.ai' \
    -H 'pragma: no-cache' \
    -H 'priority: u=1, i' \
    -H 'referer: https://gmgn.ai/follow/MVNsiiWE?chain=sol' \
    -H 'sec-ch-ua: "Not:A-Brand";v="24", "Chromium";v="134"' \
    -H 'sec-ch-ua-mobile: ?0' \
    -H 'sec-ch-ua-platform: "macOS"' \
    -H 'sec-fetch-dest: empty' \
    -H 'sec-fetch-mode: cors' \
    -H 'sec-fetch-site: same-origin' \
    -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
    --data-raw "$JSON_DATA" \
    --silent)
  
  # Print the response
  echo "Response: $response"
  
  # Add a small delay between requests to avoid rate limiting
  sleep 1
done

echo "All wallets have been unfollowed!"
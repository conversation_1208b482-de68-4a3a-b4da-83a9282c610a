#!/bin/bash

# Test script for the FFI integration between Rust and Go
set -e

echo "🚀 Testing FFI Integration"
echo "=========================="

SERVER_URL="http://localhost:8088"
TEST_TOKEN="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"

GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

check_server() {
    echo "Checking if server is running..."
    if curl -s "$SERVER_URL/health" > /dev/null; then
        print_status "Server is running"
    else
        print_error "Server is not running"
        exit 1
    fi
}

test_ffi_endpoint() {
    echo ""
    echo "Testing FFI endpoint..."
    
    local response=$(curl -s -X POST "$SERVER_URL/analyze/suspect_wallets_balances" \
        -H "Content-Type: application/json" \
        -d "{\"token\": \"$TEST_TOKEN\"}")
    
    echo "Response: $response"
    
    if echo "$response" | grep -q '"status":"success"'; then
        print_status "FFI endpoint test passed"
    else
        print_error "FFI endpoint test failed"
        return 1
    fi
}

main() {
    check_server
    test_ffi_endpoint
    echo ""
    print_status "FFI integration test completed!"
}

main "$@"

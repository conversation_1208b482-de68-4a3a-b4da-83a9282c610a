#!/bin/bash

# Check if the token data file exists
TOKEN_DATA_FILE="output/top_tokens.txt"
if [ ! -f "$TOKEN_DATA_FILE" ]; then
    echo "Error: Token data file not found at $TOKEN_DATA_FILE"
    echo "Please run the price_milestone_analyzer.py script first to generate token data."
    exit 1
fi

# API base URLs
API_URLS=(
    # "https://api-68779486420.asia-east1.run.app"
    "https://api-366580911590.asia-east1.run.app"
    "https://api-307061773427.asia-east1.run.app"
)

# Telegram link
TELEGRAM_LINK="https://t.me/pump_hound_signal"

# Function to send a request for a specific address, gain percentage and API URL
send_request() {
    local address=$1
    local gain=$2
    local api_url=$3
    
    # Convert gain percentage to float by dividing by 100
    local gain_float=$(echo "scale=2; $gain/100" | bc)
    
    echo "Sending request for address: $address with gain: +${gain}% (float: $gain_float) to API: $api_url"
    
    curl -X POST "$api_url/_v/token_signal/sell" \
         -H "Content-Type: application/json" \
         -d "{\"token_address\":\"$address\",\"highest_gain\":$gain_float,\"telegram_link\":\"$TELEGRAM_LINK\"}"
    
    echo -e "\n"
    
    # Add a small delay between requests to avoid rate limiting
    sleep 1
}

# Main loop to process all token data and API URLs
echo "Processing top tokens from $TOKEN_DATA_FILE"

# Read each line from the token data file
while IFS=, read -r address gain || [[ -n "$address" ]]; do
    for api_url in "${API_URLS[@]}"; do
        send_request "$address" "$gain" "$api_url"
    done
done < "$TOKEN_DATA_FILE"

echo "All requests completed!" 
[package]
name = "solana-bot"
version = "0.1.0"
edition = "2021"
authors = ["Your Name"]
description = "A Solana MEV bot for extracting maximal extractable value from on-chain transactions"

[dependencies]
solana-sdk = "2.2.1"
solana-client = "2.2.0"
solana-transaction-status = "2.2.0"
solana-program = "2.2.1"
solana-net-utils = "2.2.0"
tokio = { version = "1.36", features = ["full"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.11"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
config = "0.15.6"
reqwest = { version = "0.12.12", features = ["json"] }
spl-token = "7.0.0"
base64 = "0.22.1"
dotenv = "0.15.0"
bs58 = "0.5.1"
mpl-token-metadata = "5.1.0"
borsh = "1.5.5"
spl-associated-token-account = "6.0.0"
jito-sdk-rust = { git = "https://github.com/a00012025/jito-rust-rpc", branch = "master" }
bincode = "1.3.3"
rand = "0.8.5"
async-trait = "0.1.77"
chrono = { version = "0.4", features = ["serde"] }
teloxide = { version = "0.13", features = ["macros"] }
clap = { version = "4.4", features = ["derive"] }
dirs = "5.0"
scopeguard = "1.2.0"
once_cell = "1.19"
dashmap = "6.1.0"
actix-web = "4.4"
redis = { version = "0.24", features = ["tokio-comp"] }
hex = "0.4.3"
headless_chrome = "1.0.9"
lazy_static = "1.4.0"
alloy-rpc-client = "0.12.6"
alloy = { version = "0.12.6", features = ["full"] }
futures = "0.3"
futures-util = "0.3"
csv = "1.3.1"
ed25519-dalek = "2.1.1"
axum = "0.8.4"
libc = "0.2"

[[bin]]
name = "price_trader"
path = "src/bin/price_trader.rs"

[[bin]]
name = "track_wallet"
path = "src/bin/track_wallet.rs"

[[bin]]
name = "track_multi_smart"
path = "src/bin/track_multi_smart.rs"

[[bin]]
name = "telegram_bot"
path = "src/bin/telegram_bot.rs"

[[bin]]
name = "telegram_bot_bnb"
path = "src/bin/telegram_bot_bnb.rs"

[[bin]]
name = "api_server"
path = "src/bin/api_server.rs"

[[bin]]
name = "test_cf_clearance"
path = "src/bin/test_cf_clearance.rs"

[[bin]]
name = "pump_fun_buy"
path = "src/bin/pump_fun_buy.rs"

[[bin]]
name = "pump_fun_sell"
path = "src/bin/pump_fun_sell.rs"

[[bin]]
name = "token_hodlers"
path = "src/bin/token_hodlers.rs"

[[bin]]
name = "get_associated_addresses"
path = "src/bin/get_associated_addresses.rs"

[[bin]]
name = "analyze_wallet_relationships"
path = "src/bin/analyze_wallet_relationships.rs"

[[bin]]
name = "analyze_token_holders"
path = "src/bin/analyze_token_holders.rs"

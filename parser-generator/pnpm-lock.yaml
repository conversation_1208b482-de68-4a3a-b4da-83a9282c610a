lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@codama/nodes':
        specifier: ^1.3.0
        version: 1.3.0
      '@codama/nodes-from-anchor':
        specifier: ^1.2.0
        version: 1.2.0(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      '@codama/renderers-core':
        specifier: ^1.0.16
        version: 1.0.16
      '@codama/renderers-vixen-parser':
        specifier: ^1.0.15
        version: 1.0.15(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      '@codama/visitors-core':
        specifier: ^1.3.0
        version: 1.3.0
      codama:
        specifier: ^1.3.0
        version: 1.3.0

packages:

  '@codama/errors@1.3.0':
    resolution: {integrity: sha512-pYp/gOi1c/9btrybWvH2iK5jj7SOpMmyLm9EqbxrivV+lqzfEogU8IG+BLch9sqnuhPgg/PzCaB4sYd438UC/Q==}
    hasBin: true

  '@codama/node-types@1.3.0':
    resolution: {integrity: sha512-3dPbMXR/QgKUqXMMxc6PUi2f8Dq6eZ9P/v6+ChTQFLGhSQmqC1PsCbL77PuM7mf4IK9np1RXFqbC/EVpgLvbgA==}

  '@codama/nodes-from-anchor@1.2.0':
    resolution: {integrity: sha512-IiIgNnqxXAu1Qk37tdVx7zq1aqZuqRYyfq4ef+31ktaOyH+bsZex9eA0mzwkAow2Nhgj8d5N6qg+i7pwey7B3A==}

  '@codama/nodes@1.3.0':
    resolution: {integrity: sha512-Spf+Whm4jBLFxGPtJuDGmteGe+avoIDnh6rsByU1iJlYmcJJLjZayexFkvW8+1IeDclUMPQYBSj6SjuQEItLqQ==}

  '@codama/renderers-core@1.0.16':
    resolution: {integrity: sha512-IJshH6JsX7GUaYmC6KlOd5pLLyW1Yqd0I8B0pVWvvv9BfPNosC4t4RfusHSkYQePkIvs7CJ7YlwUywwW36Vt8A==}

  '@codama/renderers-rust@1.0.22':
    resolution: {integrity: sha512-ovK1UyFYt/oWW8W2B6ghe/nc1kvITakVQqVIlK21l2VHGo6yEFcdWvBRYn5VXMuwW0nMwPuC7Hv0eZmGLRR7xg==}

  '@codama/renderers-vixen-parser@1.0.15':
    resolution: {integrity: sha512-hOrAuz3QT+hY9B37KoWE3fMoC5JLuKLqIspD1TzODC+XBQo8e+M8jBippAJulkef05+udfUm0s/x//1dfOrC7A==}

  '@codama/validators@1.3.0':
    resolution: {integrity: sha512-s6rG3fJSvS7zc+fQ5yJN1NHlGQiut+P3Bpi5sQcEikjdWDDezu4OOgkxTh8ksTsCu8Rsif2hVzFYDLMcKjNrag==}

  '@codama/visitors-core@1.3.0':
    resolution: {integrity: sha512-Lldy0aOc882QYDa1IhjXhwpDsQE7oirBaebRddggXYFQs4+cvFROibHXBqG2npHPvQM4Mot6dJHQqffB/QL4iQ==}

  '@codama/visitors@1.3.0':
    resolution: {integrity: sha512-xcFae6NwC6Omr9tDm6P8rF3IDm5jWe2VMPomixaAc7+mpRhAWLbsg+FYCi0E09NvADo4C1tGo8U/SsvZLATUsQ==}

  '@noble/hashes@1.8.0':
    resolution: {integrity: sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A==}
    engines: {node: ^14.21.3 || >=16}

  '@solana/codecs-core@2.0.0-rc.4':
    resolution: {integrity: sha512-JIrTSps032mSE3wBxW3bXOqWfoy4CMy1CX/XeVCijyh5kLVxZTSDIdRTYdePdL1yzaOZF1Xysvt1DhOUgBdM+A==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5'

  '@solana/codecs-core@2.1.1':
    resolution: {integrity: sha512-iPQW3UZ2Vi7QFBo2r9tw0NubtH8EdrhhmZulx6lC8V5a+qjaxovtM/q/UW2BTNpqqHLfO0tIcLyBLrNH4HTWPg==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/codecs-data-structures@2.1.1':
    resolution: {integrity: sha512-OcR7FIhWDFqg6gEslbs2GVKeDstGcSDpkZo9SeV4bm2RLd1EZfxGhWW+yHZfHqOZiIkw9w+aY45bFgKrsLQmFw==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/codecs-numbers@2.0.0-rc.4':
    resolution: {integrity: sha512-ZJR7TaUO65+3Hzo3YOOUCS0wlzh17IW+j0MZC2LCk1R0woaypRpHKj4iSMYeQOZkMxsd9QT3WNvjFrPC2qA6Sw==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5'

  '@solana/codecs-numbers@2.1.1':
    resolution: {integrity: sha512-m20IUPJhPUmPkHSlZ2iMAjJ7PaYUvlMtFhCQYzm9BEBSI6OCvXTG3GAPpAnSGRBfg5y+QNqqmKn4QHU3B6zzCQ==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/codecs-strings@2.0.0-rc.4':
    resolution: {integrity: sha512-LGfK2RL0BKjYYUfzu2FG/gTgCsYOMz9FKVs2ntji6WneZygPxJTV5W98K3J8Rl0JewpCSCFQH3xjLSHBJUS0fA==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      fastestsmallesttextencoderdecoder: ^1.0.22
      typescript: '>=5'

  '@solana/codecs-strings@2.1.1':
    resolution: {integrity: sha512-uhj+A7eT6IJn4nuoX8jDdvZa7pjyZyN+k64EZ8+aUtJGt5Ft4NjRM8Jl5LljwYBWKQCgouVuigZHtTO2yAWExA==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      fastestsmallesttextencoderdecoder: ^1.0.22
      typescript: '>=5.3.3'

  '@solana/codecs@2.1.1':
    resolution: {integrity: sha512-89Fv22fZ5dNiXjOKh6I3U1D/lVO/dF/cPHexdiqjS5k5R5uKeK3506rhcnc4ciawQAoOkDwHzW+HitUumF2PJg==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/errors@2.0.0-rc.4':
    resolution: {integrity: sha512-0PPaMyB81keEHG/1pnyEuiBVKctbXO641M2w3CIOrYT/wzjunfF0FTxsqq9wYJeYo0AyiefCKGgSPs6wiY2PpQ==}
    engines: {node: '>=20.18.0'}
    hasBin: true
    peerDependencies:
      typescript: '>=5'

  '@solana/errors@2.1.1':
    resolution: {integrity: sha512-sj6DaWNbSJFvLzT8UZoabMefQUfSW/8tXK7NTiagsDmh+Q87eyQDDC9L3z+mNmx9b6dEf6z660MOIplDD2nfEw==}
    engines: {node: '>=20.18.0'}
    hasBin: true
    peerDependencies:
      typescript: '>=5.3.3'

  '@solana/options@2.1.1':
    resolution: {integrity: sha512-rnEExUGVOAV79kiFUEl/51gmSbBYxlcuw2VPnbAV/q53mIHoTgCwDD576N9A8wFftxaJHQFBdNuKiRrnU/fFHA==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      typescript: '>=5.3.3'

  a-sync-waterfall@1.0.1:
    resolution: {integrity: sha512-RYTOHHdWipFUliRFMCS4X2Yn2X8M87V/OpSqWzKKOGhzqyUxzyVmhHDH9sAvG+ZuQf/TAOFsLCpMw09I1ufUnA==}

  asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}

  chalk@5.4.1:
    resolution: {integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  codama@1.3.0:
    resolution: {integrity: sha512-rqnD0DRq7H3yJce3nDU1Kdy3Y/TsC/GirS4VazG7AEJ5UbR0fGIoThWWOpl2d5J1JmsaheY7APCY4PpnEZBoJA==}

  commander@12.1.0:
    resolution: {integrity: sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==}
    engines: {node: '>=18'}

  commander@13.1.0:
    resolution: {integrity: sha512-/rFeCpNJQbhSZjGVwO9RFV3xPqbnERS8MmIQzCtD/zl6gpJuV/bMLuN92oG3F7d8oDEHHRrujSXNUr8fpjntKw==}
    engines: {node: '>=18'}

  commander@5.1.0:
    resolution: {integrity: sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==}
    engines: {node: '>= 6'}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  fastestsmallesttextencoderdecoder@1.0.22:
    resolution: {integrity: sha512-Pb8d48e+oIuY4MaM64Cd7OW1gt4nxCHs7/ddPPZ/Ic3sg8yVGM7O9wDvZ7us6ScaUupzM+pfBolwtYhN1IxBIw==}

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  json-stable-stringify@1.3.0:
    resolution: {integrity: sha512-qtYiSSFlwot9XHtF9bD9c7rwKjr+RecWT//ZnPvSmEjpV5mmPOCN4j8UjY5hbjNkOwZ/jQv3J6R1/pL7RwgMsg==}
    engines: {node: '>= 0.4'}

  jsonify@0.0.1:
    resolution: {integrity: sha512-2/Ki0GcmuqSrgFyelQq9M05y7PS0mEwuIzrf3f1fPqkVDVRvZrPZtVSMHxdgo8Aq0sxAOb/cr2aqqA3LeWHVPg==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  nunjucks@3.2.4:
    resolution: {integrity: sha512-26XRV6BhkgK0VOxfbU5cQI+ICFUtMLixv1noZn1tGU38kQH5A5nmmbk/O45xdyBhD1esk47nKrY0mvQpZIhRjQ==}
    engines: {node: '>= 6.9.0'}
    hasBin: true
    peerDependencies:
      chokidar: ^3.3.0
    peerDependenciesMeta:
      chokidar:
        optional: true

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

snapshots:

  '@codama/errors@1.3.0':
    dependencies:
      '@codama/node-types': 1.3.0
      chalk: 5.4.1
      commander: 13.1.0

  '@codama/node-types@1.3.0': {}

  '@codama/nodes-from-anchor@1.2.0(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      '@codama/visitors': 1.3.0
      '@noble/hashes': 1.8.0
      '@solana/codecs': 2.1.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
    transitivePeerDependencies:
      - fastestsmallesttextencoderdecoder
      - typescript

  '@codama/nodes@1.3.0':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/node-types': 1.3.0

  '@codama/renderers-core@1.0.16':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      '@codama/visitors-core': 1.3.0

  '@codama/renderers-rust@1.0.22(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      '@codama/renderers-core': 1.0.16
      '@codama/visitors-core': 1.3.0
      '@solana/codecs-strings': 2.0.0-rc.4(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      nunjucks: 3.2.4
    transitivePeerDependencies:
      - chokidar
      - fastestsmallesttextencoderdecoder
      - typescript

  '@codama/renderers-vixen-parser@1.0.15(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      '@codama/renderers-core': 1.0.16
      '@codama/renderers-rust': 1.0.22(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      '@codama/visitors-core': 1.3.0
      '@solana/codecs-strings': 2.0.0-rc.4(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      nunjucks: 3.2.4
    transitivePeerDependencies:
      - chokidar
      - fastestsmallesttextencoderdecoder
      - typescript

  '@codama/validators@1.3.0':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      '@codama/visitors-core': 1.3.0

  '@codama/visitors-core@1.3.0':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      json-stable-stringify: 1.3.0

  '@codama/visitors@1.3.0':
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      '@codama/visitors-core': 1.3.0

  '@noble/hashes@1.8.0': {}

  '@solana/codecs-core@2.0.0-rc.4(typescript@5.8.3)':
    dependencies:
      '@solana/errors': 2.0.0-rc.4(typescript@5.8.3)
      typescript: 5.8.3

  '@solana/codecs-core@2.1.1(typescript@5.8.3)':
    dependencies:
      '@solana/errors': 2.1.1(typescript@5.8.3)
      typescript: 5.8.3

  '@solana/codecs-data-structures@2.1.1(typescript@5.8.3)':
    dependencies:
      '@solana/codecs-core': 2.1.1(typescript@5.8.3)
      '@solana/codecs-numbers': 2.1.1(typescript@5.8.3)
      '@solana/errors': 2.1.1(typescript@5.8.3)
      typescript: 5.8.3

  '@solana/codecs-numbers@2.0.0-rc.4(typescript@5.8.3)':
    dependencies:
      '@solana/codecs-core': 2.0.0-rc.4(typescript@5.8.3)
      '@solana/errors': 2.0.0-rc.4(typescript@5.8.3)
      typescript: 5.8.3

  '@solana/codecs-numbers@2.1.1(typescript@5.8.3)':
    dependencies:
      '@solana/codecs-core': 2.1.1(typescript@5.8.3)
      '@solana/errors': 2.1.1(typescript@5.8.3)
      typescript: 5.8.3

  '@solana/codecs-strings@2.0.0-rc.4(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)':
    dependencies:
      '@solana/codecs-core': 2.0.0-rc.4(typescript@5.8.3)
      '@solana/codecs-numbers': 2.0.0-rc.4(typescript@5.8.3)
      '@solana/errors': 2.0.0-rc.4(typescript@5.8.3)
      fastestsmallesttextencoderdecoder: 1.0.22
      typescript: 5.8.3

  '@solana/codecs-strings@2.1.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)':
    dependencies:
      '@solana/codecs-core': 2.1.1(typescript@5.8.3)
      '@solana/codecs-numbers': 2.1.1(typescript@5.8.3)
      '@solana/errors': 2.1.1(typescript@5.8.3)
      fastestsmallesttextencoderdecoder: 1.0.22
      typescript: 5.8.3

  '@solana/codecs@2.1.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)':
    dependencies:
      '@solana/codecs-core': 2.1.1(typescript@5.8.3)
      '@solana/codecs-data-structures': 2.1.1(typescript@5.8.3)
      '@solana/codecs-numbers': 2.1.1(typescript@5.8.3)
      '@solana/codecs-strings': 2.1.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      '@solana/options': 2.1.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - fastestsmallesttextencoderdecoder

  '@solana/errors@2.0.0-rc.4(typescript@5.8.3)':
    dependencies:
      chalk: 5.4.1
      commander: 12.1.0
      typescript: 5.8.3

  '@solana/errors@2.1.1(typescript@5.8.3)':
    dependencies:
      chalk: 5.4.1
      commander: 13.1.0
      typescript: 5.8.3

  '@solana/options@2.1.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)':
    dependencies:
      '@solana/codecs-core': 2.1.1(typescript@5.8.3)
      '@solana/codecs-data-structures': 2.1.1(typescript@5.8.3)
      '@solana/codecs-numbers': 2.1.1(typescript@5.8.3)
      '@solana/codecs-strings': 2.1.1(fastestsmallesttextencoderdecoder@1.0.22)(typescript@5.8.3)
      '@solana/errors': 2.1.1(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - fastestsmallesttextencoderdecoder

  a-sync-waterfall@1.0.1: {}

  asap@2.0.6: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  chalk@5.4.1: {}

  codama@1.3.0:
    dependencies:
      '@codama/errors': 1.3.0
      '@codama/nodes': 1.3.0
      '@codama/validators': 1.3.0
      '@codama/visitors': 1.3.0

  commander@12.1.0: {}

  commander@13.1.0: {}

  commander@5.1.0: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  fastestsmallesttextencoderdecoder@1.0.22: {}

  function-bind@1.1.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  gopd@1.2.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-symbols@1.1.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  isarray@2.0.5: {}

  json-stable-stringify@1.3.0:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      isarray: 2.0.5
      jsonify: 0.0.1
      object-keys: 1.1.1

  jsonify@0.0.1: {}

  math-intrinsics@1.1.0: {}

  nunjucks@3.2.4:
    dependencies:
      a-sync-waterfall: 1.0.1
      asap: 2.0.6
      commander: 5.1.0

  object-keys@1.1.1: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  typescript@5.8.3: {}

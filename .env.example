# Solana Bot Environment Variables Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SOLANA CONFIGURATION
# =============================================================================

# Solana RPC URL - Required for blockchain interactions
# You can use public endpoints or premium services like Alchemy, QuickNode, etc.
RPC_URL=https://api.mainnet-beta.solana.com

# Private key for Solana wallet operations (Base58 encoded)
# SECURITY WARNING: Never commit this to version control!
PRIVATE_KEY=your_base58_encoded_private_key_here

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================

# Primary Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHANNEL_ID=your_telegram_channel_id_here

# Secondary Telegram Bot Configuration (for multi-smart tracking)
TELEGRAM_BOT_TOKEN_2=your_secondary_telegram_bot_token_here
TELEGRAM_CHANNEL_ID_2=your_secondary_telegram_channel_id_here

# BNB Chain Telegram Bot Configuration
BNB_TELEGRAM_BOT_TOKEN=your_bnb_telegram_bot_token_here
BNB_TELEGRAM_CHANNEL_ID=your_bnb_telegram_channel_id_here

# =============================================================================
# TELEGRAM API CONFIGURATION (for data extraction)
# =============================================================================

# Telegram API credentials for data extraction from chats
# Get these from https://my.telegram.org/apps
TELEGRAM_API_ID=your_telegram_api_id_here
TELEGRAM_API_HASH=your_telegram_api_hash_here
TELEGRAM_PHONE_NUMBER=your_telegram_phone_number_here

# Chat names for monitoring (optional, defaults provided)
BLOOM_CHAT_NAME=Bloom Solana
BSC_CHAT_NAME=DBot - Bsc Trading Bot

# =============================================================================
# MULTI-CHAIN CONFIGURATION
# =============================================================================

# BNB Chain RPC URL
BNB_RPC_URL=https://bsc-dataseed.binance.org/

# =============================================================================
# REDIS CONFIGURATION (for API server)
# =============================================================================

# Redis connection details for caching and state management
REDIS_HOST=localhost
REDIS_PORT=6379

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Log level for debugging (optional)
# RUST_LOG=debug

# Additional RPC URLs for failover (optional)
# BACKUP_RPC_URL=https://your-backup-rpc-url.com

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit your actual .env file to version control
# 2. Keep your private keys secure and never share them
# 3. Use environment-specific configurations for different deployments
# 4. Consider using a secrets management service for production
# 5. Regularly rotate your API keys and tokens

# =============================================================================
# USAGE EXAMPLES
# =============================================================================

# To run the main trading bot:
# cargo run --bin telegram_bot

# To run the API server:
# cargo run --bin api_server

# To analyze a specific token:
# cargo run --bin analyze_token_holders -- --token <TOKEN_MINT_ADDRESS>

# To track smart money wallets:
# cargo run --bin follow_smart

# To run Python data extraction scripts:
# cd py && python combined_analysis.py

resource "google_compute_network" "vpc" {
  name                    = "solana-data-vpc"
  auto_create_subnetworks = false
  project                 = var.project_id
}

resource "google_compute_subnetwork" "subnet" {
  name          = "solana-data-subnet"
  ip_cidr_range = "10.0.0.0/24"
  region        = var.region
  network       = google_compute_network.vpc.id
  project       = var.project_id

  private_ip_google_access = true

  secondary_ip_range {
    range_name    = "pods"
    ip_cidr_range = "**********/20"
  }

  secondary_ip_range {
    range_name    = "services"
    ip_cidr_range = "***********/24"
  }
}

# Create Cloud Router
resource "google_compute_router" "router" {
  name    = "solana-data-router"
  region  = var.region
  network = google_compute_network.vpc.id
  project = var.project_id
}

# Create Cloud NAT
resource "google_compute_router_nat" "nat" {
  name                               = "solana-data-nat"
  router                            = google_compute_router.router.name
  region                            = google_compute_router.router.region
  nat_ip_allocate_option            = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}

resource "google_compute_global_address" "private_ip_address" {
  name          = "solana-data-private-ip"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = google_compute_network.vpc.id
  project       = var.project_id
}

resource "google_service_networking_connection" "private_vpc_connection" {
  network                 = google_compute_network.vpc.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_ip_address.name]
}

# Allow internal traffic within the VPC
resource "google_compute_firewall" "allow_internal" {
  name    = "allow-internal"
  network = google_compute_network.vpc.name
  project = var.project_id

  allow {
    protocol = "tcp"
  }

  allow {
    protocol = "udp"
  }

  allow {
    protocol = "icmp"
  }

  source_ranges = ["10.0.0.0/24", "**********/20", "***********/24"]
}

# Allow traffic from GKE to Cloud SQL
resource "google_compute_firewall" "allow_gke_to_cloudsql" {
  name    = "allow-gke-to-cloudsql"
  network = google_compute_network.vpc.name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["5432"]
  }

  source_ranges = [
    google_compute_subnetwork.subnet.ip_cidr_range,
    google_compute_subnetwork.subnet.secondary_ip_range[0].ip_cidr_range,
    google_compute_subnetwork.subnet.secondary_ip_range[1].ip_cidr_range
  ]

  target_tags = ["cloudsql"]
}

# Allow traffic from default network to Cloud SQL
resource "google_compute_firewall" "allow_default_to_cloudsql" {
  name    = "allow-default-to-cloudsql"
  network = google_compute_network.vpc.name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["5432"]
  }

  source_ranges = ["*********/14", "************/20"] # GKE cluster and services CIDR
  target_tags   = ["cloudsql"]
}

# Peering from your GKE VPC (solana-data-vpc) TO the default VPC
resource "google_compute_network_peering" "peering_to_default" {
  name                 = "peering-solana-data-to-default"
  network              = google_compute_network.vpc.id # This is solana-data-vpc
  peer_network         = "projects/${var.project_id}/global/networks/default"
  export_custom_routes = true
  import_custom_routes = true
}

# Peering from the default VPC BACK TO your GKE VPC (solana-data-vpc)
resource "google_compute_network_peering" "peering_from_default" {
  name                 = "peering-default-to-solana-data"
  network              = "projects/${var.project_id}/global/networks/default"
  peer_network         = google_compute_network.vpc.id # Peering TO solana-data-vpc
  export_custom_routes = true
  import_custom_routes = true
}

# Firewall rule IN THE DEFAULT VPC to allow ingress traffic from GKE pods
resource "google_compute_firewall" "allow_gke_to_gce_grpc" {
  name          = "allow-gke-to-solana-grpc"
  network       = "default" # Correctly placed in the 'default' VPC
  project       = var.project_id
  direction     = "INGRESS"
  priority      = 1000

  allow {
    protocol = "tcp"
    ports    = ["10001"] # The gRPC port
  }

  source_ranges = [
    google_compute_subnetwork.subnet.ip_cidr_range,
    google_compute_subnetwork.subnet.secondary_ip_range[0].ip_cidr_range, # Pods
    google_compute_subnetwork.subnet.secondary_ip_range[1].ip_cidr_range  # Services
  ]

  target_tags = ["solana-validator"]
}

# Firewall rule IN THE GKE VPC to allow egress traffic
resource "google_compute_firewall" "allow_gke_egress_to_default" {
  name        = "allow-gke-egress-to-default"
  network     = google_compute_network.vpc.name # In solana-data-vpc
  project     = var.project_id
  direction   = "EGRESS"
  priority    = 1000

  allow {
    protocol = "all" # Allows all protocols
  }

  destination_ranges = ["**********/20"] # Range for default VPC
}

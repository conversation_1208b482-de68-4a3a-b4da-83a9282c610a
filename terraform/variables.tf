variable "project_id" {
  description = "The project ID to deploy to"
  type        = string
}

variable "project_number" {
  description = "The GCP project number"
  type        = string
}

variable "region" {
  description = "The region to deploy to"
  type        = string
  default     = "asia-northeast1"
}

variable "environment" {
  description = "The environment to deploy to"
  type        = string
}

variable "credentials_path" {
  description = "Path to the GCP service account credentials file"
  type        = string
}
resource "random_password" "db_password" {
  length  = 16
  special = false
}

output "db_password" {
  description = "The password for the database user"
  value       = random_password.db_password.result
  sensitive   = true
}

output "db_instance_name" {
  description = "The name of the database instance"
  value       = google_sql_database_instance.solana_data.name
}

output "db_connection_name" {
  description = "The connection name of the database instance"
  value       = google_sql_database_instance.solana_data.connection_name
}

output "db_private_ip" {
  description = "The private IP address of the database instance"
  value       = google_sql_database_instance.solana_data.private_ip_address
}

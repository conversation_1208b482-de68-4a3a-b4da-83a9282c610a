resource "google_pubsub_topic" "solana_events" {
  name = "solana-events"

  labels = {
    environment = var.environment
    project     = var.project_id
    event_type  = "events"
  }

  message_retention_duration = "604800s" # 7 days
}

resource "google_pubsub_subscription" "solana_events_sub" {
  name                 = "solana-events-sub"
  topic                = google_pubsub_topic.solana_events.name
  ack_deadline_seconds = 10

  message_retention_duration = "604800s" # 7 days
  retain_acked_messages      = false
}

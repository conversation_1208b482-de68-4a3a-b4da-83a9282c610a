# KG Solana Data Terraform Config

This Terraform configuration manages GCP Pub/Sub topics and related infrastructure for the KG Solana Data stream processing system.

## 🏗️ Architecture

This configuration creates:

- **8 Pub/Sub Topics** based on the event proto definitions:
  - `solana-new-pairs` - New trading pair events
  - `solana-trades` - Trading events
  - `solana-liquidity-changes` - Liquidity changes
  - `solana-sol-transfers` - Native SOL transfers
  - `solana-token-transfers` - SPL token transfers
  - `solana-burns` - Token burn events
  - `solana-limit-orders` - Limit order events
  - `solana-account-balance-updates` - Balance update events
- **Dead Letter Topic** - For failed message processing
- **Service Account** - For Pub/Sub operations with appropriate IAM roles
- **API Enablement** - Required GCP APIs (Pub/Sub, IAM)

## 🚀 Setup

### 1. Install Terraform

```bash
brew tap hashicorp/tap
brew install hashicorp/tap/terraform
```

### 2. Setup Credentials

Create a `credentials` directory in the terraform folder and place your service account key files:

```
terraform/
├── credentials/
│   ├── kg-solana-data-dev-terraform-cli.json
│   └── kg-solana-data-prod-terraform-cli.json
└── ...
```

### 3. Initialize Terraform

```bash
cd terraform
PROJECT=kg-solana-data make init
```

This will:

- Initialize Terraform
- Create dev and prod workspaces
- Set up the backend

## 🔧 Usage

### Development Environment

**Dry run (plan) development changes:**

```bash
PROJECT=kg-solana-data make dev-dry-run
```

**Apply development environment:**

```bash
PROJECT=kg-solana-data make dev
```

### Production Environment

**Dry run (plan) production changes:**

```bash
PROJECT=kg-solana-data make prod-dry-run
```

**Apply production environment:**

```bash
PROJECT=kg-solana-data make prod
```

### Other Commands

**Lint configuration:**

```bash
make lint
```

**Format code:**

```bash
make format
```

**Destroy environment:**

```bash
make destroy-dev
# or
make destroy-prod
```

## 📁 Project Structure

```
terraform/
├── main.tf              # Main Terraform configuration
├── variables.tf         # Variable definitions
├── outputs.tf          # Output definitions
├── dev.tfvars          # Development environment variables
├── prod.tfvars         # Production environment variables
├── Makefile            # Build automation
├── README.md           # This file
└── credentials/        # Service account keys (create this)
    ├── kg-solana-data-dev-terraform-cli.json
    └── kg-solana-data-prod-terraform-cli.json
```

## 🔑 GCP Service Account Setup

Create service accounts for Terraform operations:

### 1. Create Project (if needed)

```bash
gcloud projects create kg-solana-data-dev --name="KG Solana Data Dev"
gcloud projects create kg-solana-data-prod --name="KG Solana Data Prod"
```

### 2. Enable Required Services

```bash
# For dev
gcloud config set project kg-solana-data-dev
gcloud services enable pubsub.googleapis.com iam.googleapis.com cloudresourcemanager.googleapis.com

# For prod
gcloud config set project kg-solana-data-prod
gcloud services enable pubsub.googleapis.com iam.googleapis.com cloudresourcemanager.googleapis.com
```

### 3. Create Service Account

```bash
# For dev
gcloud iam service-accounts create terraform-cli \
  --display-name "Terraform CLI Service Account" \
  --project kg-solana-data-dev

# Grant necessary permissions
gcloud projects add-iam-policy-binding kg-solana-data-dev \
  --member serviceAccount:<EMAIL> \
  --role roles/owner

# Download key
gcloud iam service-accounts keys create credentials/kg-solana-data-dev-terraform-cli.json \
  --iam-account <EMAIL>
```

Repeat similar steps for production environment.

## 📊 Outputs

After applying, you'll get:

- **pubsub_topics**: Map of all created topic names
- **service_account_email**: Service account for Pub/Sub operations
- **project_id**: GCP Project ID
- **environment**: Environment name

## 🔍 Monitoring

Topics are created with:

- **Message retention**: 7 days (30 days for dead letter)
- **Labels**: Environment, project, and event type
- **Dead letter policy**: Available for failed message handling

## 🛠️ Customization

### Adding New Topics

1. Add the topic to `main.tf`:

```hcl
resource "google_pubsub_topic" "new_topic" {
  name = "new-topic-name"
  # ... configuration
}
```

2. Add to outputs in `outputs.tf`
3. Update the README documentation

### Changing Retention Policies

Modify the `message_retention_duration` in topic resources:

```hcl
message_retention_duration = "604800s" # 7 days
```

## 🚨 Important Notes

- **State Management**: Uses Terraform workspaces for environment separation
- **Credentials**: Never commit credential files to git
- **Destruction**: Be careful with destroy commands in production
- **API Limits**: Be aware of GCP Pub/Sub quotas and limits
- **Monitoring**: Set up monitoring and alerting for topic metrics

## 📚 Useful GCP Commands

**List topics:**

```bash
gcloud pubsub topics list
```

**Publish test message:**

```bash
gcloud pubsub topics publish solana-trades --message='{"test": "message"}'
```

**List subscriptions:**

```bash
gcloud pubsub subscriptions list
```

# Terraform files
*.tfstate
*.tfstate.*
*.tfplan
*.tfplan.*

# Crash log files
crash.log
crash.*.log

# Exclude sensitive .tfvars files, but allow environment configs
# *.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Credentials and sensitive files
credentials/
*.json
!terraform.json

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Backup files
*.backup

# Temporary files
.DS_Store
Thumbs.db 
# Create GKE service account
resource "google_service_account" "gke_sa" {
  account_id   = "gke-gsa"
  display_name = "GKE Service Account"
  project      = var.project_id
}

# Grant PubSub permissions to the GKE service account
resource "google_project_iam_member" "gke_pubsub_publisher" {
  project = var.project_id
  role    = "roles/pubsub.publisher"
  member  = "serviceAccount:${google_service_account.gke_sa.email}"
}

resource "google_project_iam_member" "gke_pubsub_subscriber" {
  project = var.project_id
  role    = "roles/pubsub.subscriber"
  member  = "serviceAccount:${google_service_account.gke_sa.email}"
}

# Grant Cloud Logging permissions to the GKE service account
resource "google_project_iam_member" "gke_logging_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.gke_sa.email}"
}

# Grant Monitoring Viewer permissions to the custom metrics stackdriver adapter service account
resource "google_project_iam_member" "custom_metrics_monitoring_viewer" {
  project = var.project_id
  role    = "roles/monitoring.viewer"
  member  = "principal://iam.googleapis.com/projects/${var.project_number}/locations/global/workloadIdentityPools/${var.project_id}.svc.id.goog/subject/ns/custom-metrics/sa/custom-metrics-stackdriver-adapter"
}

resource "google_iam_workload_identity_pool" "github_pool" {
  project                   = var.project_id
  workload_identity_pool_id = "github-actions-pool"
  display_name              = "GitHub Actions Pool"
  description               = "Trust GitHub Actions OIDC tokens"
}

resource "google_iam_workload_identity_pool_provider" "github_provider" {
  project                            = var.project_id
  workload_identity_pool_id          = google_iam_workload_identity_pool.github_pool.workload_identity_pool_id
  workload_identity_pool_provider_id = "github-provider"
  display_name                       = "GitHub Actions Provider"
  
  # This tells Google Cloud to trust OIDC tokens from GitHub.
  oidc {
    issuer_uri = "https://token.actions.githubusercontent.com"
  }
  
  # Attribute mapping lets you define which GitHub repos can authenticate.
  # Here, we allow any repository under your specified owner.
  attribute_mapping = {
    "google.subject"       = "assertion.sub"
    "attribute.actor"      = "assertion.actor"
    "attribute.repository" = "assertion.repository"
  }

  attribute_condition = "attribute.repository == 'kryptogo/kg-solana-data'"
}

# Grant the GKE service account permission to be impersonated by GitHub Actions.
# This is the crucial link between GitHub and your Google Service Account.
resource "google_service_account_iam_binding" "gke_sa_workload_identity_user" {
  service_account_id = "projects/${var.project_id}/serviceAccounts/<EMAIL>"
  role               = "roles/iam.workloadIdentityUser"
  members = [
    "principalSet://iam.googleapis.com/projects/${var.project_number}/locations/global/workloadIdentityPools/${google_iam_workload_identity_pool.github_pool.workload_identity_pool_id}/attribute.repository/kryptogo/kg-solana-data"
  ]
}

# Grant GKE Hub Viewer role to the GitHub Actions service account
resource "google_project_iam_member" "github_actions_gke_hub_viewer" {
  project = var.project_id
  role    = "roles/gkehub.viewer"
  member  = "serviceAccount:<EMAIL>"
}

# Grant GKE Hub Service Agent role to the GitHub Actions service account
resource "google_project_iam_member" "github_actions_gke_hub_service_agent" {
  project = var.project_id
  role    = "roles/gkehub.serviceAgent"
  member  = "serviceAccount:<EMAIL>"
}

.PHONY: help init lint plan apply destroy dev prod dev-dry-run prod-dry-run

help:
	@echo "Available commands:"
	@echo "  init         - Initialize Terraform for specified PROJECT"
	@echo "  lint         - Lint Terraform configuration"
	@echo "  prod         - Apply production environment"
	@echo "  prod-dry-run - Plan production environment changes"
	@echo ""
	@echo "Usage examples:"
	@echo "  make init"
	@echo "  make prod"

init:
	terraform init
	terraform workspace new prod 2>/dev/null || terraform workspace select prod

lint:
	@echo "Linting Terraform configuration..."
	terraform fmt -check -recursive
	terraform validate

prod-dry-run:
	@echo "Planning production environment changes..."
	export GOOGLE_API_USE_IPV4=true
	terraform workspace select prod
	terraform plan -var-file=prod.tfvars

prod:
	@echo "Applying production environment..."
	export GOOGLE_API_USE_IPV4=true
	terraform workspace select prod
	terraform apply -var-file=prod.tfvars

format:
	@echo "Formatting Terraform configuration..."
	terraform fmt -recursive

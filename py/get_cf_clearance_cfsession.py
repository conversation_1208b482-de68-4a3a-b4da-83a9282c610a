#!/usr/bin/env python3
import sys
import json
import time
import os
import traceback

import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hai<PERSON>

def log(message):
    """Print debug messages to stderr"""
    print(message, file=sys.stderr)

def get_cf_clearance_with_undetected(url="https://solscan.io/"):
    """Get Cloudflare clearance token using undetected-chromedriver"""
    try:
        # Configure Chrome options
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        display = os.environ.get("DISPLAY", None)
        if display:
            options.add_argument(f"--display={display}")
        
        # Create a new instance of the undetected Chrome driver
        driver = uc.Chrome(headless=False, use_subprocess=True, options=options)
        
        # Set page load timeout
        driver.set_page_load_timeout(30)
        
        try:
            # Navigate to the URL
            driver.get(url)
            
            # Wait for Cloudflare challenge page if it appears
            try:
                # Check for the Cloudflare challenge stage
                WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.ID, "challenge-stage"))
                )
                log("Cloudflare challenge detected, attempting to solve...")
                
                # Try to handle checkbox challenge
                handle_checkbox_challenge(driver)
                
                # Try to handle Turnstile challenge
                handle_turnstile_challenge(driver)
                
                # Wait for the challenge to complete
                log("Waiting for Cloudflare challenge to complete...")
                time.sleep(10)
            except TimeoutException:
                # No challenge detected, continue
                log("No Cloudflare challenge detected")
            
            # Wait for the page to load
            time.sleep(5)
            
            # Get all cookies
            cookies = driver.get_cookies()
            
            # Find the cf_clearance cookie
            cf_clearance = ""
            user_agent = driver.execute_script("return navigator.userAgent")
            
            for cookie in cookies:
                if cookie["name"] == "cf_clearance":
                    cf_clearance = cookie["value"]
                    break
            
            # If we didn't find the cookie, wait longer and try again
            if not cf_clearance:
                log("cf_clearance cookie not found, waiting longer...")
                time.sleep(15)
                
                # Try to refresh the page if cookie not found
                driver.refresh()
                time.sleep(10)
                
                # Check cookies again
                cookies = driver.get_cookies()
                for cookie in cookies:
                    if cookie["name"] == "cf_clearance":
                        cf_clearance = cookie["value"]
                        break
            
            # Return the results
            result = {
                "success": True if cf_clearance else False,
                "cf_clearance": cf_clearance,
                "user_agent": user_agent
            }
            
            if not cf_clearance:
                result["error"] = "Could not obtain cf_clearance cookie"
            
            return result
        finally:
            # Always close the driver
            driver.quit()
    except Exception as e:
        log(f"Undetected-chromedriver error: {e}")
        log(traceback.format_exc())
        return {
            "success": False,
            "error": str(e)
        }

def handle_checkbox_challenge(driver):
    """Handle the Cloudflare checkbox challenge with improved methods"""
    try:
        # First try: Look for all iframes and check each one
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        log(f"Found {len(iframes)} iframes")
        
        for iframe in iframes:
            try:
                iframe_src = iframe.get_attribute("src")
                log(f"Checking iframe: {iframe_src}")
                
                if "cloudflare" in iframe_src.lower() or "turnstile" in iframe_src.lower() or "challenges" in iframe_src.lower():
                    driver.switch_to.frame(iframe)
                    
                    # Try multiple selectors for the checkbox
                    checkbox_selectors = [
                        "//input[@type='checkbox']",
                        "//div[contains(@class, 'checkbox')]",
                        "//div[contains(@class, 'rc-anchor-checkbox')]",
                        "//span[@role='checkbox']",
                        "//div[@role='checkbox']",
                        "//div[contains(@class, 'cf-checkbox')]"
                    ]
                    
                    for selector in checkbox_selectors:
                        try:
                            checkboxes = driver.find_elements(By.XPATH, selector)
                            if checkboxes:
                                for checkbox in checkboxes:
                                    if checkbox.is_displayed() and checkbox.is_enabled():
                                        # Try different click methods
                                        try:
                                            # Method 1: Direct click
                                            checkbox.click()
                                            log(f"Clicked checkbox with selector: {selector}")
                                        except:
                                            try:
                                                # Method 2: JavaScript click
                                                driver.execute_script("arguments[0].click();", checkbox)
                                                log(f"Clicked checkbox with JavaScript: {selector}")
                                            except:
                                                try:
                                                    # Method 3: ActionChains click
                                                    ActionChains(driver).move_to_element(checkbox).click().perform()
                                                    log(f"Clicked checkbox with ActionChains: {selector}")
                                                except Exception as e:
                                                    log(f"Failed to click checkbox: {e}")
                                        
                                        # Wait a moment after clicking
                                        time.sleep(2)
                        except Exception as e:
                            log(f"Error with selector {selector}: {e}")
                    
                    # Switch back to main content
                    driver.switch_to.default_content()
            except Exception as e:
                log(f"Error processing iframe: {e}")
                driver.switch_to.default_content()
        
        # Second try: Look for shadow DOM elements (newer Cloudflare challenges)
        try:
            # Execute JavaScript to find and click elements in shadow DOM
            script = """
            function findAndClickCaptcha() {
                // Find all shadow roots
                const getAllShadowRoots = (root) => {
                    const shadowRoots = [];
                    const walkTree = (node) => {
                        if (node.shadowRoot) {
                            shadowRoots.push(node.shadowRoot);
                            Array.from(node.shadowRoot.querySelectorAll('*')).forEach(walkTree);
                        }
                        Array.from(node.querySelectorAll('*')).forEach(walkTree);
                    };
                    walkTree(root);
                    return shadowRoots;
                };
                
                const shadows = getAllShadowRoots(document);
                console.log("Found " + shadows.length + " shadow roots");
                
                // Look for checkboxes or buttons in shadow DOM
                for (const shadow of shadows) {
                    // Try to find checkboxes
                    const checkboxes = shadow.querySelectorAll('input[type="checkbox"], [role="checkbox"], .checkbox');
                    for (const checkbox of checkboxes) {
                        if (checkbox.offsetParent !== null) {
                            console.log("Found checkbox in shadow DOM");
                            checkbox.click();
                            return true;
                        }
                    }
                    
                    // Try to find buttons
                    const buttons = shadow.querySelectorAll('button, [role="button"]');
                    for (const button of buttons) {
                        if (button.offsetParent !== null) {
                            console.log("Found button in shadow DOM");
                            button.click();
                            return true;
                        }
                    }
                }
                
                return false;
            }
            return findAndClickCaptcha();
            """
            result = driver.execute_script(script)
            if result:
                log("Clicked element in shadow DOM")
        except Exception as e:
            log(f"Error with shadow DOM approach: {e}")
            
    except Exception as e:
        log(f"Error in handle_checkbox_challenge: {e}")

def handle_turnstile_challenge(driver):
    """Handle the Cloudflare Turnstile challenge"""
    try:
        # Look for Turnstile iframe
        turnstile_iframes = driver.find_elements(By.XPATH, "//iframe[contains(@src, 'challenges.cloudflare.com')]")
        if not turnstile_iframes:
            return
        
        log("Detected Turnstile challenge, attempting to solve...")
        
        # Try each iframe
        for iframe in turnstile_iframes:
            try:
                driver.switch_to.frame(iframe)
                
                # Look for interactive elements with expanded selectors
                interactive_elements = driver.find_elements(
                    By.XPATH, 
                    "//div[contains(@class, 'task-card')] | //div[contains(@class, 'button')] | " +
                    "//div[contains(@class, 'tile')] | //div[contains(@class, 'challenge')] | " +
                    "//button | //div[@role='button'] | //span[@role='button']"
                )
                
                if interactive_elements:
                    for element in interactive_elements:
                        try:
                            if element.is_displayed():
                                # Try different click methods
                                try:
                                    # Method 1: Direct click
                                    element.click()
                                    log(f"Clicked Turnstile element directly: {element.get_attribute('class')}")
                                except:
                                    try:
                                        # Method 2: JavaScript click
                                        driver.execute_script("arguments[0].click();", element)
                                        log(f"Clicked Turnstile element with JavaScript: {element.get_attribute('class')}")
                                    except:
                                        try:
                                            # Method 3: ActionChains click
                                            ActionChains(driver).move_to_element(element).click().perform()
                                            log(f"Clicked Turnstile element with ActionChains: {element.get_attribute('class')}")
                                        except Exception as e:
                                            log(f"Failed to click Turnstile element: {e}")
                                
                                # Wait a moment after clicking
                                time.sleep(2)
                        except:
                            continue
                
                # Switch back to main content
                driver.switch_to.default_content()
            except Exception as e:
                log(f"Error interacting with Turnstile iframe: {e}")
                driver.switch_to.default_content()
    except Exception as e:
        log(f"Error handling Turnstile challenge: {e}")

def get_cf_clearance(url="https://solscan.io/"):
    """
    Get Cloudflare clearance token using the best available method.
    
    Args:
        url: The URL to get the clearance token for
        
    Returns:
        A JSON string containing the clearance token and user agent
    """
    result = get_cf_clearance_with_undetected(url)
    
    # Print only the JSON result to stdout
    print(json.dumps(result))
    return None

if __name__ == "__main__":
    # If URL is provided as argument, use it, otherwise use default
    url = sys.argv[1] if len(sys.argv) > 1 else "https://solscan.io/"
    get_cf_clearance(url) 
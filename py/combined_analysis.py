from telethon import TelegramClient
import re
import csv
import os
import json
import asyncio
import subprocess
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd
from dotenv import load_dotenv
from config import bsc_wallets, sol_wallets
from telegram_extractor import extract_telegram_data, extract_bsc_telegram_data, extract_trade_info, extract_bsc_trade_info
from wallet_data import get_wallet_holdings, get_bsc_wallet_holdings, get_wallet_pnl, get_bsc_wallet_pnl
from data_processor import process_holdings, process_trades_and_holdings, process_wallet_analysis
import argparse

# Load environment variables
load_dotenv()

# Telegram API credentials from environment variables
api_id = os.getenv('TELEGRAM_API_ID')
api_hash = os.getenv('TELEGRAM_API_HASH')
phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')

# Validate required environment variables
if not all([api_id, api_hash, phone_number]):
    raise ValueError("Missing required environment variables. Please set TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE_NUMBER in .env file")

def convert_kmb_to_number(value_str: str) -> float:
    """Convert K/M/B suffix to actual number"""
    value_str = value_str.strip().upper()
    if value_str.endswith('K'):
        return float(value_str[:-1]) * 1000
    elif value_str.endswith('M'):
        return float(value_str[:-1]) * 1000000
    elif value_str.endswith('B'):
        return float(value_str[:-1]) * 1000000000
    return float(value_str)

async def main(days: int = 7):
    # Extract Telegram data
    # print(f"Extracting Telegram data from the last {days} days...")
    sol_trades = await extract_telegram_data(days)
    # bsc_trades = await extract_bsc_telegram_data(days)
    
    # Combine trades
    # all_trades = sol_trades + bsc_trades
    all_trades = sol_trades
    trades_df = pd.DataFrame(all_trades)
    
    # Get wallet data for both chains
    # print("Getting Solana wallet holdings...")
    sol_holdings = []
    for wallet in sol_wallets:
        holdings = await get_wallet_holdings(wallet, days)
        # Add wallet address to each holding
        for holding in holdings:
            holding['wallet_address'] = wallet
        sol_holdings.extend(holdings)
    
    # print("Getting BSC wallet holdings...")
    # bsc_holdings = []
    # for wallet in bsc_wallets:
    #     holdings = await get_bsc_wallet_holdings(wallet, days)
    #     # Add wallet address to each holding
    #     for holding in holdings:
    #         holding['wallet_address'] = wallet
    #     bsc_holdings.extend(holdings)
    
    # print("Getting wallet PNL data...")
    sol_pnl = []
    for wallet in sol_wallets:
        pnl = await get_wallet_pnl(wallet, days)
        if pnl:
            pnl['wallet_address'] = wallet
            sol_pnl.append(pnl)
    
    # bsc_pnl = []
    # for wallet in bsc_wallets:
    #     pnl = await get_bsc_wallet_pnl(wallet, days)
    #     if pnl:
    #         pnl['wallet_address'] = wallet
    #         bsc_pnl.append(pnl)
    
    # Process holdings
    # print("Processing holdings...")
    sol_positions = process_holdings(sol_holdings)
    # bsc_positions = process_holdings(bsc_holdings)
    bsc_positions = []
    
    # Convert timestamps to datetime
    for pos in sol_positions + bsc_positions:
        if pos.get('last_active_timestamp'):
            pos['last_active_date'] = datetime.fromtimestamp(pos['last_active_timestamp'])
        if pos.get('start_holding_at'):
            pos['start_date'] = datetime.fromtimestamp(pos['start_holding_at'])
        if pos.get('end_holding_at'):
            pos['end_date'] = datetime.fromtimestamp(pos['end_holding_at'])
    
    # Process trades and holdings
    # print("\nProcessing trades and holdings...")
    merged_df = process_trades_and_holdings(trades_df, sol_positions, bsc_positions, sol_pnl, [])
    
    # Save to CSV
    output_file = 'combined_analysis.csv'
    merged_df.to_csv(output_file, index=False)
    print(f"Saved combined analysis to {output_file}")
    
    # Process wallet analysis
    if not trades_df.empty:
        # print("\nProcessing wallet analysis...")
        wallet_analysis = process_wallet_analysis(trades_df, sol_positions, bsc_positions)
        
        # Save wallet analysis to CSV
        wallet_analysis_file = 'target_wallet_analysis.csv'
        wallet_analysis.to_csv(wallet_analysis_file, index=False)
        # print(f"\nSaved target wallet analysis to {wallet_analysis_file}")
        
        # Print summary
        # print(f"\nFound {len(wallet_analysis)} unique target wallets")
        # print("\nTop 5 wallets by total profit:")
        # print(wallet_analysis.head().to_string(index=False))
        
        # Print overall stats using actual PNL data
        total_sol_trades = len(trades_df[trades_df['chain'] == 'Solana'])
        # total_bsc_trades = len(trades_df[trades_df['chain'] == 'BSC'])
        total_bsc_trades = 0
        total_sold_positions = len(trades_df[trades_df['status'].str.contains('Success', case=False, na=False)])
        
        # total_profit_7d = sum(p.get('total_profit', 0) for p in sol_pnl) + sum(p.get('total_profit', 0) for p in bsc_pnl)
        total_profit_7d = sum(p.get('total_profit', 0) for p in sol_pnl)
        
        # total_trades_7d = sum(p.get('total_trades', 0) for p in sol_pnl) + sum(p.get('total_trades', 0) for p in bsc_pnl)
        total_trades_7d = sum(p.get('total_trades', 0) for p in sol_pnl)

        # winning_trades_7d = sum(p.get('winning_trades', 0) for p in sol_pnl) + sum(p.get('winning_trades', 0) for p in bsc_pnl)
        winning_trades_7d = sum(p.get('winning_trades', 0) for p in sol_pnl)
        win_rate_7d = (winning_trades_7d / total_trades_7d * 100) if total_trades_7d > 0 else 0

        # print(f"\nOverall Stats:")
        # print(f"Total Solana trades: {total_sol_trades}")
        # print(f"Total BSC trades: {total_bsc_trades}")
        # print(f"Total sold positions: {total_sold_positions}")
        # print(f"Total profit ({days}d): ${total_profit_7d:.2f}")
        # print(f"Win rate ({days}d): {win_rate_7d:.2f}%")
        
        # Read tracked wallets from JSON file
        # print("\nAnalyzing wallet performance for tracked wallets...")
        json_path = os.path.expanduser("~/.solana-bot/tracked_wallets_bot.json")
        
        if os.path.exists(json_path):
            with open(json_path, 'r') as f:
                tracked_data = json.load(f)
            
            all_wallets = tracked_data.get('all_wallets', [])
            good_wallets = tracked_data.get('good_wallets', [])
            
            # print(f"Found {len(all_wallets)} tracked wallets, {len(good_wallets)} good wallets")
            
            # Filter wallet analysis to only include tracked wallets
            filtered_wallet_analysis = wallet_analysis[wallet_analysis['target_wallet'].isin(all_wallets)]
            # print(f"Filtered to {len(filtered_wallet_analysis)} tracked wallets in analysis")
            
            # Analyze each wallet
            wallets_to_add = []
            wallets_to_remove = []
            
            for _, row in filtered_wallet_analysis.iterrows():
                wallet_address = row['target_wallet']
                total_trades = row['total_trades']
                avg_profit_rate = row['avg_profit_rate']
                total_volume_sol = row['total_volume_sol']
                
                # Check if wallet should be added to good wallets
                if total_trades >= 3 and avg_profit_rate >= 0.1 and wallet_address not in good_wallets:
                    wallets_to_add.append(wallet_address)
                    print(f"add wallet {wallet_address} into good wallets")
                
                # Check if wallet should be removed
                if (total_trades * avg_profit_rate < -1.1):
                    wallets_to_remove.append(wallet_address)
                    print(f"remove wallet {wallet_address}: {total_trades} trades, {avg_profit_rate*100:.1f}% avg profit rate, avg SOL volume: {(total_volume_sol/total_trades):.1f}")
            
            # Generate commands to update the JSON file
            if wallets_to_add or wallets_to_remove:
                print("\nCommands to update tracked wallets:")
                
                if wallets_to_add:
                    # Use move_to_gem command to add wallets to good_wallets
                    add_command = f"python py/bloom_bot_controller.py move_to_gem \"{','.join(wallets_to_add)}\""
                    print(f"To add wallets to good_wallets:\n{add_command}\n\n")
                
                if wallets_to_remove:
                    # Use remove command to remove wallets
                    remove_command = f"python py/bloom_bot_controller.py remove \"{','.join(wallets_to_remove)}\""
                    print(f"To remove wallets:\n{remove_command}\n")
        else:
            print(f"Tracked wallets file not found: {json_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Analyze trading data from Telegram and GMGN')
    parser.add_argument('--days', type=int, default=7, help='Number of days to analyze (default: 7)')
    args = parser.parse_args()
    
    asyncio.run(main(args.days))
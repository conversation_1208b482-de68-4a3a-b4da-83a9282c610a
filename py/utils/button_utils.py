from telethon.tl.functions.messages import GetBotCallbackAnswerRequest
import asyncio

async def find_button_in_message(message, button_text):
    """
    Find a button with the given text in a message.
    
    Args:
        message: The message containing buttons
        button_text: The text to search for in the button
        
    Returns:
        Button or None if not found
    """
    for row in message.buttons:
        for button in row:
            if button_text in button.text:
                return button
    return None

async def click_button(client, bloom_bot, message, button):
    """
    Click a button in a message.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing the button
        button: The button to click
        
    Returns:
        bool: True if successful, False otherwise
    """
    max_retries = 5
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            await client(GetBotCallbackAnswerRequest(
                peer=bloom_bot,
                msg_id=message.id,
                data=button.data
            ))
            print(f"Clicked the button: {button.text}")
            
            # Wait for the bot to respond
            await asyncio.sleep(2.5)
            return True
        except Exception as e:
            retry_count += 1
            print(f"Error clicking button: {str(e)}. Retry {retry_count}/{max_retries}")
            if retry_count >= max_retries:
                print(f"Failed after {max_retries} attempts")
                return False
            # Wait before retrying
            await asyncio.sleep(1)

async def find_and_click_button(client, bloom_bot, message, button_text):
    """
    Find and click a button with the given text in a message.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        button_text: The text to search for in the button
        
    Returns:
        bool: True if successful, False otherwise
    """
    button = await find_button_in_message(message, button_text)
    if not button:
        print(f"Could not find button with text: {button_text}")
        return False
    
    return await click_button(client, bloom_bot, message, button) 
import asyncio

async def get_latest_message(client, bloom_bot, message_text="💡 Copy the best traders with Bloom!", limit=10):
    """
    Get the latest message from the bot.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message_text: Optional text to filter messages by
        limit: Maximum number of messages to retrieve
        
    Returns:
        Message or None if not found
    """
    max_retries = 5
    retry_count = 0
    
    while retry_count < max_retries:
        messages = await client.get_messages(bloom_bot, limit=limit)
        if messages is not None:
            for message in messages:
                if message_text is not None and message_text in message.text and message.buttons is not None:
                    return message
        
        # If we reach here, message wasn't found
        retry_count += 1
        if retry_count < max_retries:
            await asyncio.sleep(1)
    print('No message found after {} retries'.format(max_retries))
    return None

async def find_wallet_button_with_pagination(message, wallet_address, client, bloom_bot):
    """
    Search for a wallet address in buttons with pagination support.
    
    Args:
        message: The message containing buttons
        wallet_address: The wallet address to search for
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        
    Returns:
        tuple: (wallet_button, message) or (None, None) if not found
    """
    from utils.button_utils import find_button_in_message, click_button
    
    # First, go to the first page by clicking "Previous" until not found
    current_message = message
    while True:
        previous_button = await find_button_in_message(current_message, "Previous")
        
        if not previous_button:
            break

        # Click the Previous button
        await click_button(client, bloom_bot, current_message, previous_button)
        
        # Get the updated message
        current_message = await get_latest_message(client, bloom_bot)
        if not current_message:
            print('No current_message after found previous')
            return None, None
    
    # Now search through pages until the wallet is found or no more pages
    while True:
        # Search for the wallet address in the current page
        wallet_button = None
        
        # The wallet address might be truncated in the button text
        truncated_wallet = wallet_address[:7]
        try:
            for i, row in enumerate(current_message.buttons):
                for j, button in enumerate(row):
                    if truncated_wallet in button.text:
                        wallet_button = button
                        break
                if wallet_button:
                    break
        except Exception as e:
            print(f'Error while searching buttons: {str(e)}')
        
        if wallet_button:
            return wallet_button, current_message
            
        # If not found, check if there's a Next button
        next_button = await find_button_in_message(current_message, "Next")
                
        if not next_button:
            # No more pages, wallet not found
            print('No Next button found, reached last page')
            return None, None
            
        # Click the Next button
        await click_button(client, bloom_bot, current_message, next_button)
        
        # Get the updated message
        current_message = await get_latest_message(client, bloom_bot)
        if not current_message:
            print('Failed to get updated message after clicking Next')
            return None, None

import os
import json

def update_tracked_wallets(wallet_address, action="add"):
    """
    Update the tracked_wallets_bot.json file.
    
    Args:
        wallet_address: The wallet address to add or remove
        action: "add", "remove", or "add_to_good"
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Expand the home directory path
        home_dir = os.path.expanduser("~")
        file_path = os.path.join(home_dir, ".solana-bot", "tracked_wallets_bot.json")
        
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Read the current content of the file if it exists
        data = {}
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError:
                    print(f"Warning: {file_path} is not a valid JSON file. Creating a new one.")
        
        # Initialize the all_wallets array if it doesn't exist
        if 'all_wallets' not in data:
            data['all_wallets'] = []
        
        # Initialize the good_wallets array if it doesn't exist
        if 'good_wallets' not in data:
            data['good_wallets'] = []
        
        if action == "add":
            # Add the wallet address if it's not already in the array
            if wallet_address not in data['all_wallets']:
                data['all_wallets'].append(wallet_address)
                print(f"Added {wallet_address} to tracked wallets")
            else:
                print(f"Wallet {wallet_address} is already in tracked wallets")
        elif action == "remove":
            # Remove the wallet address if it's in the array
            if wallet_address in data['all_wallets']:
                data['all_wallets'].remove(wallet_address)
                print(f"Removed {wallet_address} from tracked wallets")
                
                # Also update good_wallets and blacklist if they exist
                if 'good_wallets' in data and wallet_address in data['good_wallets']:
                    data['good_wallets'].remove(wallet_address)
                    print(f"Removed {wallet_address} from good wallets")
                
                if 'blacklist' in data and wallet_address not in data['blacklist']:
                    data['blacklist'].append(wallet_address)
                    print(f"Added {wallet_address} to blacklist")
            else:
                print(f"Wallet {wallet_address} is not in tracked wallets")
        elif action == "add_to_good":
            # Add the wallet address to all_wallets if it's not already there
            if wallet_address not in data['all_wallets']:
                data['all_wallets'].append(wallet_address)
                print(f"Added {wallet_address} to tracked wallets")
            
            # Add the wallet address to good_wallets if it's not already there
            if wallet_address not in data['good_wallets']:
                data['good_wallets'].append(wallet_address)
                print(f"Added {wallet_address} to good wallets")
            else:
                print(f"Wallet {wallet_address} is already in good wallets")
        
        # Write the updated content back to the file
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        
        return True
    
    except Exception as e:
        print(f"Error updating tracked_wallets_bot.json: {str(e)}")
        return False

def get_good_wallets():
    """
    Get the list of good wallets from the tracked_wallets_bot.json file.
    
    Returns:
        list: The list of good wallet addresses
    """
    try:
        # Expand the home directory path
        home_dir = os.path.expanduser("~")
        file_path = os.path.join(home_dir, ".solana-bot", "tracked_wallets_bot.json")
        
        # Check if the file exists
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} does not exist. No good wallets found.")
            return []
        
        # Read the content of the file
        with open(file_path, 'r') as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError:
                print(f"Warning: {file_path} is not a valid JSON file. No good wallets found.")
                return []
        
        # Return the list of good wallets
        if 'good_wallets' in data:
            return data['good_wallets']
        else:
            print("Warning: No good_wallets found in the tracked_wallets_bot.json file.")
            return []
    
    except Exception as e:
        print(f"Error reading tracked_wallets_bot.json: {str(e)}")
        return [] 
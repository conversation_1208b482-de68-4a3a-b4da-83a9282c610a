import asyncio
from telethon.tl.functions.messages import GetBotCallbackAnswerRequest
from utils.button_utils import find_and_click_button, click_button
from utils.message_utils import find_wallet_button_with_pagination, get_latest_message
from utils.file_utils import update_tracked_wallets

async def toggle_wallet(client, bloom_bot, message, wallet_address):
    """
    Toggle copy trading for a wallet.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        wallet_address: The wallet address to toggle
    
    Returns:
        bool: True if successful, False otherwise
    """
    # Find the wallet button
    wallet_button, message = await find_wallet_button_with_pagination(message, wallet_address, client, bloom_bot)
    if not wallet_button:
        print(f"Could not find wallet {wallet_address}")
        return False
    
    # Click the wallet button
    await click_button(client, bloom_bot, message, wallet_button)
    
    # Get the latest message
    target_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
    if not target_message:
        print(f"Could not find response message for {wallet_address}")
        return False
    
    # Find and click the Active button
    if not await find_and_click_button(client, bloom_bot, target_message, "Active"):
        print(f"Could not click Active button for {wallet_address}")
        return False
    
    # Find and click the Back button
    if not await find_and_click_button(client, bloom_bot, target_message, "Back"):
        print(f"Could not click Back button for {wallet_address}")
        return False
    
    return True

async def remove_wallet(client, bloom_bot, message, wallet_address):
    """
    Remove a wallet from copy trading.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        wallet_address: The wallet address to remove
    
    Returns:
        bool: True if successful, False otherwise
    """
    # Update the tracked wallets file
    update_tracked_wallets(wallet_address, "remove")

    # Find the wallet button
    wallet_button, message = await find_wallet_button_with_pagination(message, wallet_address, client, bloom_bot)
    if not wallet_button:
        print(f"Could not find wallet {wallet_address}")
        return False
    
    # Click the wallet button
    await click_button(client, bloom_bot, message, wallet_button)
    
    # Get the latest message
    target_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
    if not target_message:
        print(f"Could not find response message for {wallet_address}")
        return False
    
    # Find and click the Delete Config button
    if not await find_and_click_button(client, bloom_bot, target_message, "Delete Config"):
        print(f"Could not click Delete Config button for {wallet_address}")
        return False
    return True

async def copy_multiple_wallets(client, bloom_bot, message, wallet_addresses):
    """
    Add multiple wallets for copy trading in a single operation.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        wallet_addresses: List of wallet addresses to add
    
    Returns:
        bool: True if successful, False otherwise
    """
    # Find and click the Mass Create button
    if not await find_and_click_button(client, bloom_bot, message, "Mass Create"):
        print("Could not click Mass Create button")
        return False
    
    # Wait for the bot to respond
    await asyncio.sleep(2)

    # Prepare the wallet addresses as a single string with each address on a new line
    wallet_addresses_text = "\n".join(wallet_addresses)
    await client.send_message(bloom_bot, wallet_addresses_text)
    await asyncio.sleep(3)
    
    # Get the latest message
    target_message = await get_latest_message(client, bloom_bot)
    if not target_message:
        print("Could not find response message")
        return False
    
    # Process each wallet address
    for wallet_address in wallet_addresses:
        # Find the wallet button
        wallet_button, target_message = await find_wallet_button_with_pagination(target_message, wallet_address, client, bloom_bot)
        if not wallet_button:
            print(f"Could not find wallet {wallet_address}")
            continue
        
        # Click the wallet button
        await click_button(client, bloom_bot, target_message, wallet_button)
        
        # Get the latest message
        wallet_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
        if not wallet_message:
            print(f"Could not find response message for {wallet_address}")
            continue
        
        if not await find_and_click_button(client, bloom_bot, wallet_message, "🔴 Multi Region"):
            print(f"Could not click Multi Region button for {wallet_address}")

        if not await find_and_click_button(client, bloom_bot, wallet_message, "🟢 Buy Exact"):
            print(f"Could not click Buy Exact button for {wallet_address}")

        wallet_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
        if not wallet_message:
            print(f"Could not find response message for {wallet_address}")
            continue

        if not await find_and_click_button(client, bloom_bot, wallet_message, "🔴 Buy Exact"):
            print(f"Could not click Buy Exact button for {wallet_address}")
        
        if not await find_and_click_button(client, bloom_bot, wallet_message, "Active 🔴"):
            print(f"Could not click Active button for {wallet_address}")

        # Find and click the Back button
        if not await find_and_click_button(client, bloom_bot, wallet_message, "Back"):
            print(f"Could not click Back button for {wallet_address}")
            continue
        
        # Get the latest message
        target_message = await get_latest_message(client, bloom_bot)
        if not target_message:
            print(f"Could not find response message for {wallet_address}")
            continue
        
        # Update the tracked wallets file
        update_tracked_wallets(wallet_address, "add")
    
    return True

async def move_wallet_to_gem(client, bloom_bot, message, wallet_address):
    """
    Move a wallet to GEM configuration.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        wallet_address: The wallet address to move to GEM
    
    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Moving wallet to GEM: {wallet_address}")
    
    try:
        # Find the wallet button with pagination support
        wallet_button, _ = await find_wallet_button_with_pagination(
            message, wallet_address, client, bloom_bot
        )
        
        if not wallet_button:
            print(f"Could not find a button containing the wallet address: {wallet_address}")
            return False
        
        # Click the wallet button
        await click_button(client, bloom_bot, message, wallet_button)
        
        # Get the updated message
        updated_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
        if not updated_message:
            print("Could not find the updated message with the wallet address")
            return False
        
        # Find and click the "Wallet:" button
        if not await find_and_click_button(client, bloom_bot, updated_message, "Wallet:"):
            print("Could not find or click the Wallet: button")
            return False
        
        # Get the message containing "🌸 Manage your config wallet"
        config_message = await get_latest_message(client, bloom_bot, "🌸 Manage your config wallet")
        if not config_message:
            print("Could not find the config wallet message")
            return False
        
        # Find and click the "GEM" button
        if not await find_and_click_button(client, bloom_bot, config_message, "GEM"):
            print("Could not find or click the GEM button")
            return False
        
        # Find and click the "Back" button in the same message
        if not await find_and_click_button(client, bloom_bot, config_message, "Back"):
            print("Could not find or click the Back button")
            return False
        
        # Get the message containing "🌸 Setup New Copy-Trading Profile" and the wallet address
        profile_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
        if not profile_message or wallet_address not in profile_message.text:
            print("Could not find the profile message with the wallet address")
            return False
        
        # Find and click the "Max Buy" button
        if not await find_and_click_button(client, bloom_bot, profile_message, "Max Buy"):
            print("Could not find or click the Max Buy button")
            return False
        
        # Send message
        await client.send_message(bloom_bot, "0.5")
        await asyncio.sleep(2)
        
        # Get the updated message
        updated_profile_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
        if not updated_profile_message or wallet_address not in updated_profile_message.text:
            print("Could not find the updated profile message with the wallet address")
            return False
        
        # Find and click the "Back" button
        if not await find_and_click_button(client, bloom_bot, updated_profile_message, "Back"):
            print("Could not find or click the Back button")
            return False
        
        # Add the wallet to good_wallets
        update_tracked_wallets(wallet_address, "add_to_good")
        
        print(f"Successfully moved wallet to GEM: {wallet_address}")
        return True
    
    except Exception as e:
        print(f"Error moving wallet to GEM: {str(e)}")
        return False

async def move_multiple_wallets_to_gem(client, bloom_bot, message, wallet_addresses):
    """
    Move multiple wallets to GEM configuration.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        wallet_addresses: List of wallet addresses to move to GEM
    
    Returns:
        bool: True if all operations were successful, False otherwise
    """
    success = True
    
    for wallet_address in wallet_addresses:
        result = await move_wallet_to_gem(client, bloom_bot, message, wallet_address)
        if not result:
            success = False
    
    return success

async def update_wallet_max_buy(client, bloom_bot, message, wallet_address, sol_value):
    """
    Update the Max Buy value for a wallet.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        wallet_address: The wallet address to update
        sol_value: The new Max Buy value in SOL
    
    Returns:
        bool: True if successful, False otherwise
    """
    print(f"Updating Max Buy for wallet: {wallet_address} to {sol_value} SOL")
    
    try:
        # Find the wallet button with pagination support
        wallet_button, _ = await find_wallet_button_with_pagination(
            message, wallet_address, client, bloom_bot
        )
        
        if not wallet_button:
            print(f"Could not find a button containing the wallet address: {wallet_address}")
            return False
        
        # Click the wallet button
        await click_button(client, bloom_bot, message, wallet_button)
        
        # Get the updated message
        updated_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
        if not updated_message:
            print("Could not find the updated message with the wallet address")
            return False
        
        # Find and click the "Max Buy" button
        if not await find_and_click_button(client, bloom_bot, updated_message, "Max Buy"):
            print("Could not find or click the Max Buy button")
            return False
        
        # Send the SOL value as a message
        await client.send_message(bloom_bot, str(sol_value))
        await asyncio.sleep(2)
        
        # Get the updated message
        updated_profile_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
        if not updated_profile_message or wallet_address not in updated_profile_message.text:
            print("Could not find the updated profile message with the wallet address")
            return False
        
        # Find and click the "Back" button
        if not await find_and_click_button(client, bloom_bot, updated_profile_message, "Back"):
            print("Could not find or click the Back button")
            return False
        
        print(f"Successfully updated Max Buy for wallet: {wallet_address} to {sol_value} SOL")
        return True
    
    except Exception as e:
        print(f"Error updating Max Buy for wallet {wallet_address}: {str(e)}")
        return False

async def update_multiple_wallets_max_buy(client, bloom_bot, message, wallet_addresses, sol_value):
    """
    Update the Max Buy value for multiple wallets.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
        message: The message containing buttons
        wallet_addresses: List of wallet addresses to update
        sol_value: The new Max Buy value in SOL
    
    Returns:
        bool: True if all operations were successful, False otherwise
    """
    success = True
    
    for wallet_address in wallet_addresses:
        result = await update_wallet_max_buy(client, bloom_bot, message, wallet_address, sol_value)
        if not result:
            success = False
        message = await get_latest_message(client, bloom_bot)
    
    return success 
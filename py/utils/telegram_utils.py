from telethon import TelegramClient
import asyncio
from config import api_id, api_hash, phone_number
from utils.button_utils import click_button

async def connect_to_telegram():
    """
    Connect to Telegram and return the client.
    
    Returns:
        TelegramClient: The connected Telegram client
    """
    client = TelegramClient('bloom_session', api_id, api_hash)
    await client.start(phone_number)
    print("Connected to Telegram.")
    return client

async def find_bloom_bot(client):
    """
    Find the Bloom Solana US1 bot.
    
    Args:
        client: The Telegram client
    
    Returns:
        The Bloom bot entity or None if not found
    """
    bloom_bot = None
    async for dialog in client.iter_dialogs():
        if "Bloom Solana EU2" in dialog.name:
            bloom_bot = dialog
            break
    
    if not bloom_bot:
        print("Could not find Bloom Solana bot")
        return None
    
    return bloom_bot

async def get_copy_message(client, bloom_bot):
    """
    Send the /copy command and get the response message.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
    
    Returns:
        The message containing buttons or None if not found
    """
    # Send /copy command
    await client.send_message(bloom_bot, "/copy")
    
    # Wait for the bot to respond
    await asyncio.sleep(2)
    
    # Get the latest message from the bot
    from utils.message_utils import get_latest_message
    target_message = await get_latest_message(client, bloom_bot)
    
    if not target_message:
        print("Could not find the expected response from the bot")
        return None
    
    return target_message

async def click_close_button(client, bloom_bot):
    """
    Find and click the Close button.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
    
    Returns:
        bool: True if successful, False otherwise
    """
    from utils.message_utils import get_latest_message
    from utils.button_utils import find_and_click_button
    
    # Get the latest message from the bot
    target_message = await get_latest_message(client, bloom_bot)
    
    if not target_message:
        print("Could not find the expected response from the bot")
        return False
    
    # Find and click the Close button
    return await find_and_click_button(client, bloom_bot, target_message, "Close")

async def turn_off_buy_fixed(client, bloom_bot):
    """
    Turn off Buy Fixed for all wallets.
    
    Args:
        client: The Telegram client
        bloom_bot: The Bloom bot entity
    
    Returns:
        bool: True if successful, False otherwise
    """
    from utils.message_utils import get_latest_message
    from utils.button_utils import find_and_click_button
    
    # Send /copy command
    target_message = await get_copy_message(client, bloom_bot)
    if not target_message:
        print("Could not find the expected response from the bot")
        return False
    
    # Click Previous until there's no Previous button
    while True:
        # Check if there's a Previous button
        has_previous = await find_and_click_button(client, bloom_bot, target_message, "Previous")
        if not has_previous:
            break
        
        # Wait for the bot to respond
        await asyncio.sleep(1)
        target_message = await get_latest_message(client, bloom_bot)
        if not target_message:
            print("Could not find the expected response from the bot")
            return False
    
    # Process all wallets
    while True:
        # Get the current message
        target_message = await get_latest_message(client, bloom_bot)
        if not target_message:
            print("Could not find the expected response from the bot")
            return False
        
        # Find all buttons containing 🟢
        buttons = target_message.buttons
        if not buttons:
            print("No buttons found in the message")
            return False
        
        # Process each wallet with 🟢
        for row in buttons:
            for button in row:
                if "🟢" in button.text:
                    # Click the wallet button
                    await click_button(client, bloom_bot, target_message, button)
                    await asyncio.sleep(1)
                    
                    # Get the wallet settings message
                    wallet_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
                    if not wallet_message:
                        print("Could not find the wallet settings message")
                        ok = False
                        break
                    
                    # Check if there's a "🟢 Buy Fixed" button
                    buy_fixed_button, buy_exact_button = None, None
                    for btn_row in wallet_message.buttons:
                        for btn in btn_row:
                            if "🟢 Buy Fixed" in btn.text:
                                buy_fixed_button = btn
                            elif "Buy Exact" in btn.text:
                                buy_exact_button = btn
                    
                    # If found, click it to turn off Buy Fixed
                    if buy_fixed_button:
                        await click_button(client, bloom_bot, wallet_message, buy_exact_button)
                        await asyncio.sleep(1)
                    
                    # Get the wallet settings message
                    wallet_message = await get_latest_message(client, bloom_bot, "🌸 Setup New Copy-Trading Profile")
                    if not wallet_message:
                        print("Could not find the wallet settings message")
                        ok = False
                        break
                    
                    # Check if there's a "🔴 Buy Exact" button
                    buy_exact_button = None
                    for btn_row in wallet_message.buttons:
                        for btn in btn_row:
                            if "🔴 Buy Exact" in btn.text:
                                buy_exact_button = btn
                                break
                        if buy_exact_button:
                            break
                    if buy_exact_button:
                        await click_button(client, bloom_bot, wallet_message, buy_exact_button)
                        await asyncio.sleep(1)
                    
                    # Click Back
                    back_button = None
                    for btn_row in wallet_message.buttons:
                        for btn in btn_row:
                            if "Back" in btn.text:
                                back_button = btn
                                break
                        if back_button:
                            break
                    
                    if back_button:
                        await click_button(client, bloom_bot, wallet_message, back_button)
                        await asyncio.sleep(1)
        
        # Check if there's a Next button
        has_next = await find_and_click_button(client, bloom_bot, target_message, "Next")
        if not has_next:
            break
        
        # Wait for the bot to respond
        await asyncio.sleep(1)
        target_message = await get_latest_message(client, bloom_bot)
        if not target_message:
            print("Could not find the expected response from the bot")
            return False
    
    # Click the Close button at the end
    return await click_close_button(client, bloom_bot) 
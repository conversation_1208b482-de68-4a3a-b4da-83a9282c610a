import os
import csv
import json
import asyncio
import subprocess
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def get_wallet_holdings(wallet_address: str) -> List[Dict]:
    """Get wallet holdings using curl to call GMGN API"""
    base_url = f"https://gmgn.ai/api/v1/wallet_holdings/sol/{wallet_address}?device_id=0d89bab0-6361-41dd-a41b-88f7e715e19f&client_id=gmgn_web_2025.0312.115714&from_app=gmgn&app_ver=2025.0312.115714&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en-US&limit=20&orderby=last_active_timestamp&direction=desc&showsmall=true&sellout=true&tx30d=true"
    
    all_holdings = []
    next_cursor = None
    page_count = 0
    max_pages = 2  # Limit to 2 pages as in Rust code
    
    while page_count < max_pages:
        page_count += 1
        
        # Construct URL with cursor if available
        url = f"{base_url}&cursor={next_cursor}" if next_cursor else base_url
        
        cmd = [
            "curl",
            url,
            "-H", "accept: application/json, text/plain, */*",
            "-H", "accept-language: en-US,en;q=0.9",
            "-H", "cache-control: no-cache",
            "-b", "_ga=GA1.1.1867644676.1736254604; _ga_0XM0LYXGC8=deleted; sid=gmgn%7Cab1e390fc4bffd8378a04c386c79af4e; _ga_UGLVBMV4Z0=GS1.2.1741674138160037.7ace014211b366a86efe8ebe7b206400.o7lBMX0TQ8mFZD0XLbpyfw%3D%3D.hFX9NS1YTBg25e2P7E%2BrDA%3D%3D.mh%2BCI6nJyW7zJ4oR0OahzQ%3D%3D.g2STYFqRJDqE50iDEiCRmQ%3D%3D",
            "-H", "dnt: 1",
            "-H", "pragma: no-cache",
            "-H", "priority: u=1, i",
            "-H", f"referer: https://gmgn.ai/sol/address/MVNsiiWE_{wallet_address}",
            "-H", "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                response = json.loads(result.stdout)
                holdings_data = response.get('data', {})
                holdings = holdings_data.get('holdings', [])
                all_holdings.extend(holdings)
                
                # Get next cursor
                next_cursor = holdings_data.get('next')
                if not next_cursor:
                    break
                    
                # Add delay between requests
                await asyncio.sleep(1)
            else:
                print(f"Error getting holdings: {result.stderr}")
                break
        except Exception as e:
            print(f"Error running command: {e}")
            break
    
    return all_holdings

async def get_wallet_pnl(wallet_address: str) -> Optional[Dict]:
    """Get wallet PNL data from GMGN API"""
    url = f"https://gmgn.ai/defi/quotation/v1/smartmoney/sol/walletNew/{wallet_address}?device_id=0d89bab0-6361-41dd-a41b-88f7e715e19f&client_id=gmgn_web_2025.0205.173135&from_app=gmgn&app_ver=2025.0205.173135&tz_name=Asia%2FTaipei&tz_offset=28800&app_lang=en&period=7d"
    
    cmd = [
        "curl",
        url,
        "-H", "accept: application/json, text/plain, */*",
        "-H", "accept-language: en-US,en;q=0.9",
        "-H", "cache-control: no-cache",
        "-H", "dnt: 1",
        "-H", "pragma: no-cache",
        "-H", "priority: u=1, i",
        "-H", f"referer: https://gmgn.ai/sol/address/{wallet_address}",
        "-H", "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            response = json.loads(result.stdout)
            return response.get('data')
        else:
            print(f"Error getting PNL: {result.stderr}")
            return None
    except Exception as e:
        print(f"Error running command: {e}")
        return None

def process_holdings(holdings: List[Dict]) -> List[Dict]:
    """Process holdings to get sold positions with profit data"""
    sold_positions = []
    for holding in holdings:
        # Only include positions with balance = 0 (sold positions)
        if holding.get('balance') == '0':
            try:
                total_profit = float(holding.get('total_profit', '0'))
                total_profit_pnl = float(holding.get('total_profit_pnl', '0'))
                avg_cost = float(holding.get('avg_cost', '0'))
                avg_sold = float(holding.get('avg_sold', '0'))
                
                if avg_cost > 0:
                    profit_rate = (avg_sold - avg_cost) / avg_cost
                else:
                    profit_rate = 0
                
                sold_positions.append({
                    'token_address': holding['token']['address'],
                    'token_name': holding['token']['name'],
                    'token_symbol': holding['token']['symbol'],
                    'total_profit': total_profit,
                    'total_profit_pnl': total_profit_pnl,
                    'profit_rate': profit_rate,
                    'avg_cost': avg_cost,
                    'avg_sold': avg_sold,
                    'last_active_timestamp': holding.get('last_active_timestamp'),
                    'start_holding_at': holding.get('start_holding_at'),
                    'end_holding_at': holding.get('end_holding_at')
                })
            except (ValueError, KeyError) as e:
                print(f"Error processing holding: {e}")
                continue
    
    return sold_positions

async def main():
    wallet_address = "4zVmga8K7NxXomV7Bhv6wGdkjr8cQezMzrA4J7JBJ3XA"
    
    # Get holdings data
    print("Getting wallet holdings...")
    holdings = await get_wallet_holdings(wallet_address)
    
    # Get PNL data
    print("Getting wallet PNL data...")
    pnl_data = await get_wallet_pnl(wallet_address)
    
    # Process holdings
    print("Processing holdings...")
    sold_positions = process_holdings(holdings)
    
    # Load bloom trades data
    print("Loading bloom trades data...")
    if os.path.exists('bloom_trades.csv'):
        bloom_trades = pd.read_csv('bloom_trades.csv')
    else:
        print("bloom_trades.csv not found")
        bloom_trades = pd.DataFrame()
    
    # Convert timestamps to datetime
    for pos in sold_positions:
        if pos.get('last_active_timestamp'):
            pos['last_active_date'] = datetime.fromtimestamp(pos['last_active_timestamp'])
        if pos.get('start_holding_at'):
            pos['start_date'] = datetime.fromtimestamp(pos['start_holding_at'])
        if pos.get('end_holding_at'):
            pos['end_date'] = datetime.fromtimestamp(pos['end_holding_at'])
    
    # Convert to DataFrame
    holdings_df = pd.DataFrame(sold_positions)
    
    # Join with bloom trades if available
    if not bloom_trades.empty:
        # Convert date columns to datetime
        bloom_trades['date'] = pd.to_datetime(bloom_trades['date'])
        
        # Join on token_address
        merged_df = pd.merge(
            holdings_df,
            bloom_trades,
            on='token_address',
            how='left',
            suffixes=('', '_bloom')
        )
        
        # Save merged data
        output_file = 'wallet_analysis.csv'
        merged_df.to_csv(output_file, index=False)
        print(f"Saved merged data to {output_file}")
    else:
        # Save just holdings data
        output_file = 'wallet_holdings.csv'
        holdings_df.to_csv(output_file, index=False)
        print(f"Saved holdings data to {output_file}")
    
    # Print summary
    print("\nSummary:")
    print(f"Total sold positions: {len(sold_positions)}")
    if pnl_data:
        print(f"Total profit: {pnl_data.get('total_profit', 'Unknown')}")
        print(f"Win rate: {pnl_data.get('win_rate', 'Unknown')}")

if __name__ == '__main__':
    asyncio.run(main()) 
from telethon import TelegramClient
import re
import asyncio
import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Telegram API credentials from environment variables
api_id = os.getenv('TELEGRAM_API_ID')
api_hash = os.getenv('TELEGRAM_API_HASH')
phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')
channel_name = "Pump Hound Signal"  # Update this to your target channel name

# Validate required environment variables
if not all([api_id, api_hash, phone_number]):
    raise ValueError("Missing required environment variables. Please set TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE_NUMBER in .env file")

# Regex patterns to extract data from price milestone messages
patterns = {
    'token_name': re.compile(r'🚀 \*\*(.+?)\*\* \| PRICE MILESTONE'),
    'increase': re.compile(r'📊 Increase: \*\*\+([0-9.]+)%\*\*'),
    'token_address': re.compile(r'📝 CA: `([0-9a-zA-Z]{42,})`')
}

async def extract_milestone_info(message):
    """Extract price milestone information from a message"""
    if not message.text:
        return None
        
    text = message.text
    
    # Check if this is a price milestone message
    token_name_match = patterns['token_name'].search(text)
    if not token_name_match:
        return None
        
    token_name = token_name_match.group(1).strip()
    
    # Extract increase percentage
    increase_match = patterns['increase'].search(text)
    increase = float(increase_match.group(1)) if increase_match else 0
    
    # Extract token address
    token_address_match = patterns['token_address'].search(text)
    token_address = token_address_match.group(1) if token_address_match else 'Unknown'
    
    return {
        'date': message.date,
        'token_name': token_name,
        'increase': increase,
        'token_address': token_address
    }

async def extract_telegram_milestones(days=7):
    """Extract price milestone messages from the target Telegram channel for the specified days"""
    # Calculate date range (now to N days ago)
    utc_plus_8 = timezone(timedelta(hours=8))
    now = datetime.now(utc_plus_8)
    start_date = now - timedelta(days=days)
    
    # Convert to UTC for comparison with Telegram API dates
    now_utc = now.astimezone(timezone.utc)
    start_date_utc = start_date.astimezone(timezone.utc)
    
    milestones = []
    
    # Connect to Telegram
    client = TelegramClient('milestone_session', api_id, api_hash)
    await client.start(phone_number)
    
    print(f"Connected to Telegram. Fetching price milestones from {start_date} to {now} (UTC+8)...")
    
    # Find the target channel
    target_channel_entity = None
    async for dialog in client.iter_dialogs():
        if channel_name.lower() in dialog.name.lower():
            target_channel_entity = dialog
            break
    
    if not target_channel_entity:
        print(f"Could not find {channel_name} channel")
        await client.disconnect()
        return milestones
    
    # Get messages from the channel
    messages = await client.get_messages(
        target_channel_entity,
        limit=1000,  # Adjust as needed
        offset_date=now_utc
    )
    
    # Filter messages by date range (using UTC for comparison)
    filtered_messages = [
        msg for msg in messages 
        if start_date_utc <= msg.date <= now_utc
    ]
    
    print(f"Found {len(filtered_messages)} messages in {channel_name} from the specified time range")
    
    # Extract milestone information
    for message in filtered_messages:
        milestone_info = await extract_milestone_info(message)
        if milestone_info:
            milestones.append(milestone_info)
    
    await client.disconnect()
    return milestones

async def analyze_milestones(days=7):
    """Analyze price milestones and find max gain for each token"""
    milestones = await extract_telegram_milestones(days)
    
    if not milestones:
        print(f"No price milestone messages found in the past {days} days")
        return
    
    # Convert to DataFrame for easier analysis
    milestones_df = pd.DataFrame(milestones)
    
    # Group by token address and find max increase for each token
    token_max_gain = milestones_df.groupby(['token_address', 'token_name'])['increase'].max().reset_index()
    
    # Sort by highest gain in descending order
    token_max_gain = token_max_gain.sort_values('increase', ascending=False)
    
    # Print results
    print(f"\nToken price milestones from the past {days} days (sorted by max gain):")
    print("=" * 100)
    print(f"{'Token Name':<25} {'Token Address':<45} {'Max Gain':<13}")
    print("-" * 100)
    
    for _, row in token_max_gain.iterrows():
        max_gain = f"+{row['increase']:.0f}%"
        print(f"{row['token_name']:<25} {row['token_address']:<45} {max_gain:<13}")
    
    # Save top tokens to a file for use by send_token_signals.sh
    save_tokens_for_signals(token_max_gain)
    
    return token_max_gain

def save_tokens_for_signals(token_data, top_n=200):
    """Save top token addresses and their gain percentages to a file"""
    # Filter out tokens with 'Unknown' addresses
    valid_tokens = token_data[token_data['token_address'] != 'Unknown']
    
    # Take top N tokens sorted by highest gain
    top_tokens = valid_tokens.head(top_n)
    
    # Create output directory if it doesn't exist
    os.makedirs('output', exist_ok=True)
    
    # Save to file
    with open('output/top_tokens.txt', 'w') as f:
        for _, row in top_tokens.iterrows():
            # Format: token_address,gain_percentage (as integer without % sign)
            f.write(f"{row['token_address']},{int(row['increase'])}\n")
    
    print(f"\nSaved top {len(top_tokens)} tokens to output/top_tokens.txt for signal processing")

async def main():
    # Default to 7 days, but can be changed by passing a parameter
    await analyze_milestones(days=7)

if __name__ == "__main__":
    asyncio.run(main()) 
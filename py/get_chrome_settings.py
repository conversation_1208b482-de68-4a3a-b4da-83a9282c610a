#!/usr/bin/env python3
import json
import subprocess
import os

def get_chrome_settings():
    """Get Chrome Beta's current settings"""
    # Get Chrome version
    try:
        version = subprocess.check_output(['google-chrome-beta', '--version']).decode().strip()
        print(f"Chrome Beta Version: {version}")
    except:
        print("Could not get Chrome Beta version")
        version = ""

    # Get Chrome flags
    try:
        flags = subprocess.check_output(['google-chrome-beta', '--list-flags']).decode().strip()
        print("\nChrome Beta Flags:")
        print(flags)
    except:
        print("Could not get Chrome Beta flags")

    # Get Chrome user agent
    try:
        user_agent = subprocess.check_output([
            'google-chrome-beta',
            '--headless',
            '--disable-gpu',
            '--print-to-pdf=/dev/null',
            '--dump-dom',
            'https://www.whatismybrowser.com/detect/what-is-my-user-agent'
        ]).decode()
        print("\nChrome Beta User Agent:")
        print(user_agent)
    except:
        print("Could not get Chrome Beta user agent")

    # Get Chrome process arguments
    try:
        ps_output = subprocess.check_output(['ps', 'aux', '|', 'grep', 'chrome-beta']).decode()
        print("\nChrome Beta Process Arguments:")
        print(ps_output)
    except:
        print("Could not get Chrome Beta process arguments")

if __name__ == "__main__":
    get_chrome_settings() 
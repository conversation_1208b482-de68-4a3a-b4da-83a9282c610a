#!/usr/bin/env python3
import os
import csv
import math
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np

# Solana addresses to track
ADDRESS_1 = "C18EnJxfGUWvKKs74HGLcFgWevhtksvrcKekyqbgxKJD"
ADDRESS_2 = "46W2UspRnpB8x7ZajVTg2rVBo2ad3CbnXiMEV8Y96kQa"

# Data directory and file paths
DATA_DIR = "sol_balance_data"
ADDRESS_1_FILE = os.path.join(DATA_DIR, f"{ADDRESS_1}.csv")
ADDRESS_2_FILE = os.path.join(DATA_DIR, f"{ADDRESS_2}.csv")

# Output directory for visualizations
OUTPUT_DIR = "sol_performance_results"


def read_balance_data(file_path):
    """Read balance data from CSV file"""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist.")
        return None
    
    timestamps = []
    unix_timestamps = []
    sol_balances = []
    usd_balances = []
    sol_prices = []
    
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header row
        
        for row in reader:
            if len(row) >= 5:  # Ensure row has all expected columns
                timestamps.append(datetime.strptime(row[0], "%Y-%m-%d %H:%M:%S"))
                unix_timestamps.append(int(row[1]))
                sol_balances.append(float(row[2]))
                usd_balances.append(float(row[3]))
                sol_prices.append(float(row[4]))
    
    return {
        "timestamps": timestamps,
        "unix_timestamps": unix_timestamps,
        "sol_balances": sol_balances,
        "usd_balances": usd_balances,
        "sol_prices": sol_prices
    }


def calculate_metrics(data):
    """Calculate investment performance metrics"""
    if not data or len(data["sol_balances"]) < 2:
        return {
            "pnl_pct": 0,
            "sharpe_ratio": 0,
            "max_drawdown": 0,
            "volatility": 0,
            "avg_balance": 0
        }
    
    # Extract values
    sol_balances = data["sol_balances"]
    
    # Calculate returns
    returns = []
    for i in range(1, len(sol_balances)):
        if sol_balances[i-1] > 0:
            ret = (sol_balances[i] - sol_balances[i-1]) / sol_balances[i-1]
            returns.append(ret)
    
    # Calculate PnL
    initial_value = sol_balances[0]
    final_value = sol_balances[-1]
    pnl_pct = ((final_value - initial_value) / initial_value) * 100 if initial_value > 0 else 0
    
    # Calculate volatility (standard deviation of returns)
    if returns:
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        volatility = math.sqrt(variance) if variance > 0 else 0
    else:
        volatility = 0
    
    # Calculate Sharpe Ratio (assuming risk-free rate of 0 for simplicity)
    # Annualized Sharpe = (Mean Daily Return / Daily Std Dev) * sqrt(365)
    # Since we're collecting data every minute, we need to adjust the annualization factor
    # For minute data: sqrt(365 * 24 * 60) = sqrt(525600) ≈ 725
    # But since our data might be sparse, we'll use a more conservative factor
    if returns and volatility > 0:
        mean_return = sum(returns) / len(returns)
        sharpe_ratio = (mean_return / volatility) * math.sqrt(365)
    else:
        sharpe_ratio = 0
    
    # Calculate Max Drawdown
    max_drawdown = 0
    peak = sol_balances[0]
    
    for value in sol_balances:
        if value > peak:
            peak = value
        drawdown = (peak - value) / peak if peak > 0 else 0
        max_drawdown = max(max_drawdown, drawdown)
    
    # Calculate average balance
    avg_balance = sum(sol_balances) / len(sol_balances) if sol_balances else 0
    
    return {
        "pnl_pct": pnl_pct,
        "sharpe_ratio": sharpe_ratio,
        "max_drawdown": max_drawdown * 100,  # Convert to percentage
        "volatility": volatility * 100,  # Convert to percentage
        "avg_balance": avg_balance
    }


def plot_performance(address, data, metrics, output_dir):
    """Create performance visualizations"""
    if not data or len(data["timestamps"]) < 2:
        print(f"Not enough data to plot performance for {address}")
        return
    
    # Ensure output directory exists
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Extract data
    timestamps = data["timestamps"]
    sol_balances = data["sol_balances"]
    sol_prices = data["sol_prices"]
    
    # Create figure with multiple subplots
    fig, axs = plt.subplots(3, 1, figsize=(12, 15), gridspec_kw={'height_ratios': [3, 2, 2]})
    
    # Plot 1: SOL Balance over time
    axs[0].plot(timestamps, sol_balances, 'b-', linewidth=2)
    axs[0].set_title(f'SOL Balance for {address}', fontsize=16)
    axs[0].set_ylabel('SOL Balance', fontsize=14)
    axs[0].grid(True)
    axs[0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
    plt.setp(axs[0].xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # Add metrics as text annotation
    metrics_text = (
        f"PnL: {metrics['pnl_pct']:.2f}%\n"
        f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}\n"
        f"Max Drawdown: {metrics['max_drawdown']:.2f}%\n"
        f"Volatility: {metrics['volatility']:.2f}%\n"
        f"Avg Balance: {metrics['avg_balance']:.4f} SOL"
    )
    axs[0].annotate(metrics_text, xy=(0.02, 0.97), xycoords='axes fraction',
                   bbox=dict(boxstyle="round,pad=0.5", fc="white", alpha=0.8),
                   va='top', fontsize=12)
    
    # Plot 2: Calculate and plot drawdown
    drawdowns = []
    peak = sol_balances[0]
    
    for value in sol_balances:
        if value > peak:
            peak = value
        drawdown = (peak - value) / peak if peak > 0 else 0
        drawdowns.append(-drawdown * 100)  # Convert to negative percentage for visualization
    
    axs[1].plot(timestamps, drawdowns, 'r-', linewidth=2)
    axs[1].set_title('Drawdown (%)', fontsize=16)
    axs[1].set_ylabel('Drawdown (%)', fontsize=14)
    axs[1].grid(True)
    axs[1].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
    plt.setp(axs[1].xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # Plot 3: SOL Price
    axs[2].plot(timestamps, sol_prices, 'g-', linewidth=2)
    axs[2].set_title('SOL Price (USD)', fontsize=16)
    axs[2].set_xlabel('Time', fontsize=14)
    axs[2].set_ylabel('Price (USD)', fontsize=14)
    axs[2].grid(True)
    axs[2].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
    plt.setp(axs[2].xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # Adjust layout and save figure
    plt.tight_layout()
    output_file = os.path.join(output_dir, f"{address}_performance.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    print(f"Performance visualization saved to {output_file}")


def compare_performances(address1, data1, metrics1, address2, data2, metrics2, output_dir):
    """Create a comparison visualization of both addresses"""
    if not data1 or not data2 or len(data1["timestamps"]) < 2 or len(data2["timestamps"]) < 2:
        print("Not enough data to compare performances")
        return
    
    # Ensure output directory exists
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Create figure with multiple subplots
    fig, axs = plt.subplots(2, 1, figsize=(12, 12))
    
    # Normalize balances to start at 100% for fair comparison
    norm_balance1 = []
    if data1["sol_balances"][0] > 0:
        norm_balance1 = [b / data1["sol_balances"][0] * 100 for b in data1["sol_balances"]]
    
    norm_balance2 = []
    if data2["sol_balances"][0] > 0:
        norm_balance2 = [b / data2["sol_balances"][0] * 100 for b in data2["sol_balances"]]
    
    # Plot 1: Normalized SOL Balance comparison
    if norm_balance1 and norm_balance2:
        axs[0].plot(data1["timestamps"], norm_balance1, 'b-', linewidth=2, label=f'Address 1 ({address1[:8]}...)')
        axs[0].plot(data2["timestamps"], norm_balance2, 'r-', linewidth=2, label=f'Address 2 ({address2[:8]}...)')
        axs[0].set_title('Normalized Performance Comparison (Starting at 100%)', fontsize=16)
        axs[0].set_ylabel('Normalized Value (%)', fontsize=14)
        axs[0].grid(True)
        axs[0].legend(fontsize=12)
        axs[0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.setp(axs[0].xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # Plot 2: Metrics comparison as bar chart
    metrics_labels = ['PnL (%)', 'Sharpe Ratio', 'Max Drawdown (%)', 'Volatility (%)']
    metrics_values1 = [metrics1['pnl_pct'], metrics1['sharpe_ratio'], metrics1['max_drawdown'], metrics1['volatility']]
    metrics_values2 = [metrics2['pnl_pct'], metrics2['sharpe_ratio'], metrics2['max_drawdown'], metrics2['volatility']]
    
    x = np.arange(len(metrics_labels))
    width = 0.35
    
    axs[1].bar(x - width/2, metrics_values1, width, label=f'Address 1 ({address1[:8]}...)', color='blue')
    axs[1].bar(x + width/2, metrics_values2, width, label=f'Address 2 ({address2[:8]}...)', color='red')
    
    axs[1].set_title('Performance Metrics Comparison', fontsize=16)
    axs[1].set_ylabel('Value', fontsize=14)
    axs[1].set_xticks(x)
    axs[1].set_xticklabels(metrics_labels, fontsize=12)
    axs[1].legend(fontsize=12)
    axs[1].grid(True, axis='y')
    
    # Add value labels on top of bars
    for i, v1 in enumerate(metrics_values1):
        axs[1].text(i - width/2, v1 + 0.5, f'{v1:.2f}', ha='center', va='bottom', fontsize=10)
    
    for i, v2 in enumerate(metrics_values2):
        axs[1].text(i + width/2, v2 + 0.5, f'{v2:.2f}', ha='center', va='bottom', fontsize=10)
    
    # Adjust layout and save figure
    plt.tight_layout()
    output_file = os.path.join(output_dir, "performance_comparison.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    print(f"Performance comparison visualization saved to {output_file}")


def save_metrics_to_file(address1, metrics1, address2, metrics2, output_dir):
    """Save metrics to a text file"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    output_file = os.path.join(output_dir, "performance_metrics.txt")
    
    with open(output_file, 'w') as f:
        f.write("=" * 50 + "\n")
        f.write("SOLANA ADDRESSES PERFORMANCE METRICS\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Address 1: {address1}\n")
        f.write(f"PnL: {metrics1['pnl_pct']:.2f}%\n")
        f.write(f"Sharpe Ratio: {metrics1['sharpe_ratio']:.2f}\n")
        f.write(f"Max Drawdown: {metrics1['max_drawdown']:.2f}%\n")
        f.write(f"Volatility: {metrics1['volatility']:.2f}%\n")
        f.write(f"Average Balance: {metrics1['avg_balance']:.4f} SOL\n\n")
        
        f.write(f"Address 2: {address2}\n")
        f.write(f"PnL: {metrics2['pnl_pct']:.2f}%\n")
        f.write(f"Sharpe Ratio: {metrics2['sharpe_ratio']:.2f}\n")
        f.write(f"Max Drawdown: {metrics2['max_drawdown']:.2f}%\n")
        f.write(f"Volatility: {metrics2['volatility']:.2f}%\n")
        f.write(f"Average Balance: {metrics2['avg_balance']:.4f} SOL\n\n")
        
        f.write("=" * 50 + "\n")
        f.write("PERFORMANCE COMPARISON\n")
        f.write("=" * 50 + "\n\n")
        
        # Compare PnL
        better_pnl = "Address 1" if metrics1['pnl_pct'] > metrics2['pnl_pct'] else "Address 2"
        f.write(f"Better PnL: {better_pnl}\n")
        
        # Compare Sharpe Ratio
        better_sharpe = "Address 1" if metrics1['sharpe_ratio'] > metrics2['sharpe_ratio'] else "Address 2"
        f.write(f"Better Sharpe Ratio: {better_sharpe}\n")
        
        # Compare Max Drawdown (lower is better)
        better_drawdown = "Address 1" if metrics1['max_drawdown'] < metrics2['max_drawdown'] else "Address 2"
        f.write(f"Lower Max Drawdown: {better_drawdown}\n")
        
        # Compare Volatility (lower is better)
        better_volatility = "Address 1" if metrics1['volatility'] < metrics2['volatility'] else "Address 2"
        f.write(f"Lower Volatility: {better_volatility}\n")
    
    print(f"Performance metrics saved to {output_file}")


def main():
    # Ensure output directory exists
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
    
    # Check if data files exist
    if not os.path.exists(ADDRESS_1_FILE) or not os.path.exists(ADDRESS_2_FILE):
        print(f"Error: Data files not found in {DATA_DIR}/")
        print("Please run monitor_sol_balance.py first to collect data.")
        return
    
    # Read balance data
    print(f"Reading data for {ADDRESS_1}...")
    data1 = read_balance_data(ADDRESS_1_FILE)
    
    print(f"Reading data for {ADDRESS_2}...")
    data2 = read_balance_data(ADDRESS_2_FILE)
    
    if not data1 or not data2:
        print("Error: Could not read data files.")
        return
    
    # Calculate metrics
    print("Calculating performance metrics...")
    metrics1 = calculate_metrics(data1)
    metrics2 = calculate_metrics(data2)
    
    # Create visualizations
    print("Creating performance visualizations...")
    plot_performance(ADDRESS_1, data1, metrics1, OUTPUT_DIR)
    plot_performance(ADDRESS_2, data2, metrics2, OUTPUT_DIR)
    
    # Compare performances
    print("Creating performance comparison...")
    compare_performances(ADDRESS_1, data1, metrics1, ADDRESS_2, data2, metrics2, OUTPUT_DIR)
    
    # Save metrics to file
    print("Saving performance metrics...")
    save_metrics_to_file(ADDRESS_1, metrics1, ADDRESS_2, metrics2, OUTPUT_DIR)
    
    print(f"\nAnalysis complete! Results saved to {OUTPUT_DIR}/")


if __name__ == "__main__":
    main()

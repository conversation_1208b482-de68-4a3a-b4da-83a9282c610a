#!/usr/bin/env python3
import requests
import json
import time
from datetime import datetime
import os
import csv

# Solana addresses to track
ADDRESS_1 = "C18EnJxfGUWvKKs74HGLcFgWevhtksvrcKekyqbgxKJD"
ADDRESS_2 = "46W2UspRnpB8x7ZajVTg2rVBo2ad3CbnXiMEV8Y96kQa"

# API endpoints
TOKENS_API = "https://api.phantom.app/tokens/v1"
PRICE_API = "https://api.phantom.app/price/v1"

# Headers for API requests
HEADERS = {
    "content-type": "application/json"
}

# Output file paths
DATA_DIR = "sol_balance_data"
ADDRESS_1_FILE = os.path.join(DATA_DIR, f"{ADDRESS_1}.csv")
ADDRESS_2_FILE = os.path.join(DATA_DIR, f"{ADDRESS_2}.csv")


def get_token_holdings(address):
    """Get token holdings for a specific Solana address"""
    payload = {
        "addresses": [{
            "chainId": "solana:101",
            "address": address
        }]
    }
    
    try:
        response = requests.post(f"{TOKENS_API}?isSolCompressedTokensEnabled=true", 
                              headers=HEADERS, 
                              json=payload,
                              timeout=10)
        
        if response.status_code == 200:
            return response.json()["tokens"]
        else:
            print(f"Error fetching tokens for {address}: {response.status_code}")
            return []
    except Exception as e:
        print(f"Exception fetching tokens for {address}: {e}")
        return []


def get_token_prices(token_addresses):
    """Get prices for multiple tokens in a single batch request"""
    # Create payload with all token addresses
    tokens_payload = []
    for address in token_addresses:
        tokens_payload.append({
            "token": {
                "chainId": "solana:101",
                "address": address,
                "resourceType": "address"
            }
        })
    
    payload = {"tokens": tokens_payload}
    
    try:
        response = requests.post(PRICE_API, headers=HEADERS, json=payload, timeout=10)
        
        prices = {}
        if response.status_code == 200:
            response_data = response.json()["prices"]
            for address in token_addresses:
                key = f"solana:101/address:{address}"
                if key in response_data:
                    prices[address] = response_data[key]
                else:
                    # print(f"No price data for {address}")
                    prices[address] = {"price": 0, "priceChange24h": 0}
        else:
            print(f"Error fetching prices: {response.status_code}")
            # Set default prices for all tokens
            for address in token_addresses:
                prices[address] = {"price": 0, "priceChange24h": 0}
        
        return prices
    except Exception as e:
        print(f"Exception fetching prices: {e}")
        # Set default prices for all tokens
        prices = {}
        for address in token_addresses:
            prices[address] = {"price": 0, "priceChange24h": 0}
        return prices


def calculate_portfolio_value(tokens):
    """Calculate total portfolio value in USD and SOL"""
    total_usd_value = 0
    token_values = []
    
    # Collect all token addresses first
    token_addresses = []
    token_info = []
    
    # Add SOL address
    sol_address = "So11111111111111111111111111111111111111112"
    token_addresses.append(sol_address)
    
    # Collect all other token addresses
    for token in tokens:
        token_type = token["type"]
        token_data = token["data"]
        
        # Get token address
        if token_type == "SolanaNative":
            token_address = sol_address
        else:  # SPL token
            token_address = token_data["mintAddress"]
            if token_address not in token_addresses:
                token_addresses.append(token_address)
        
        # Store token info for later use
        token_info.append({
            "type": token_type,
            "data": token_data,
            "address": token_address
        })
    
    # Get all token prices in a single request
    all_prices = get_token_prices(token_addresses)
    
    # Get SOL price
    sol_price = all_prices[sol_address]["price"] if sol_address in all_prices else 0
    
    # Calculate values for each token
    for token in token_info:
        token_data = token["data"]
        token_address = token["address"]
        
        # Get token amount (adjusted for decimals)
        decimals = token_data["decimals"]
        amount = float(token_data["amount"]) / (10 ** decimals)
        
        # Get token price
        price = all_prices[token_address]["price"] if token_address in all_prices else 0
        
        # Calculate value
        usd_value = amount * price
        sol_value = usd_value / sol_price if sol_price > 0 else 0
        
        token_values.append({
            "symbol": token_data["symbol"],
            "amount": amount,
            "usd_value": usd_value,
            "sol_value": sol_value
        })
        
        total_usd_value += usd_value
    
    total_sol_value = total_usd_value / sol_price if sol_price > 0 else 0
    
    return {
        "total_usd_value": total_usd_value,
        "total_sol_value": total_sol_value,
        "sol_price": sol_price,
        "token_values": token_values
    }


def ensure_data_directory():
    """Ensure the data directory exists"""
    if not os.path.exists(DATA_DIR):
        os.makedirs(DATA_DIR)


def ensure_csv_file(file_path):
    """Ensure the CSV file exists with headers"""
    if not os.path.exists(file_path):
        with open(file_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(["timestamp", "unix_timestamp", "sol_balance", "usd_balance", "sol_price"])


def record_balance(address, file_path):
    """Record the SOL balance for an address"""
    try:
        # Get token holdings
        tokens = get_token_holdings(address)
        
        # Calculate portfolio value
        portfolio = calculate_portfolio_value(tokens)
        if portfolio["total_sol_value"] == 0:
            return False
        
        # Get current timestamp
        now = datetime.now()
        timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
        unix_timestamp = int(time.time())
        
        # Record data
        with open(file_path, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                timestamp,
                unix_timestamp,
                portfolio["total_sol_value"],
                portfolio["total_usd_value"],
                portfolio["sol_price"]
            ])
        
        print(f"[{timestamp}] Recorded balance for {address}: {portfolio['total_sol_value']:.4f} SOL (${portfolio['total_usd_value']:.2f})")
        return True
    except Exception as e:
        print(f"Error recording balance for {address}: {e}")
        return False


def main():
    # Ensure data directory and files exist
    ensure_data_directory()
    ensure_csv_file(ADDRESS_1_FILE)
    ensure_csv_file(ADDRESS_2_FILE)
    
    print(f"Starting SOL balance monitoring for addresses:\n{ADDRESS_1}\n{ADDRESS_2}")
    print(f"Data will be saved to {DATA_DIR}/")
    print("Press Ctrl+C to stop monitoring...")
    
    try:
        while True:
            # Record balances
            record_balance(ADDRESS_1, ADDRESS_1_FILE)
            record_balance(ADDRESS_2, ADDRESS_2_FILE)
            
            # Wait for the next minute
            time.sleep(60)  # 60 seconds = 1 minute
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


if __name__ == "__main__":
    main()

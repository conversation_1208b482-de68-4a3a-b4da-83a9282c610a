from telethon import TelegramClient
from datetime import datetime, timedelta, timezone
from typing import List, Dict
import pandas as pd
from config import api_id, api_hash, phone_number, bloom_chat_name, bsc_chat_name, sol_patterns, bsc_patterns
import re

def convert_kmb_to_number(value_str: str) -> float:
    """Convert K/M/B suffix to actual number"""
    value_str = value_str.strip().upper()
    if value_str.endswith('K'):
        return float(value_str[:-1]) * 1000
    elif value_str.endswith('M'):
        return float(value_str[:-1]) * 1000000
    elif value_str.endswith('B'):
        return float(value_str[:-1]) * 1000000000
    return float(value_str)

def extract_bsc_trade_info(message):
    """Extract trade information from a BSC message."""
    # Handle both string and message object inputs
    text = message.text if hasattr(message, 'text') else message
    
    trade_info = {
        'chain': 'BSC',
        'status': 'success' if bsc_patterns['buy_success'].search(text) else 'unknown'
    }
    
    # Extract token name
    token_name_match = bsc_patterns['token_name'].search(text)
    trade_info['token_name'] = token_name_match.group(1) if token_name_match else 'Unknown'
    
    # Extract token amount
    token_amount_match = bsc_patterns['token_amount'].search(text)
    if token_amount_match:
        amount_str = token_amount_match.group(1)
        trade_info['token_amount'] = convert_kmb_to_number(amount_str)
    else:
        trade_info['token_amount'] = 0
    
    # Extract BNB value
    value_match = bsc_patterns['value_bnb'].search(text)
    trade_info['value_bnb'] = float(value_match.group(1)) if value_match else 0
    trade_info['value_sol'] = 0
    
    # Extract token address
    token_address_match = bsc_patterns['token_address'].search(text)
    if not token_address_match:
        # Try alternative format
        token_address_match = re.search(r'\[Token\] (0x[a-fA-F0-9]{40})', text)
    trade_info['token_address'] = token_address_match.group(1) if token_address_match else 'Unknown'
    
    # Extract target wallet
    # Try all possible formats for Copy Wallet section
    copy_wallet_patterns = [
        r'\*\*Copy Wallet\*\*\n`(0x[a-fA-F0-9]{40})`',  # Format with asterisks and backticks
        r'Copy Wallet\n`(0x[a-fA-F0-9]{40})`',          # Format with backticks
        r'Copy Wallet\n(0x[a-fA-F0-9]{40})',            # Format without backticks
        r'\*\*Copy Wallet\*\*\n(0x[a-fA-F0-9]{40})'     # Format with asterisks
    ]
    
    target_wallet = None
    for pattern in copy_wallet_patterns:
        match = re.search(pattern, text)
        if match:
            target_wallet = match.group(1)
            break
    
    trade_info['target_wallet'] = target_wallet if target_wallet else 'Unknown'
    
    # Extract price
    price_match = bsc_patterns['price'].search(text)
    trade_info['price'] = float(price_match.group(1)) if price_match else 0
    
    # Extract market cap
    market_cap_match = bsc_patterns['market_cap'].search(text)
    if market_cap_match:
        market_cap_str = market_cap_match.group(1)
        trade_info['market_cap'] = convert_kmb_to_number(market_cap_str)
    else:
        trade_info['market_cap'] = 0
    
    # Extract trading fee
    trading_fee_match = bsc_patterns['trading_fee'].search(text)
    trade_info['trading_fee'] = float(trading_fee_match.group(1)) if trading_fee_match else 0
    
    # Extract wallet address
    my_wallet_match = bsc_patterns['my_wallet'].search(text)
    if not my_wallet_match:
        # Try alternative format
        my_wallet_match = re.search(r'\[Wallet \d+\] (0x[a-fA-F0-9]{40})', text)
    trade_info['wallet_address'] = my_wallet_match.group(1) if my_wallet_match else 'Unknown'
    
    # Add timestamp
    if hasattr(message, 'date'):
        trade_info['date'] = message.date
    else:
        timestamp_match = re.search(r'(\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2})', text)
        if timestamp_match:
            try:
                trade_info['date'] = datetime.strptime(timestamp_match.group(1), '%m/%d/%Y %H:%M:%S')
            except ValueError:
                trade_info['date'] = None
        else:
            trade_info['date'] = None
    
    return trade_info

async def extract_trade_info(message):
    """Extract trade information from Solana message"""
    text = message.text
    
    # Determine status
    if sol_patterns['pending_buy'].search(text):
        status = 'Pending Buy'
    elif sol_patterns['success_buy'].search(text):
        status = 'Success Buy'
    else:
        return None
    
    # Extract other information
    token_name_match = sol_patterns['token_name'].search(text)
    token_name = token_name_match.group(1) if token_name_match else 'Unknown'
    
    token_address_match = sol_patterns['token_address'].search(text)
    token_address = token_address_match.group(1) if token_address_match else 'Unknown'
    
    target_wallet_match = sol_patterns['target_wallet'].search(text)
    target_wallet = target_wallet_match.group(1) if target_wallet_match else 'Unknown'
    
    value_sol_match = sol_patterns['value_sol'].search(text)
    value_sol = value_sol_match.group(1) if value_sol_match else '0'
    
    tx_hash_match = sol_patterns['tx_hash'].search(text)
    tx_hash = tx_hash_match.group(1) if tx_hash_match else 'Unknown'
    
    return {
        'date': message.date,
        'chain': 'Solana',
        'status': status,
        'token_name': token_name,
        'token_address': token_address,
        'target_wallet': target_wallet,
        'value_sol': value_sol,
        'value_bnb': 0,
        'tx_hash': tx_hash
    }

async def extract_telegram_data(days: int = 7):
    """Extract trade data from Solana Telegram messages"""
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    trades = []
    
    # Connect to Telegram
    client = TelegramClient('bloom_session', api_id, api_hash)
    await client.start(phone_number)
    
    print(f"Connected to Telegram. Fetching messages from the last {days} days...")
    
    async for dialog in client.iter_dialogs():
        if bloom_chat_name.lower() in dialog.name.lower():
            latest_message = await client.get_messages(dialog, limit=1)
            if not latest_message:
                continue
                
            max_id = latest_message[0].id
            min_date = cutoff_date
            
            messages = []
            while True:
                batch = await client.get_messages(
                    dialog,
                    limit=100,
                    max_id=max_id,
                    offset_date=min_date
                )
                
                if not batch:
                    break
                    
                # Check if any message is older than our cutoff date
                if batch[-1].date < cutoff_date:
                    # Only add messages newer than cutoff date
                    messages.extend([msg for msg in batch if msg.date >= cutoff_date])
                    break
                    
                messages.extend(batch)
                max_id = batch[-1].id - 1
            
            print(f"Found {len(messages)} messages in {dialog.name} from the last {days} days")
            
            for message in messages:
                if message.text and ('🟡 Copy Trade Buy Pending' in message.text or 
                                    '🟢 Copy Trade Buy Success' in message.text):
                    trade_info = await extract_trade_info(message)
                    if trade_info:
                        trades.append(trade_info)
    
    await client.disconnect()
    return trades

async def extract_bsc_telegram_data(days: int = 7):
    """Extract trade data from BSC Telegram messages"""
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    trades = []
    
    # Connect to Telegram
    client = TelegramClient('bloom_session', api_id, api_hash)
    await client.start(phone_number)
    
    print(f"Connected to Telegram. Fetching BSC messages from the last {days} days...")
    
    async for dialog in client.iter_dialogs():
        if bsc_chat_name.lower() in dialog.name.lower():
            latest_message = await client.get_messages(dialog, limit=1)
            if not latest_message:
                continue
                
            max_id = latest_message[0].id
            min_date = cutoff_date
            
            messages = []
            while True:
                batch = await client.get_messages(
                    dialog,
                    limit=100,
                    max_id=max_id,
                    offset_date=min_date
                )
                
                if not batch:
                    break
                    
                # Check if any message is older than our cutoff date
                if batch[-1].date < cutoff_date:
                    # Only add messages newer than cutoff date
                    messages.extend([msg for msg in batch if msg.date >= cutoff_date])
                    break
                    
                messages.extend(batch)
                max_id = batch[-1].id - 1
            
            print(f"Found {len(messages)} messages in {dialog.name} from the last {days} days")
            
            for message in messages:
                if message.text and '✅ Copy buy' in message.text:
                    trade_info = extract_bsc_trade_info(message)
                    if trade_info:
                        trades.append(trade_info)
    
    await client.disconnect()
    return trades 
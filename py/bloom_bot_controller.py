import asyncio
import argparse
from utils.telegram_utils import connect_to_telegram, find_bloom_bot, get_copy_message, click_close_button, turn_off_buy_fixed
from utils.wallet_operations import toggle_wallet, remove_wallet, copy_multiple_wallets, move_multiple_wallets_to_gem, update_multiple_wallets_max_buy
from utils.message_utils import get_latest_message
from utils.file_utils import get_good_wallets

async def main():
    parser = argparse.ArgumentParser(description='Control Bloom Solana US1 bot copy trading')
    parser.add_argument('command', choices=['toggle', 'copy', 'remove', 'move_to_gem', 'turn_off_buy_fixed', 'update_gem_sol'], help='Command to execute: toggle (enable/disable), copy (add new wallet), remove (delete wallet), move_to_gem (move wallet to GEM), turn_off_buy_fixed (turn off Buy Fixed for all wallets), or update_gem_sol (update Max Buy for all good wallets)')
    parser.add_argument('wallet_addresses', nargs='?', help='Comma-separated list of wallet addresses to operate on (not required for turn_off_buy_fixed or update_gem_sol)')
    parser.add_argument('--state', choices=['on', 'off'], help='Whether to enable (on) or disable (off) copy trading (required for toggle command)')
    parser.add_argument('--sol-value', type=float, help='The new Max Buy value in SOL (required for update_gem_sol command)')
    
    args = parser.parse_args()
    
    # Connect to Telegram
    client = await connect_to_telegram()
    
    try:
        # Find the Bloom Solana US1 bot
        bloom_bot = await find_bloom_bot(client)
        if not bloom_bot:
            return
        
        if args.command == 'turn_off_buy_fixed':
            print("Turning off Buy Fixed for all wallets...")
            await turn_off_buy_fixed(client, bloom_bot)
            return
        
        if args.command == 'update_gem_sol':
            if not args.sol_value:
                print("Error: --sol-value is required for the update_gem_sol command")
                return
            
            # Get the list of good wallets
            good_wallets = get_good_wallets()
            if not good_wallets:
                print("No good wallets found. Please add some wallets to the good_wallets list first.")
                return
            
            print(f"Updating Max Buy for {len(good_wallets)} good wallet(s) to {args.sol_value} SOL...")
            
            # Send /copy command once at the beginning
            target_message = await get_copy_message(client, bloom_bot)
            if not target_message:
                return
            
            # Update the Max Buy value for all good wallets
            await update_multiple_wallets_max_buy(client, bloom_bot, target_message, good_wallets, args.sol_value)
            
            # Click the Close button at the end
            await click_close_button(client, bloom_bot)
            return
        
        # For other commands, wallet_addresses is required
        if not args.wallet_addresses:
            print("Error: wallet_addresses is required for this command")
            return
            
        # Split the wallet addresses
        wallet_addresses = [addr.strip() for addr in args.wallet_addresses.split(',')]
        
        # Send /copy command once at the beginning
        target_message = await get_copy_message(client, bloom_bot)
        if not target_message:
            return
        
        if args.command == 'toggle':
            if not args.state:
                print("Error: --state is required for the toggle command")
                return
            
            enable = args.state == 'on'
            print(f"Processing {len(wallet_addresses)} wallet(s) for {'enabling' if enable else 'disabling'} copy trading.")
            
            # Process each wallet address
            for wallet_address in wallet_addresses:
                await toggle_wallet(client, bloom_bot, target_message, wallet_address)
            
            # Click the Close button at the end
            await click_close_button(client, bloom_bot)
                
        elif args.command == 'copy':
            print(f"Processing {len(wallet_addresses)} wallet(s) for adding to copy trading.")
            
            # Use the optimized function for copying multiple wallets
            await copy_multiple_wallets(client, bloom_bot, target_message, wallet_addresses)
            
            # Click the Close button at the end
            await click_close_button(client, bloom_bot)
                
        elif args.command == 'remove':
            print(f"Processing {len(wallet_addresses)} wallet(s) for removal from copy trading.")
            
            # Process each wallet address
            for wallet_address in wallet_addresses:
                await remove_wallet(client, bloom_bot, target_message, wallet_address)
                target_message = await get_latest_message(client, bloom_bot)
            
            # Click the Close button at the end
            await click_close_button(client, bloom_bot)
                
        elif args.command == 'move_to_gem':
            print(f"Processing {len(wallet_addresses)} wallet(s) for moving to GEM configuration.")
            
            # Use the function for moving multiple wallets to GEM
            await move_multiple_wallets_to_gem(client, bloom_bot, target_message, wallet_addresses)
            
            # Click the Close button at the end
            await click_close_button(client, bloom_bot)
    
    except Exception as e:
        print(f"Error: {str(e)}")
    
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main()) 
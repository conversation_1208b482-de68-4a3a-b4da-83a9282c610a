#!/usr/bin/env python3
"""
Trade Analyzer

This script analyzes trade history from a JSON file, excluding trades from sandwich bots,
and identifies the top wallets by USD trading volume.
"""

import json
import argparse
from collections import defaultdict
from typing import List, Dict, Any

def load_trade_data(file_path: str) -> List:
    """
    Load trade data from a JSON file.
    
    Args:
        file_path: Path to the JSON file containing trade data
        
    Returns:
        Loaded trade data
    """
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        exit(1)
    except json.JSONDecodeError:
        print(f"Error: File '{file_path}' contains invalid JSON.")
        exit(1)

def analyze_trades(data: List) -> Dict[str, float]:
    """
    Analyze trades, excluding sandwich bots, and sum up amount_usd by maker.
    
    Args:
        data: List of trade data arrays
        
    Returns:
        Dictionary mapping maker addresses to their total amount_usd
    """
    maker_totals = defaultdict(float)
    
    for trade_group in data:
        for trade in trade_group:
            # Skip trades from sandwich bots
            if 'maker_tags' in trade and 'sandwich_bot' in trade.get('maker_tags', []):
                continue
                
            # Extract maker and amount_usd
            maker = trade.get('maker')
            amount_usd = float(trade.get('amount_usd', 0))
            
            if maker and amount_usd > 0:
                maker_totals[maker] += amount_usd
    
    return maker_totals

def get_top_makers(maker_totals: Dict[str, float], limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get the top makers by total amount_usd.
    
    Args:
        maker_totals: Dictionary mapping maker addresses to their total amount_usd
        limit: Number of top makers to return
        
    Returns:
        List of dicts containing maker addresses and their total amount_usd
    """
    # Sort makers by total amount_usd in descending order and take the top 'limit'
    top_makers = sorted(
        [{'maker': maker, 'total_amount_usd': amount} for maker, amount in maker_totals.items()],
        key=lambda x: x['total_amount_usd'],
        reverse=True
    )[:limit]
    
    return top_makers

def main():
    parser = argparse.ArgumentParser(description='Analyze trade history and find top makers by USD volume')
    parser.add_argument('file_path', help='Path to the JSON file containing trade data')
    parser.add_argument('--top', type=int, default=10, help='Number of top makers to display (default: 10)')
    args = parser.parse_args()
    
    # Load data
    data = load_trade_data(args.file_path)
    
    # Analyze trades
    maker_totals = analyze_trades(data)
    
    # Get top makers
    top_makers = get_top_makers(maker_totals, args.top)
    
    # Print results
    print(f"\nTop {args.top} Makers by USD Volume (excluding sandwich bots):\n")
    print(f"{'Rank':<5}{'Maker Address':<45}{'Total USD Volume':<15}")
    print("-" * 65)
    
    for i, maker_data in enumerate(top_makers, 1):
        print(f"{i:<5}{maker_data['maker']:<45}${maker_data['total_amount_usd']:<15,.2f}")
    
    print(f"\nAnalyzed {len(maker_totals)} unique makers.")

if __name__ == "__main__":
    main() 
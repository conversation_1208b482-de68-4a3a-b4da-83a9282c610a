from telethon import TelegramClient
import re
import asyncio
import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd
from dotenv import load_dotenv
import json
import requests
import time
import argparse

# Load environment variables
load_dotenv()

# Telegram API credentials from environment variables
api_id = os.getenv('TELEGRAM_API_ID')
api_hash = os.getenv('TELEGRAM_API_HASH')
phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')
pump_hound_channel = "Pump Hound Signal"

# Validate required environment variables
if not all([api_id, api_hash, phone_number]):
    raise ValueError("Missing required environment variables. Please set TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE_NUMBER in .env file")

# Regex patterns to extract data
patterns = {
    'buy_signal': re.compile(r'🟢 Buy \*\*(.+?)\*\*(?:\n|$)'),
    'token_address': re.compile(r'📝 CA: `([0-9a-zA-Z]{42,})`')
}

# API endpoint for sell signals
SELL_SIGNAL_API = "https://wallet-dev.kryptogo.app/v1/token_signal/sell"
BUY_SIGNAL_API = "https://wallet-dev.kryptogo.app/v1/token_signal/buy"

async def get_token_symbols_from_telegram(token_addresses):
    """Get token symbols from Telegram messages for the given token addresses"""
    # Connect to Telegram
    client = TelegramClient('pump_hound_session', api_id, api_hash)
    await client.start(phone_number)
    
    print(f"Connected to Telegram. Fetching token symbols for {len(token_addresses)} tokens...")
    
    # Find the Pump Hound Signal channel
    pump_hound_channel_entity = None
    async for dialog in client.iter_dialogs():
        if pump_hound_channel.lower() in dialog.name.lower():
            pump_hound_channel_entity = dialog
            break
    
    if not pump_hound_channel_entity:
        print(f"Could not find {pump_hound_channel} channel")
        await client.disconnect()
        return {}

    # Dictionary to store token symbols
    token_symbol_map = {}
    token_addresses_set = set(token_addresses)
    
    # Get messages from the channel (more than enough to cover recent signals)
    messages = await client.get_messages(
        pump_hound_channel_entity,
        limit=2000  # Adjust as needed
    )
    
    print(f"Fetched {len(messages)} messages from Telegram to find token symbols")
    
    # Find buy signals for the token addresses we're interested in
    for message in messages:
        if not message.text:
            continue
            
        token_address_match = patterns['token_address'].search(message.text)
        if not token_address_match:
            continue
            
        token_address = token_address_match.group(1)
        
        # If this is a token address we're looking for and we haven't found its symbol yet
        if token_address in token_addresses_set and token_address not in token_symbol_map:
            # Extract token symbol
            buy_signal_match = patterns['buy_signal'].search(message.text)
            if buy_signal_match:
                token_symbol = buy_signal_match.group(1).strip()
                token_symbol_map[token_address] = token_symbol
    
    await client.disconnect()
    
    # For tokens where we couldn't find the symbol, use a placeholder
    for token_address in token_addresses:
        if token_address not in token_symbol_map:
            token_symbol_map[token_address] = "Unknown"
    
    return token_symbol_map

def fetch_sell_signals():
    """Fetch sell signals from the API"""
    try:
        response = requests.get(SELL_SIGNAL_API)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error fetching data from API: {e}")
        return {"code": -1, "data": []}

def fetch_buy_signals():
    """Fetch buy signals from the API"""
    try:
        response = requests.get(BUY_SIGNAL_API)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error fetching buy signals from API: {e}")
        return {"code": -1, "data": []}

def is_in_time_range(timestamp, yesterday_noon_timestamp, today_noon_timestamp):
    """Check if the timestamp is within the specified time range"""
    return yesterday_noon_timestamp <= timestamp <= today_noon_timestamp

async def analyze_signals(days=2):
    """Analyze signals from the API and find top performing tokens"""
    # Calculate date range (yesterday 12pm to today 12pm in UTC+8)
    utc_plus_8 = timezone(timedelta(hours=8))
    now = datetime.now(utc_plus_8)
    today_noon = now.replace(hour=12, minute=0, second=0, microsecond=0)
    yesterday_noon = today_noon - timedelta(days=days)
    
    # Convert to Unix timestamps for comparison with API data
    today_noon_timestamp = int(today_noon.timestamp())
    yesterday_noon_timestamp = int(yesterday_noon.timestamp())
    
    print(f"Fetching signals from {yesterday_noon} to {today_noon} (UTC+8)...")
    
    # Fetch both buy and sell signals from the API
    sell_api_response = fetch_sell_signals()
    buy_api_response = fetch_buy_signals()
    
    if (sell_api_response["code"] != 0 and buy_api_response["code"] != 0) or (not sell_api_response["data"] and not buy_api_response["data"]):
        print("No valid data received from the APIs")
        return
    
    # Combine signals from both APIs
    signals = []
    
    # Process sell signals
    for signal in sell_api_response.get("data", []):
        if signal.get("buy_entry_time") is None:
            continue
        if is_in_time_range(signal["buy_entry_time"], yesterday_noon_timestamp, today_noon_timestamp):
            signals.append({
                "token_address": signal["token_address"],
                "highest_gain": signal["highest_gain"],
                "buy_entry_time": signal["buy_entry_time"],
                "type": "sell"
            })
    
    # Process buy signals
    for signal in buy_api_response.get("data", []):
        if signal.get("emit_time") is None:
            continue
        if is_in_time_range(signal["emit_time"], yesterday_noon_timestamp, today_noon_timestamp):
            # Calculate gain using highest_price/buy_entry_price-1
            gain = (signal["highest_price"] / signal["buy_entry_price"]) - 1
            signals.append({
                "token_address": signal["token_address"],
                "highest_gain": gain,
                "buy_entry_time": signal["emit_time"],
                "type": "buy"
            })
    
    if not signals:
        print("No signals found in the specified time range")
        return
    
    print(f"Found {len(signals)} signals within the specified time range")
    
    # Extract token addresses to fetch their symbols
    token_addresses = [signal["token_address"] for signal in signals]
    
    # Get token symbols from Telegram
    token_symbol_map = await get_token_symbols_from_telegram(token_addresses)
    
    # Prepare data for analysis
    analyzed_tokens = []
    
    for signal in signals:
        token_address = signal["token_address"]
        highest_gain = signal["highest_gain"] * 100  # Convert to percentage
        buy_time = datetime.fromtimestamp(signal["buy_entry_time"], tz=timezone.utc)
        
        analyzed_tokens.append({
            'token_symbol': token_symbol_map.get(token_address, "Unknown"),
            'token_address': token_address,
            'highest_gain': highest_gain,
            'buy_time': buy_time,
            'signal_type': signal["type"]
        })
    
    # Sort by highest gain in descending order
    analyzed_tokens.sort(key=lambda x: x['highest_gain'], reverse=True)
    
    # Take top 10
    top_tokens = analyzed_tokens[:10]
    # print(top_tokens)
    
    # Create UTC+8 timezone
    utc_plus_8 = timezone(timedelta(hours=8))
    
    # Print results
    print("\nTop 10 tokens with highest gain (sorted by highest gain):")
    print("=" * 100)
    print(f"{'Token Symbol':<15} {'Token Address':<45} {'Highest Gain':<13} {'Buy Time (UTC+8)':<25}")
    print("-" * 100)
    
    for token in top_tokens:
        highest_gain = f"{token['highest_gain']:.2f}%"
        # Convert buy_time to UTC+8
        buy_time_utc8 = token['buy_time'].astimezone(utc_plus_8)
        print(f"{token['token_symbol']:<15} {token['token_address']:<45} {highest_gain:<13} {buy_time_utc8.strftime('%Y-%m-%d %H:%M:%S'):<25}")
    
    return top_tokens

async def main():
    """Main function to run the analysis"""
    parser = argparse.ArgumentParser(description='Analyze pump signals')
    parser.add_argument('--days', type=int, default=2, help='Number of days to analyze (default: 2)')
    args = parser.parse_args()
    
    await analyze_signals(args.days)

if __name__ == "__main__":
    asyncio.run(main()) 
import pandas as pd
import os
import argparse
import json
from datetime import datetime, timed<PERSON><PERSON>

def analyze_wallet_performance(csv_file):
    """
    Analyze wallet performance from the sold_tokens.csv file.
    
    Args:
        csv_file (str): Path to the CSV file containing token sale data
        
    Returns:
        pandas.DataFrame: DataFrame containing wallet performance statistics
    """
    # Check if file exists
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"File not found: {csv_file}")
    
    # Read the CSV file
    df = pd.read_csv(csv_file)
    
    # Check if required columns exist
    required_columns = ['timestamp', 'token_mint', 'symbol', 'price_change', 'minutes_monitored', 'related_wallets']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")
    
    # Convert timestamp to datetime if it's not already
    if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
    
    # Create boolean masks for different holding time ranges
    df['holding_time_15_480'] = (df['minutes_monitored'] >= 15) & (df['minutes_monitored'] <= 480)
    df['holding_time_lt_3'] = df['minutes_monitored'] <= 3
    
    # Group by wallet address
    wallet_stats = df.groupby('related_wallets').agg({
        'price_change': ['mean', 'max', 'min'],
        'minutes_monitored': 'mean',
        'token_mint': 'count',  # Count the number of trades
        'holding_time_15_480': 'sum',  # Count trades with holding time between 15-480 minutes
        'holding_time_lt_3': 'sum',  # Count trades with holding time less than 3 minutes
        'timestamp': 'max'  # Get the latest timestamp for each wallet
    }).reset_index()
    
    # Rename columns for clarity
    wallet_stats.columns = ['wallet_address', 'avg_profit_ratio', 'max_profit_ratio', 'min_profit_ratio', 'avg_holding_time_minutes', 'trade_count', 'trades_15_480_min', 'trades_lt_3_min', 'latest_timestamp']
    
    # Calculate the ratios
    wallet_stats['ratio_15_480_min'] = wallet_stats['trades_15_480_min'] / wallet_stats['trade_count']
    wallet_stats['ratio_lt_3_min'] = wallet_stats['trades_lt_3_min'] / wallet_stats['trade_count']
    
    # Convert holding time to hours for better readability
    wallet_stats['avg_holding_time_hours'] = wallet_stats['avg_holding_time_minutes'] / 60
    
    # Round numeric columns
    numeric_columns = ['avg_profit_ratio', 'max_profit_ratio', 'min_profit_ratio', 'avg_holding_time_minutes', 'avg_holding_time_hours', 'ratio_15_480_min', 'ratio_lt_3_min']
    wallet_stats[numeric_columns] = wallet_stats[numeric_columns].round(4)
    
    # Sort by average profit ratio in descending order
    wallet_stats = wallet_stats.sort_values('avg_profit_ratio', ascending=False)
    
    return wallet_stats

def load_tracked_wallets():
    """
    Load tracked wallet addresses from the JSON file.
    
    Returns:
        list: List of wallet addresses
    """
    json_path = os.path.expanduser("~/.solana-bot/tracked_wallets.json")
    
    if not os.path.exists(json_path):
        raise FileNotFoundError(f"Tracked wallets file not found: {json_path}")
    
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    if 'wallets' not in data:
        raise ValueError("No 'wallets' key found in the JSON file")
    
    return data['wallets']

def identify_wallets_to_remove(wallet_stats):
    """
    Identify wallets that should be removed based on specific criteria.
    
    Args:
        wallet_stats (pandas.DataFrame): DataFrame containing wallet performance statistics
        
    Returns:
        list: List of dictionaries containing wallet data for removal
    """
    wallets_to_remove = []
    current_time = datetime.now()
    
    for _, row in wallet_stats.iterrows():
        removal_reason = None
        
        if row['trade_count'] >= 3:
            # Check for holding time criteria
            if row['ratio_lt_3_min'] > 0.25:
                removal_reason = "holding_time_criteria"
            
            # Check for profit ratio criteria
            elif row['avg_profit_ratio'] * row['trade_count'] < -3:
                removal_reason = "profit_ratio_criteria"
        
        # Check for inactivity criteria (48h)
        time_diff = current_time - row['latest_timestamp']
        if time_diff > timedelta(hours=48):
            removal_reason = "inactivity"
        
        if removal_reason:
            wallets_to_remove.append({
                'wallet_address': row['wallet_address'],
                'trade_count': row['trade_count'],
                'ratio_15_480_min': row['ratio_15_480_min'],
                'ratio_lt_3_min': row['ratio_lt_3_min'],
                'avg_profit_ratio': row['avg_profit_ratio'],
                'latest_timestamp': row['latest_timestamp'],
                'removal_reason': removal_reason
            })
    
    return wallets_to_remove

def main():
    parser = argparse.ArgumentParser(description='Analyze wallet performance from sold_tokens.csv')
    parser.add_argument('--input', type=str, default='sold_tokens.csv', help='Path to the input CSV file (default: sold_tokens.csv)')
    parser.add_argument('--output', type=str, default='wallet_performance_stats.csv', help='Path to the output CSV file (default: wallet_performance_stats.csv)')
    args = parser.parse_args()
    
    try:
        # Load tracked wallets
        tracked_wallets = load_tracked_wallets()
        
        # Analyze wallet performance
        wallet_stats = analyze_wallet_performance(args.input)
        
        # Filter to only include tracked wallets
        wallet_stats = wallet_stats[wallet_stats['wallet_address'].isin(tracked_wallets)]
        
        # Save to CSV
        wallet_stats.to_csv(args.output, index=False)
        
        # Identify wallets to remove
        wallets_to_remove = identify_wallets_to_remove(wallet_stats)
        
        # Print wallets to remove with all metrics in a single line
        for wallet in wallets_to_remove:
            removal_info = f"Remove wallet {wallet['wallet_address']} because {wallet['removal_reason']}: trade_count={wallet['trade_count']}, ratio_15_480_min={wallet['ratio_15_480_min']:.4f}, ratio_lt_3_min={wallet['ratio_lt_3_min']:.4f}, avg_profit_ratio={wallet['avg_profit_ratio']:.4f}"
            if wallet['removal_reason'] == "inactivity":
                removal_info += f", last_activity={wallet['latest_timestamp']}, reason=inactive for >48h"
            print(removal_info)
        
        # Generate command to remove all wallets in one command
        if wallets_to_remove:
            wallet_addresses = " ".join([wallet['wallet_address'] for wallet in wallets_to_remove])
            print(f"\nCommand to remove all wallets:")
            print(f"cargo run --bin manage_wallets remove {wallet_addresses}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main() 
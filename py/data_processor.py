from typing import List, Dict
import pandas as pd
from datetime import datetime

def process_holdings(holdings: List[Dict]) -> List[Dict]:
    """Process holdings to get sold positions with profit data"""
    sold_positions = []
    for holding in holdings:
        # Skip if holding is not a dictionary
        if not isinstance(holding, dict):
            print(f"Warning: Skipping non-dictionary holding: {holding}")
            continue
            
        # Skip if balance is not 0
        # if holding.get('balance') != '0':
        #     continue
            
        try:
            # Get token data safely
            token_data = holding.get('token', {})
            if not isinstance(token_data, dict):
                print(f"Warning: Invalid token data structure: {token_data}")
                continue
                
            total_profit = float(holding.get('total_profit', '0'))
            total_profit_pnl = float(holding.get('total_profit_pnl', '0'))
            avg_cost = float(holding.get('avg_cost', '0'))
            avg_sold = float(holding.get('avg_sold', '0'))
            
            if avg_cost > 0:
                profit_rate = (avg_sold - avg_cost) / avg_cost
            else:
                profit_rate = 0
            
            position = {
                'token_address': token_data.get('address', ''),
                'token_name': token_data.get('name', ''),
                'token_symbol': token_data.get('symbol', ''),
                'total_profit': total_profit,
                'total_profit_pnl': total_profit_pnl,
                'profit_rate': profit_rate,
                'avg_cost': avg_cost,
                'avg_sold': avg_sold,
                'last_active_timestamp': holding.get('last_active_timestamp'),
                'start_holding_at': holding.get('start_holding_at'),
                'end_holding_at': holding.get('end_holding_at')
            }
            
            # Preserve wallet_address if it exists
            if 'wallet_address' in holding:
                position['wallet_address'] = holding['wallet_address']
            
            sold_positions.append(position)
        except (ValueError, KeyError) as e:
            print(f"Error processing holding: {e}")
            print(f"Holding data: {holding}")
            continue
    
    return sold_positions

def process_trades_and_holdings(trades_df: pd.DataFrame, sol_positions: List[Dict], bsc_positions: List[Dict], 
                              sol_pnl: List[Dict], bsc_pnl: List[Dict]) -> pd.DataFrame:
    """Process trades and holdings data to create combined analysis"""
    if trades_df.empty:
        return pd.DataFrame()
    
    # Convert date columns to datetime
    trades_df['date'] = pd.to_datetime(trades_df['date'])
    
    # Split trades by chain
    sol_trades = trades_df[trades_df['chain'] == 'Solana'].copy()
    bsc_trades = trades_df[trades_df['chain'] == 'BSC'].copy()
    
    # Split holdings by chain (both Solana and BSC holdings have wallet_address now)
    sol_holdings = pd.DataFrame(sol_positions)
    bsc_holdings = pd.DataFrame(bsc_positions)
    
    # Process Solana merges
    if not sol_trades.empty and not sol_holdings.empty:
        sol_merged = pd.merge(
            sol_holdings,
            sol_trades,
            on='token_address',
            how='outer',
            suffixes=('', '_bloom')
        )
        
        # Handle duplicate columns for Solana
        if 'token_name_bloom' in sol_merged.columns:
            sol_merged['token_name'] = sol_merged['token_name'].fillna(sol_merged['token_name_bloom'])
            sol_merged = sol_merged.drop('token_name_bloom', axis=1)
    elif not sol_trades.empty:
        sol_merged = sol_trades
    elif not sol_holdings.empty:
        sol_merged = sol_holdings
    else:
        sol_merged = pd.DataFrame()
    
    # Process BSC merges
    if not bsc_trades.empty:
        # Convert wallet_address and token_address to lowercase for consistent matching
        bsc_trades['wallet_address'] = bsc_trades['wallet_address'].str.lower()
        bsc_trades['token_address'] = bsc_trades['token_address'].str.lower()
        bsc_trades['merge_key'] = bsc_trades['wallet_address'] + '_' + bsc_trades['token_address']
    
    if not bsc_holdings.empty:
        # Convert wallet_address and token_address to lowercase for consistent matching
        bsc_holdings['wallet_address'] = bsc_holdings['wallet_address'].str.lower()
        bsc_holdings['token_address'] = bsc_holdings['token_address'].str.lower()
        bsc_holdings['merge_key'] = bsc_holdings['wallet_address'] + '_' + bsc_holdings['token_address']
    
    if not bsc_trades.empty and not bsc_holdings.empty:
        # Perform the merge
        bsc_merged = pd.merge(
            bsc_holdings,
            bsc_trades,
            on='merge_key',
            how='outer',
            suffixes=('', '_bloom')
        )
        
        # Handle duplicate columns for BSC
        if 'token_name_bloom' in bsc_merged.columns:
            bsc_merged['token_name'] = bsc_merged['token_name'].fillna(bsc_merged['token_name_bloom'])
            bsc_merged = bsc_merged.drop('token_name_bloom', axis=1)
        if 'token_address_bloom' in bsc_merged.columns:
            bsc_merged['token_address'] = bsc_merged['token_address'].fillna(bsc_merged['token_address_bloom'])
            bsc_merged = bsc_merged.drop('token_address_bloom', axis=1)
    elif not bsc_trades.empty:
        bsc_merged = bsc_trades
    elif not bsc_holdings.empty:
        bsc_merged = bsc_holdings
    else:
        bsc_merged = pd.DataFrame()
    
    # Combine the results
    merged_df = pd.concat([sol_merged, bsc_merged], ignore_index=True)
    
    # Then, merge with PNL data if available
    if sol_pnl or bsc_pnl:
        pnl_data = {
            'total_profit_7d': sum(p.get('total_profit', 0) for p in sol_pnl) + 
                             sum(p.get('total_profit', 0) for p in bsc_pnl),
            'win_rate_7d': sum(p.get('win_rate', 0) for p in sol_pnl) + 
                          sum(p.get('win_rate', 0) for p in bsc_pnl),
            'total_trades_7d': sum(p.get('total_trades', 0) for p in sol_pnl) + 
                             sum(p.get('total_trades', 0) for p in bsc_pnl),
            'winning_trades_7d': sum(p.get('winning_trades', 0) for p in sol_pnl) + 
                               sum(p.get('winning_trades', 0) for p in bsc_pnl)
        }
        pnl_df = pd.DataFrame([pnl_data])
        merged_df = pd.concat([merged_df, pnl_df], axis=1)
    
    # Fill NaN values with appropriate defaults
    merged_df = merged_df.fillna({
        'total_profit': 0,
        'total_profit_pnl': 0,
        'profit_rate': 0,
        'avg_cost': 0,
        'avg_sold': 0,
        'value_sol': 0,
        'value_bnb': 0,
        'token_amount': 0,
        'price': 0,
        'market_cap': 0,
        'trading_fee': 0
    })
    
    # Deduplicate rows based on token_address, wallet_address, keeping earliest date
    # print("\nDeduplicating merged data...")
    # print(f"Original number of rows: {len(merged_df)}")
    
    # Create a copy of the DataFrame for deduplication
    dedup_df = merged_df.copy()
    
    # Convert date to datetime if it's not already
    if 'date' in dedup_df.columns:
        dedup_df['date'] = pd.to_datetime(dedup_df['date'])
    
    # Sort by date to ensure we keep the earliest entry
    dedup_df = dedup_df.sort_values('date')
    
    # Drop duplicates keeping the first occurrence (earliest date)
    dedup_df = dedup_df.drop_duplicates(
        subset=['token_address', 'wallet_address'],
        keep='first'
    )
    
    # print(f"Number of rows after deduplication: {len(dedup_df)}")
    return dedup_df

def process_wallet_analysis(trades_df: pd.DataFrame, sol_positions: List[Dict], bsc_positions: List[Dict]) -> pd.DataFrame:
    """Process trades data to analyze target wallet performance"""
    if trades_df.empty:
        return pd.DataFrame()
    
    # Convert value columns to float
    trades_df['value_sol'] = pd.to_numeric(trades_df['value_sol'], errors='coerce')
    trades_df['value_bnb'] = pd.to_numeric(trades_df['value_bnb'], errors='coerce')
    
    # Calculate success rate based on status
    trades_df['success_rate'] = trades_df['status'].apply(lambda x: 1 if 'Success' in str(x) else 0)
    
    # Calculate win rate (same as success rate for now)
    trades_df['win_rate'] = trades_df['success_rate']
    
    # Calculate profit based on chain using actual PNL data
    def calculate_profit(row):
        if row['chain'] == 'BSC':
            # Find matching holding for this trade
            holding = next((h for h in bsc_positions 
                          if h.get('token_address', '').lower() == row['token_address'].lower() 
                          and h.get('wallet_address', '').lower() == row['wallet_address'].lower()), None)
            if holding:
                return float(holding.get('total_profit', 0))
            return 0
        else:
            # Find matching holding for Solana
            holding = next((h for h in sol_positions 
                          if h.get('token_address', '').lower() == row['token_address'].lower()), None)
            if holding:
                return float(holding.get('total_profit', 0))
            return 0
    
    trades_df['profit'] = trades_df.apply(calculate_profit, axis=1)
    
    # Calculate profit rate based on holdings data
    def calculate_profit_rate(row):
        if row['chain'] == 'BSC':
            holding = next((h for h in bsc_positions 
                          if h.get('token_address', '').lower() == row['token_address'].lower() 
                          and h.get('wallet_address', '').lower() == row['wallet_address'].lower()), None)
            if holding:
                avg_cost = float(holding.get('avg_cost', 0))
                if avg_cost > 0:
                    return (float(holding.get('avg_sold', 0)) - avg_cost) / avg_cost
            return 0
        else:
            holding = next((h for h in sol_positions 
                          if h.get('token_address', '').lower() == row['token_address'].lower()), None)
            if holding:
                avg_cost = float(holding.get('avg_cost', 0))
                if avg_cost > 0:
                    return (float(holding.get('avg_sold', 0)) - avg_cost) / avg_cost
            return 0
    
    trades_df['profit_rate'] = trades_df.apply(calculate_profit_rate, axis=1)
    
    # Calculate holding time from holdings data
    def calculate_holding_time(row):
        if row['chain'] == 'BSC':
            holding = next((h for h in bsc_positions 
                          if h.get('token_address', '').lower() == row['token_address'].lower() 
                          and h.get('wallet_address', '').lower() == row['wallet_address'].lower()), None)
            if holding:
                start_time = holding.get('start_holding_at')
                end_time = holding.get('end_holding_at')
                if start_time and end_time:
                    return (end_time - start_time) / 3600  # Convert to hours
            return 24  # Default to 24 hours
        else:
            holding = next((h for h in sol_positions 
                          if h.get('token_address', '').lower() == row['token_address'].lower()), None)
            if holding:
                start_time = holding.get('start_holding_at')
                end_time = holding.get('end_holding_at')
                if start_time and end_time:
                    return (end_time - start_time) / 3600  # Convert to hours
            return 24  # Default to 24 hours
    
    trades_df['holding_time_hours'] = trades_df.apply(calculate_holding_time, axis=1)
    
    # Ensure timestamp exists
    if 'date' in trades_df.columns:
        trades_df['timestamp'] = trades_df['date']
    else:
        trades_df['timestamp'] = pd.Timestamp.now()
    
    # Calculate wallet analysis
    wallet_analysis = trades_df.groupby('target_wallet').agg({
        'timestamp': ['count', 'min', 'max'],
        'value_sol': 'sum',
        'value_bnb': 'sum',
        'success_rate': 'mean',
        'win_rate': 'mean',
        'profit': 'sum',
        'profit_rate': 'mean',
        'holding_time_hours': 'mean'
    }).reset_index()

    # Flatten column names
    wallet_analysis.columns = ['target_wallet', 'total_trades', 'first_trade', 'last_trade', 
                             'total_volume_sol', 'total_volume_bnb', 'success_rate', 
                             'win_rate', 'total_profit', 'avg_profit_rate', 'avg_holding_time_hours']

    # Handle BSC wallets (identified by 0x prefix)
    bsc_mask = wallet_analysis['target_wallet'].str.startswith('0x')
    # print("\nDebug - BSC wallet statistics:")
    # print(f"Number of BSC wallets found: {sum(bsc_mask)}")

    # Calculate winning trades and win rate for BSC wallets
    bsc_trades = trades_df[trades_df['target_wallet'].str.startswith('0x')].copy()
    # print(f"Number of BSC trades found: {len(bsc_trades)}")

    # For BSC, a trade is considered successful if it has a non-zero profit
    bsc_trades['is_winning_trade'] = bsc_trades['profit'] > 0
    # print(f"\nNumber of winning BSC trades: {bsc_trades['is_winning_trade'].sum()}")

    # Calculate BSC wallet statistics
    bsc_wallet_stats = bsc_trades.groupby('target_wallet').agg({
        'is_winning_trade': 'sum',
        'value_bnb': 'sum',
        'timestamp': ['min', 'max', 'count'],
        'profit': 'sum',
        'profit_rate': 'mean',
        'holding_time_hours': 'mean'
    }).reset_index()

    # Flatten column names
    bsc_wallet_stats.columns = ['target_wallet', 'winning_trades', 'total_volume_bnb', 
                              'first_trade', 'last_trade', 'total_trades',
                              'total_profit', 'avg_profit_rate', 'avg_holding_time_hours']

    # Calculate success rate and win rate for BSC
    bsc_wallet_stats['win_rate'] = bsc_wallet_stats['winning_trades'] / bsc_wallet_stats['total_trades']
    bsc_wallet_stats['success_rate'] = bsc_wallet_stats['win_rate']  # Use win rate as success rate for BSC
    bsc_wallet_stats['total_volume_sol'] = 0  # No SOL volume for BSC wallets

    # Create a new DataFrame for all wallets
    new_wallet_analysis = pd.DataFrame()

    # Add Solana wallets (non-BSC)
    solana_wallets = wallet_analysis[~wallet_analysis['target_wallet'].str.startswith('0x')].copy()
    new_wallet_analysis = pd.concat([new_wallet_analysis, solana_wallets])

    # Add BSC wallets
    bsc_wallet_stats = bsc_wallet_stats[['target_wallet', 'total_trades', 'total_profit', 'avg_profit_rate', 
                                        'total_volume_sol', 'total_volume_bnb', 'win_rate',
                                        'first_trade', 'last_trade', 
                                        'avg_holding_time_hours', 'winning_trades']]
    new_wallet_analysis = pd.concat([new_wallet_analysis, bsc_wallet_stats])

    # Fill NaN values with 0
    new_wallet_analysis = new_wallet_analysis.fillna(0)

    # Reorder columns as requested
    column_order = ['target_wallet', 'total_trades', 'total_profit', 'avg_profit_rate', 
                   'total_volume_sol', 'total_volume_bnb', 'win_rate',
                   'first_trade', 'last_trade', 
                   'avg_holding_time_hours', 'winning_trades']
    new_wallet_analysis = new_wallet_analysis[column_order]

    # Sort by total profit
    new_wallet_analysis = new_wallet_analysis.sort_values('total_profit', ascending=False)

    return new_wallet_analysis 
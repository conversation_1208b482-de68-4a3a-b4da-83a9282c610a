from telethon import TelegramClient
import re
import csv
import os
from datetime import datetime, timedelta
import asyncio
from datetime import timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Telegram API credentials from environment variables
api_id = os.getenv('TELEGRAM_API_ID')
api_hash = os.getenv('TELEGRAM_API_HASH')
phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')
bloom_chat_name = os.getenv('BLOOM_CHAT_NAME', 'Bloom Solana')  # Default value if not set

# Validate required environment variables
if not all([api_id, api_hash, phone_number]):
    raise ValueError("Missing required environment variables. Please set TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE_NUMBER in .env file")

# Output CSV file
output_file = 'bloom_trades.csv'

# Create CSV if it doesn't exist
if not os.path.exists(output_file):
    with open(output_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['date', 'status', 'token_name', 'token_address', 'target_wallet', 'value_sol', 'tx_hash'])

# Regex patterns to extract data
patterns = {
    'pending_buy': re.compile(r'🟡 Copy Trade Buy Pending'),
    'success_buy': re.compile(r'🟢 Copy Trade Buy Success'),
    'token_name': re.compile(r'🌸 Token: (?:\*\*)?(.+?)(?:\*\*)?(?:\n|$)'),
    'token_address': re.compile(r'🌸\s*(?:`)?([0-9a-zA-Z]{32,})(?:`)?'),
    'target_wallet': re.compile(r'🕵️ Target \((?:[^:]+)\): (?:`)?([0-9a-zA-Z]{32,})(?:`)?'),
    'value_sol': re.compile(r'Value: (?:\*\*)?([0-9.]+) SOL(?:\*\*)?'),
    'tx_hash': re.compile(r'(?:`)?([0-9a-zA-Z]{60,})(?:`)?\n(?:🚀|View)')
}

async def extract_trade_info(message):
    """Extract trade information from a message"""
    text = message.text
    
    # Determine status
    if patterns['pending_buy'].search(text):
        status = 'Pending Buy'
    elif patterns['success_buy'].search(text):
        status = 'Success Buy'
    else:
        return None
    
    # Extract other information
    token_name_match = patterns['token_name'].search(text)
    token_name = token_name_match.group(1) if token_name_match else 'Unknown'
    
    token_address_match = patterns['token_address'].search(text)
    token_address = token_address_match.group(1) if token_address_match else 'Unknown'
    
    target_wallet_match = patterns['target_wallet'].search(text)
    target_wallet = target_wallet_match.group(1) if target_wallet_match else 'Unknown'
    
    value_sol_match = patterns['value_sol'].search(text)
    value_sol = value_sol_match.group(1) if value_sol_match else '0'
    
    tx_hash_match = patterns['tx_hash'].search(text)
    tx_hash = tx_hash_match.group(1) if tx_hash_match else 'Unknown'
    
    return {
        'date': message.date,
        'status': status,
        'token_name': token_name,
        'token_address': token_address,
        'target_wallet': target_wallet,
        'value_sol': value_sol,
        'tx_hash': tx_hash
    }

async def main():
    # Calculate the date 7 days ago with timezone awareness
    seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
    
    # Connect to Telegram
    client = TelegramClient('bloom_session', api_id, api_hash)
    await client.start(phone_number)
    
    print("Connected to Telegram. Fetching messages from the last 7 days...")
    
    # Debug flag to print first matching message
    debug_printed = False
    
    # Get messages from the last 7 days
    async for dialog in client.iter_dialogs():
        if bloom_chat_name.lower() in dialog.name.lower():
            # Get the most recent message ID
            latest_message = await client.get_messages(dialog, limit=1)
            if not latest_message:
                continue
                
            max_id = latest_message[0].id
            min_date = seven_days_ago
            
            messages = []
            while True:
                # Get messages in batches
                batch = await client.get_messages(
                    dialog,
                    limit=100,  # Get messages in batches of 100
                    max_id=max_id,
                    offset_date=min_date
                )
                
                if not batch:
                    break
                    
                messages.extend(batch)
                max_id = batch[-1].id - 1  # Update max_id to get older messages
                
                # If we've reached messages older than 7 days, stop
                if batch[-1].date < min_date:
                    break
            
            print(f"Found {len(messages)} messages in {dialog.name} from the last 7 days")
            
            # Extract information from matching messages
            trades = []
            for message in messages:
                if message.text and ('🟡 Copy Trade Buy Pending' in message.text or 
                                    '🟢 Copy Trade Buy Success' in message.text):
                    # Debug: Print first matching message
                    if not debug_printed:
                        print("\nFirst matching message:")
                        print(message.text)
                        print("\nDebug regex matches:")
                        token_address_match = patterns['token_address'].search(message.text)
                        tx_hash_match = patterns['tx_hash'].search(message.text)
                        print(f"Token Address Match: {token_address_match.group(1) if token_address_match else 'No match'}")
                        print(f"TX Hash Match: {tx_hash_match.group(1) if tx_hash_match else 'No match'}")
                        debug_printed = True
                    
                    trade_info = await extract_trade_info(message)
                    if trade_info:
                        trades.append(trade_info)
            
            # Save to CSV
            with open(output_file, 'a', newline='') as f:
                writer = csv.writer(f)
                for trade in trades:
                    writer.writerow([
                        trade['date'],
                        trade['status'],
                        trade['token_name'],
                        trade['token_address'],
                        trade['target_wallet'],
                        trade['value_sol'],
                        trade['tx_hash']
                    ])
            
            print(f"Extracted and saved {len(trades)} trade messages")
    
    # Disconnect
    await client.disconnect()
    print(f"Done! Trade data saved to {output_file}")

def test_extraction():
    """Test the message extraction with a sample message"""
    test_message = """**🟢 Copy Trade Buy Success**

🌸 Token: **Solari**
🌸 `FHrKNpuMEokApt8dQKLAo1v3mQsdq3L5ZuhrdzvUpump`

🕵️ Target (Ejjk8R9Ro9YKpw6RLw5b4SJNvT7fEaJ6Hwp5vjqwYoWJ): `Ejjk8R9Ro9YKpw6RLw5b4SJNvT7fEaJ6Hwp5vjqwYoWJ`

Transactions:

💰 Value: **1.489 SOL** - Prio Fee: **0.0001 SOL** - Processor Tip: **0.0012 SOL**
`5HALv9CUMmZBEu71mPpXcKKi2n5gYoRg9pMcjRh2znuTEbPVFxv4rMrBMx2ogd3zd4mxaner4JQXsobmi4fExPRR`
🚀 [**View on Solscan**](https://solscan.io/tx/5HALv9CUMmZBEu71mPpXcKKi2n5gYoRg9pMcjRh2znuTEbPVFxv4rMrBMx2ogd3zd4mxaner4JQXsobmi4fExPRR)"""

    # Create a mock message object
    class MockMessage:
        def __init__(self, text):
            self.text = text
            self.date = datetime.now(timezone.utc)

    mock_message = MockMessage(test_message)
    
    # Extract trade info
    trade_info = asyncio.run(extract_trade_info(mock_message))
    
    # Print results
    print("\nTest Results:")
    print(f"Status: {trade_info['status']}")
    print(f"Token Name: {trade_info['token_name']}")
    print(f"Token Address: {trade_info['token_address']}")
    print(f"Target Wallet: {trade_info['target_wallet']}")
    print(f"Value SOL: {trade_info['value_sol']}")
    print(f"TX Hash: {trade_info['tx_hash']}")

if __name__ == '__main__':
    # Run test first
    test_extraction()
    # Then run main
    asyncio.run(main())
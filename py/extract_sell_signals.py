from telethon import TelegramClient
import re
import asyncio
import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Telegram API credentials from environment variables
api_id = os.getenv('TELEGRAM_API_ID')
api_hash = os.getenv('TELEGRAM_API_HASH')
phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')
pump_hound_channel = "Pump Hound Signal"

# Validate required environment variables
if not all([api_id, api_hash, phone_number]):
    raise ValueError("Missing required environment variables. Please set TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE_NUMBER in .env file")

# Regex patterns to extract data
patterns = {
    'buy_signal': re.compile(r'🟢 Buy \*\*(.+?)\*\*(?:\n|$)'),
    'sell_signal': re.compile(r'🔴 Sell \*\*(.+?)\*\*(?:\n|$)'),
    'price': re.compile(r'💰 Price: \$([0-9.]+)'),
    'highest_gain': re.compile(r'🎉 Highest Gain: \*\*\+([0-9.]+)%\*\*'),
    'token_address': re.compile(r'📝 CA: `([0-9a-zA-Z]{42,})`')
}

async def extract_signal_info(message):
    """Extract signal information from a message"""
    text = message.text
    
    # Determine signal type and extract token symbol
    token_symbol = 'Unknown'
    if patterns['buy_signal'].search(text):
        signal_type = 'Buy'
        match = patterns['buy_signal'].search(text)
        if match:
            token_symbol = match.group(1).strip()
    elif patterns['sell_signal'].search(text):
        signal_type = 'Sell'
        match = patterns['sell_signal'].search(text)
        if match:
            token_symbol = match.group(1).strip()
    else:
        return None
    
    # Extract token address
    token_address_match = patterns['token_address'].search(text)
    token_address = token_address_match.group(1) if token_address_match else 'Unknown'
    
    # Extract price
    price_match = patterns['price'].search(text)
    price = float(price_match.group(1)) if price_match else 0
    
    # Extract highest gain (only for sell signals)
    highest_gain = 0
    if signal_type == 'Sell':
        highest_gain_match = patterns['highest_gain'].search(text)
        highest_gain = float(highest_gain_match.group(1)) if highest_gain_match else 0
    
    return {
        'date': message.date,
        'signal_type': signal_type,
        'token_symbol': token_symbol,
        'token_address': token_address,
        'price': price,
        'highest_gain': highest_gain
    }

async def extract_telegram_signals():
    """Extract signals from Pump Hound Signal channel for the last 7 days"""
    # Calculate date range (7 days ago to now in UTC+8)
    utc_plus_8 = timezone(timedelta(hours=8))
    now = datetime.now(utc_plus_8)
    seven_days_ago = now - timedelta(days=7)
    
    # Convert to UTC for comparison with Telegram API dates
    now_utc = now.astimezone(timezone.utc)
    seven_days_ago_utc = seven_days_ago.astimezone(timezone.utc)
    
    signals = []
    
    # Connect to Telegram
    client = TelegramClient('pump_hound_session', api_id, api_hash)
    await client.start(phone_number)
    
    print(f"Connected to Telegram. Fetching signals from {seven_days_ago} to {now} (UTC+8)...")
    
    # Find the Pump Hound Signal channel
    pump_hound_channel_entity = None
    async for dialog in client.iter_dialogs():
        if pump_hound_channel.lower() in dialog.name.lower():
            pump_hound_channel_entity = dialog
            break
    
    if not pump_hound_channel_entity:
        print(f"Could not find {pump_hound_channel} channel")
        await client.disconnect()
        return signals
    
    # Get messages from the channel
    messages = await client.get_messages(
        pump_hound_channel_entity,
        limit=1000,  # Adjust as needed
        offset_date=now_utc
    )
    
    # Filter messages by date range (using UTC for comparison)
    filtered_messages = [
        msg for msg in messages 
        if seven_days_ago_utc <= msg.date <= now_utc
    ]
    
    print(f"Found {len(filtered_messages)} messages in {pump_hound_channel} from the specified time range")
    
    # Extract signal information
    for message in filtered_messages:
        if message.text and patterns['sell_signal'].search(message.text):
            signal_info = await extract_signal_info(message)
            if signal_info:
                signals.append(signal_info)
    
    await client.disconnect()
    return signals

async def generate_sql_updates():
    """Generate SQL UPDATE statements for sell signals"""
    signals = await extract_telegram_signals()
    
    if not signals:
        print("No sell signals found in the last 7 days")
        return
    
    # Convert to DataFrame for easier analysis
    signals_df = pd.DataFrame(signals)
    
    # Print SQL UPDATE statements
    print("\nSQL UPDATE statements for sell signals:")
    print("-- Generated on", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    for _, signal in signals_df.iterrows():
        # Format the date for SQL
        emit_time = signal['date'].strftime("%Y-%m-%d %H:%M:%S")
        # Format the highest gain as a decimal
        highest_gain = f"{signal['highest_gain']/100:.2f}"
        
        print(f"UPDATE token_sell_signals SET highest_gain = {highest_gain} WHERE token_address = '{signal['token_address']}';")

async def main():
    await generate_sql_updates()

if __name__ == "__main__":
    asyncio.run(main()) 
import json
import time
import asyncio
import sys
import os
import traceback
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains

# Global driver instance to be reused
_driver = None

def log(message, debug=True):
    """Print debug messages to stderr if debug mode is enabled"""
    if debug:
        print(message, file=sys.stderr)

def get_driver():
    """Get or create the undetected-chromedriver instance"""
    global _driver
    if _driver is None:
        # Configure Chrome options
        options = uc.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        display = os.environ.get("DISPLAY", None)
        if display:
            options.add_argument(f"--display={display}")
        
        # Enable performance logging
        options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        # Create a new instance of the undetected Chrome driver
        _driver = uc.Chrome(headless=False, use_subprocess=True, options=options)
        
        # Set page load timeout
        _driver.set_page_load_timeout(60)
    
    return _driver

def close_driver():
    """Close the undetected-chromedriver instance"""
    global _driver
    if _driver is not None:
        _driver.quit()
        _driver = None

async def get_wallet_data_selenium(wallet_address: str, chain: str = "sol", days: int = 7, scroll: bool = True) -> Dict[str, Any]:
    """
    Get wallet data using undetected-chromedriver to bypass Cloudflare protection
    
    Args:
        wallet_address: The wallet address to analyze
        chain: The blockchain chain ("sol" for Solana, "bsc" for BSC)
        days: Number of days of data to retrieve
        scroll: Whether to scroll the page multiple times (needed for holdings, not for PNL)
        
    Returns:
        Dictionary containing wallet summary and holdings data
    """
    driver = get_driver()
    results = {
        "wallet_summary": None,
        "wallet_holdings": []
    }
    
    try:
        # Enable network monitoring BEFORE navigating to the page
        log("Setting up network interception...")
        driver.execute_cdp_cmd('Network.enable', {})
        
        # Clear existing logs
        driver.get_log('performance')
        
        # Navigate to the URL
        wallet_url = f"https://gmgn.ai/{chain}/address/{wallet_address}"
        log(f"Navigating to {wallet_url}...")
        driver.get(wallet_url)
        
        # Wait for the page to load
        log("Waiting for page to load...")
        time.sleep(2)  # Increased wait time
        
        # Find and click the close icon if it exists
        log("Looking for close icon...")
        try:
            close_icons = driver.find_elements(By.CLASS_NAME, "css-pt4g3d")
            if len(close_icons) >= 1:
                close_icon = close_icons[0]
                log(f"Found close icon")
                
                # Use JavaScript to click the element
                driver.execute_script("arguments[0].click();", close_icon)
                log("Clicked close icon using JavaScript")
            else:
                log(f"Not enough close icons found. Found {len(close_icons)} elements with class css-pt4g3d")
        except Exception as e:
            log(f"Error clicking close icon: {e}")

        try:
            # Instead of clicking Next buttons multiple times then Finish button,
            # directly click the modal background to dismiss it
            log("Looking for modal background to dismiss...")
            driver.execute_script("document.querySelector('.pi-modal-wrap').click();")
            log("Clicked modal background using JavaScript")
            
            # Wait a moment for any animations or page changes
            time.sleep(2)
        except Exception as e:
            log(f"Error clicking modal background: {e}")
        
        # Find and click the "Recent PnL" element
        log("Looking for 'Recent PnL' element...")
        try:
            # Try to find the element by text
            recent_pnl_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Recent PnL')]")
            if len(recent_pnl_elements) >= 1:
                recent_pnl = recent_pnl_elements[0]
                log(f"Found 'Recent PnL' element")
                
                # Use JavaScript to click the element
                driver.execute_script("arguments[0].click();", recent_pnl)
                log("Clicked 'Recent PnL' element using JavaScript")
            else:
                log(f"'Recent PnL' element not found")
        except Exception as e:
            log(f"Error clicking 'Recent PnL' element: {e}")
        
        # Try multiple scrolling methods to ensure the page scrolls properly
        if scroll:
            log("Scrolling to bottom of page...")
            # Find all scrollable containers and scroll each one to the bottom
            js_find_and_scroll = """
            let containers = Array.from(document.querySelectorAll('*')).filter(el => {
              let style = window.getComputedStyle(el);
              return (style.overflowY === 'scroll' || style.overflowY === 'auto') &&
                     el.scrollHeight > el.clientHeight;
            });
            
            let scrolledContainers = 0;
            containers.forEach(container => {
              container.scrollTop = container.scrollHeight;
              scrolledContainers++;
            });
            
            return {
              totalContainers: containers.length,
              scrolledContainers: scrolledContainers
            };
            """
            
            # Execute the script multiple times to catch dynamically loaded content
            for i in range(10):
                # Execute the script and get the result
                scroll_result = driver.execute_script(js_find_and_scroll)
                log(f"Scroll attempt {i+1}/10: Found {scroll_result['totalContainers']} scrollable containers and scrolled {scroll_result['scrolledContainers']}")
                
                # Wait a moment to let any lazy-loaded content appear
                time.sleep(2)
        else:
            log("Skipping scrolling as requested")
        
        # Get all network requests
        logs = driver.get_log('performance')
        cutoff_timestamp = int((datetime.now() - timedelta(days=days)).timestamp())
        
        # Process all network requests in a single loop
        for entry in logs:
            if 'message' in entry:
                message = json.loads(entry['message'])
                if 'message' in message and 'method' in message['message']:
                    if message['message']['method'] == 'Network.responseReceived':
                        params = message['message']['params']
                        request_id = params['requestId']
                        url = params['response']['url']
                        
                        # Look for the wallet summary API
                        if f'/api/v1/wallet_stat/{chain}/' in url:
                            log(f"Found wallet summary request: {url}")
                            try:
                                # Get response body
                                response = driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': request_id})
                                if 'body' in response:
                                    results["wallet_summary"] = json.loads(response['body'])['data']
                                    log("Successfully captured wallet summary data")
                            except Exception as e:
                                log(f"Error getting wallet summary response body: {e}")
                        
                        # Look for the wallet holdings API
                        elif f'/api/v1/wallet_holdings/{chain}/' in url:
                            log(f"Found wallet holdings request: {url}")
                            try:
                                # Get response body
                                response = driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': request_id})
                                if 'body' in response:
                                    holdings_data = json.loads(response['body'])
                                    holdings = holdings_data['data']['holdings']
                                    
                                    # Filter holdings by timestamp
                                    filtered_holdings = []
                                    for holding in holdings:
                                        last_active = holding.get('last_active_timestamp')
                                        if not last_active or last_active >= cutoff_timestamp:
                                            filtered_holdings.append(holding)
                                    
                                    results["wallet_holdings"].extend(filtered_holdings)
                                    log(f"Successfully captured {len(filtered_holdings)} wallet holdings")
                            except Exception as e:
                                log(f"Error getting wallet holdings response body: {e}")
        
    except Exception as e:
        log(f"Error processing wallet {wallet_address}: {e}")
        log(traceback.format_exc())
    
    return results

async def get_wallet_holdings_selenium(wallet_address: str, chain: str = "sol", days: int = 7) -> List[Dict]:
    """
    Get wallet holdings using undetected-chromedriver
    
    Args:
        wallet_address: The wallet address to analyze
        chain: The blockchain chain ("sol" for Solana, "bsc" for BSC)
        days: Number of days of data to retrieve
        
    Returns:
        List of wallet holdings
    """
    data = await get_wallet_data_selenium(wallet_address, chain, days, scroll=True)
    return data.get("wallet_holdings", [])

async def get_wallet_pnl_selenium(wallet_address: str, chain: str = "sol", days: int = 7) -> Optional[Dict]:
    """
    Get wallet PNL data using undetected-chromedriver
    
    Args:
        wallet_address: The wallet address to analyze
        chain: The blockchain chain ("sol" for Solana, "bsc" for BSC)
        days: Number of days of data to retrieve
        
    Returns:
        Dictionary containing wallet PNL data
    """
    data = await get_wallet_data_selenium(wallet_address, chain, days, scroll=False)
    return data.get("wallet_summary")

# Add a simple test function if called directly
if __name__ == "__main__":
    if len(sys.argv) > 1:
        wallet = sys.argv[1]
        chain = sys.argv[2] if len(sys.argv) > 2 else "sol"
        
        async def test_functions():
            try:
                print(f"Testing with wallet: {wallet} on chain: {chain}")
                
                # Test get_wallet_holdings_selenium
                print("Getting wallet holdings...")
                holdings = await get_wallet_holdings_selenium(wallet, chain, days=7)
                print(f"Found {len(holdings)} holdings")
                
                # Test get_wallet_pnl_selenium
                print("Getting wallet PNL...")
                pnl = await get_wallet_pnl_selenium(wallet, chain, days=7)
                if pnl:
                    print(f"PNL data: {pnl}")
                else:
                    print("Failed to get PNL data")
                
            except Exception as e:
                print(f"Error during test: {e}")
            finally:
                # Close the driver when done
                close_driver()
        
        # Run the test
        asyncio.run(test_functions()) 
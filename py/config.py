import os
from dotenv import load_dotenv
import re

# Load environment variables
load_dotenv()

# Telegram API credentials from environment variables
api_id = os.getenv('TELEGRAM_API_ID')
api_hash = os.getenv('TELEGRAM_API_HASH')
phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')
bloom_chat_name = os.getenv('BLOOM_CHAT_NAME', 'Bloom Solana')
bsc_chat_name = os.getenv('BSC_CHAT_NAME', 'DBot - Bsc Trading Bot')

# Solana wallet addresses 
sol_wallets = [
    '4zVmga8K7NxXomV7Bhv6wGdkjr8cQezMzrA4J7JBJ3XA',
    '********************************************'
]

# BSC wallet addresses
bsc_wallets = [
    # '******************************************',
    # '******************************************',
    # '******************************************',
    # '******************************************',
    # '******************************************'
]

# Validate required environment variables
if not all([api_id, api_hash, phone_number]):
    raise ValueError("Missing required environment variables. Please set TELEGRAM_API_ID, TELEGRAM_API_HASH, and TELEGRAM_PHONE_NUMBER in .env file")

# Regex patterns to extract data
sol_patterns = {
    'pending_buy': re.compile(r'🟡 Copy Trade Buy Pending'),
    'success_buy': re.compile(r'🟢 Copy Trade Buy Success'),
    'token_name': re.compile(r'🌸 Token: (?:\*\*)?(.+?)(?:\*\*)?(?:\n|$)'),
    'token_address': re.compile(r'🌸\s*(?:`)?([0-9a-zA-Z]{32,})(?:`)?'),
    'target_wallet': re.compile(r'🕵️ Target \((?:[^:]+)\): (?:`)?([0-9a-zA-Z]{32,})(?:`)?'),
    'value_sol': re.compile(r'Value: (?:\*\*)?([0-9.]+) SOL(?:\*\*)?'),
    'tx_hash': re.compile(r'(?:`)?([0-9a-zA-Z]{60,})(?:`)?\n(?:🚀|View)')
}

bsc_patterns = {
    'buy_success': re.compile(r'✅ Copy buy'),
    'token_name': re.compile(r'Copy buy [\d.]+[KMB]? (.+?) for'),
    'token_amount': re.compile(r'Copy buy ([\d.]+[KMB]?) .+? for'),
    'value_bnb': re.compile(r'for ([\d.]+) BNB'),
    'token_address': re.compile(r'\[Token\] (?:`)?(0x[a-fA-F0-9]{40})(?:`)?'),
    'target_wallet': re.compile(r'Copy Wallet\n(?:`)?(0x[a-fA-F0-9]{40})(?:`)?'),
    'price': re.compile(r'\[Price\] \$([\d.]+)'),
    'market_cap': re.compile(r'\[Market Cap\] \$([\d.]+[KMB]?)'),
    'trading_fee': re.compile(r'\[Trading Fee\] ([\d.]+) BNB'),
    'my_wallet': re.compile(r'\[Wallet \d+\] (?:`)?(0x[a-fA-F0-9]{40})(?:`)?')
} 
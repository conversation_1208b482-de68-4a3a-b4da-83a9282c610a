# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Install protoc and required tools
RUN apk add --no-cache protobuf-dev make git

# Copy go mod files
COPY go.mod ./
RUN go mod download

# Copy source code
COPY . .

# Generate protobuf code
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
RUN go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
RUN protoc --go_out=. --go_opt=paths=source_relative proto/events.proto

# Build the applications
RUN CGO_ENABLED=0 GOOS=linux go build -o data-writer
RUN CGO_ENABLED=0 GOOS=linux go build -o create-partition ./cmd/create-partition

# Final stage
FROM alpine:latest

WORKDIR /app

# Copy the binaries from builder
COPY --from=builder /app/data-writer .
COPY --from=builder /app/create-partition .

# Expose health check port
EXPOSE 8080

# Run the application
CMD ["./data-writer"]

package analyzer

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/samber/lo"
)

// AssociatedWallet represents a wallet that has transfer activity with the target wallet
type AssociatedWallet struct {
	Address         string `json:"address"`
	InTransfers     int    `json:"in_transfers"`
	OutTransfers    int    `json:"out_transfers"`
	IsBidirectional bool   `json:"is_bidirectional"`
}

// WalletAnalyzerConfig holds the configuration for the wallet analyzer
type WalletAnalyzerConfig struct {
	MinTransfers       int
	IncludeOneWay      bool
	MinOneWayTransfers int
}

// WalletAnalyzer is the main analyzer struct
type WalletAnalyzer struct {
	config WalletAnalyzerConfig
	repo   *db.Repo
}

// NewWalletAnalyzer creates a new wallet analyzer instance
func NewWalletAnalyzer(config WalletAnalyzerConfig) *WalletAnalyzer {
	return &WalletAnalyzer{
		config: config,
		repo:   db.Get(),
	}
}

// AnalyzeWallet analyzes a wallet's transfer history to find associated wallets
func (wa *WalletAnalyzer) AnalyzeWallet(ctx context.Context, wallet string, tokenMint string) ([]AssociatedWallet, error) {
	// Get transfer statistics from database
	stats, err := wa.repo.GetWalletTransfers(ctx, wallet, tokenMint)
	if err != nil {
		return nil, err
	}

	wallets := make([]string, len(stats))
	for i, stat := range stats {
		wallets[i] = stat.AssociatedWallet
	}

	shouldExcludes, err := wa.repo.CheckWalletsForExclusion(ctx, wallets)
	if err != nil {
		return nil, err
	}

	// Convert and filter results based on config
	var result []AssociatedWallet
	for _, stat := range stats {
		if slices.Contains(KNOWN_EXCLUSIONS, stat.AssociatedWallet) {
			continue
		}
		if !solana.IsOnCurve(solana.MustPublicKeyFromBase58(stat.AssociatedWallet).Bytes()) {
			continue
		}
		if shouldExcludes[stat.AssociatedWallet] {
			fmt.Println("excluded wallet:", stat.AssociatedWallet)
			continue
		}

		totalTransfers := stat.InTransfers + stat.OutTransfers

		// Skip if total transfers is less than minimum
		if totalTransfers < wa.config.MinTransfers {
			continue
		}

		// Skip one-way transfers if not included
		if !wa.config.IncludeOneWay && !stat.IsBidirectional {
			continue
		}

		// Skip one-way transfers with low count
		if !stat.IsBidirectional && totalTransfers < wa.config.MinOneWayTransfers {
			continue
		}

		result = append(result, AssociatedWallet{
			Address:         stat.AssociatedWallet,
			InTransfers:     stat.InTransfers,
			OutTransfers:    stat.OutTransfers,
			IsBidirectional: stat.IsBidirectional,
		})
	}

	return result, nil
}

// AnalyzeWallets analyzes multiple wallets and returns a map of wallet to its associated wallets
func (wa *WalletAnalyzer) AnalyzeWallets(ctx context.Context, wallets []string, tokenMint string) (map[string][]string, error) {
	startTime := time.Now()

	// First, check which input wallets should be excluded
	shouldExcludes, err := wa.repo.CheckWalletsForExclusion(ctx, wallets)
	if err != nil {
		return nil, fmt.Errorf("failed to check wallet exclusions: %v", err)
	}
	fmt.Printf("Initial exclusion check took: %v\n", time.Since(startTime))
	fmt.Println("shouldExcludes length:", len(shouldExcludes))

	// Filter out excluded wallets from input
	filterStart := time.Now()
	var validWallets []string
	for _, wallet := range wallets {
		if !shouldExcludes[wallet] {
			validWallets = append(validWallets, wallet)
		}
	}
	fmt.Printf("Filtering valid wallets took: %v\n", time.Since(filterStart))

	// Get transfer statistics for all valid wallets in batches
	transferStart := time.Now()
	walletStats, err := wa.repo.GetWalletsTransfers(ctx, validWallets, tokenMint)
	if err != nil {
		return nil, fmt.Errorf("failed to get transfers for wallets: %v", err)
	}
	fmt.Printf("Getting wallet transfers took: %v\n", time.Since(transferStart))

	// Collect all associated wallets from all valid input wallets
	processStart := time.Now()
	allAssociatedWallets := make(map[string]struct{})
	walletToAssociated := make(map[string][]string)

	// Process results for each wallet
	for wallet, stats := range walletStats {
		// Filter and collect associated wallets
		var associatedWallets []string
		for _, stat := range stats {
			if slices.Contains(KNOWN_EXCLUSIONS, stat.AssociatedWallet) {
				continue
			}
			if !solana.IsOnCurve(solana.MustPublicKeyFromBase58(stat.AssociatedWallet).Bytes()) {
				continue
			}

			totalTransfers := stat.InTransfers + stat.OutTransfers
			if totalTransfers < wa.config.MinTransfers {
				continue
			}
			if !wa.config.IncludeOneWay && !stat.IsBidirectional {
				continue
			}
			if !stat.IsBidirectional && totalTransfers < wa.config.MinOneWayTransfers {
				continue
			}

			associatedWallets = append(associatedWallets, stat.AssociatedWallet)
			allAssociatedWallets[stat.AssociatedWallet] = struct{}{}
		}

		walletToAssociated[wallet] = associatedWallets
	}
	fmt.Printf("Processing wallet stats took: %v\n", time.Since(processStart))

	// Check exclusions for all discovered associated wallets
	exclusionStart := time.Now()
	associatedWalletList := make([]string, 0, len(allAssociatedWallets))
	for wallet := range allAssociatedWallets {
		associatedWalletList = append(associatedWalletList, wallet)
	}

	fmt.Println("associatedWalletList length:", len(associatedWalletList))
	shouldExcludes, err = wa.repo.CheckWalletsForExclusion(ctx, associatedWalletList)
	if err != nil {
		return nil, fmt.Errorf("failed to check associated wallet exclusions: %v", err)
	}
	fmt.Printf("Checking associated wallet exclusions took: %v\n", time.Since(exclusionStart))
	fmt.Println("shouldExcludes length:", len(shouldExcludes))

	// Remove excluded wallets from results
	finalFilterStart := time.Now()
	for wallet, associated := range walletToAssociated {
		var filteredAssociated []string
		for _, w := range associated {
			if !shouldExcludes[w] {
				filteredAssociated = append(filteredAssociated, w)
			}
		}
		walletToAssociated[wallet] = filteredAssociated
	}
	fmt.Printf("Final filtering took: %v\n", time.Since(finalFilterStart))
	fmt.Printf("Total execution time: %v\n", time.Since(startTime))

	return walletToAssociated, nil
}

// AnalyzeWalletsCluster finds clusters of related wallets using BFS
func (wa *WalletAnalyzer) AnalyzeWalletsCluster(ctx context.Context, wallets []string, tokenMint string) ([][]string, error) {
	// Get the wallet relationship graph
	walletToAssociated, err := wa.AnalyzeWallets(ctx, wallets, tokenMint)
	if err != nil {
		return nil, err
	}

	// Build adjacency list for BFS
	adjacencyList := make(map[string]map[string]struct{})
	for wallet, associated := range walletToAssociated {
		if _, exists := adjacencyList[wallet]; !exists {
			adjacencyList[wallet] = make(map[string]struct{})
		}
		for _, w := range associated {
			adjacencyList[wallet][w] = struct{}{}
			if _, exists := adjacencyList[w]; !exists {
				adjacencyList[w] = make(map[string]struct{})
			}
			adjacencyList[w][wallet] = struct{}{}
		}
	}

	// BFS to find clusters
	visited := make(map[string]bool)
	var clusters [][]string

	for wallet := range adjacencyList {
		if visited[wallet] {
			continue
		}

		// Start a new cluster
		var cluster []string
		queue := []string{wallet}
		visited[wallet] = true

		// BFS
		for len(queue) > 0 {
			current := queue[0]
			queue = queue[1:]
			cluster = append(cluster, current)

			// Add unvisited neighbors to queue
			for neighbor := range adjacencyList[current] {
				if !visited[neighbor] {
					visited[neighbor] = true
					queue = append(queue, neighbor)
				}
			}
		}

		clusters = append(clusters, cluster)
		// fmt.Println("cluster:", cluster)
	}

	inputWalletsSet := make(map[string]struct{})
	for _, wallet := range wallets {
		inputWalletsSet[wallet] = struct{}{}
	}
	return lo.FilterMap(clusters, func(cluster []string, _ int) ([]string, bool) {
		if len(cluster) > 1 {
			return cluster, true
		}
		// Filter cluster to only include input wallets
		// filteredCluster := lo.Filter(cluster, func(w string, _ int) bool {
		// 	_, exists := inputWalletsSet[w]
		// 	return exists
		// })
		// if len(filteredCluster) > 1 {
		// 	return filteredCluster, true
		// }
		return nil, false
	}), nil
}

package db

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"sync"
	"time"
)

var (
	maxBatchSize = 1000
	maxRetries   = 3
	instance     *Repo
	once         sync.Once
	initErr      error
)

// config holds the database connection configuration
type config struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	SSLMode  string
}

// defaultConfig creates a new DBConfig with default values
func defaultConfig() *config {
	return &config{
		Host:     "localhost",
		Port:     5432,
		User:     "solana_data",
		Password: "",
		Database: "solana_data",
		SSLMode:  "disable",
	}
}

// ConnectionString returns the PostgreSQL connection string
func (c *config) ConnectionString() string {
	return fmt.Sprintf(
		"postgres://%s:%s@%s:%d/%s?sslmode=%s",
		c.User,
		c.Password,
		c.Host,
		c.Port,
		c.Database,
		c.SSLMode,
	)
}

func initOnce() {
	log.Println("Starting database initialization...")
	// Create default configuration
	config := defaultConfig()

	// Override with environment variables
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Host = host
		log.Printf("Using DB_HOST: %s", host)
	}
	if port := os.Getenv("DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
			log.Printf("Using DB_PORT: %d", p)
		}
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.User = user
		log.Printf("Using DB_USER: %s", user)
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Password = password
		log.Printf("Using DB_PASSWORD: [REDACTED]")
	}
	if database := os.Getenv("DB_NAME"); database != "" {
		config.Database = database
		log.Printf("Using DB_NAME: %s", database)
	}
	if sslMode := os.Getenv("DB_SSL_MODE"); sslMode != "" {
		config.SSLMode = sslMode
		log.Printf("Using DB_SSL_MODE: %s", sslMode)
	}

	log.Printf("Connecting to database at %s:%d/%s with SSL mode: %s", config.Host, config.Port, config.Database, config.SSLMode)

	// Create new database instance with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	db, err := newRepo(ctx, config)
	if err != nil {
		initErr = fmt.Errorf("failed to initialize database: %w", err)
		log.Printf("Database initialization failed: %v", initErr)
		return
	}

	// Test the connection
	if err := db.pool.Ping(ctx); err != nil {
		initErr = fmt.Errorf("failed to ping database: %w", err)
		log.Printf("Database ping failed: %v", initErr)
		return
	}

	log.Println("Database connection established successfully")
	instance = db
}

// Initialize sets up the database connection using environment variables
func Initialize() error {
	log.Println("Calling db.Initialize()...")
	once.Do(initOnce)
	if initErr != nil {
		log.Printf("Database initialization failed: %v", initErr)
	}
	return initErr
}

// Get returns the singleton database instance
func Get() *Repo {
	if instance == nil {
		log.Panic("database not initialized. Call db.Initialize() first")
	}
	return instance
}

// Close closes the database connection
func Close() error {
	if instance == nil {
		return nil
	}
	instance.Close()
	instance = nil
	return nil
}

package db

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type Repo struct {
	pool *pgxpool.Pool
}

// PartitionInfo represents a partition's slot range
type PartitionInfo struct {
	TableName     string
	PartitionName string
	StartSlot     int64
	EndSlot       int64
}

func newRepo(ctx context.Context, config *config) (*Repo, error) {
	poolConfig, err := pgxpool.ParseConfig(config.ConnectionString())
	if err != nil {
		return nil, fmt.Errorf("failed to parse connection string: %v", err)
	}

	// Configure connection pool
	poolConfig.MaxConns = 20
	poolConfig.MinConns = 2
	poolConfig.MaxConnLifetime = time.Hour
	poolConfig.MaxConnIdleTime = 30 * time.Minute

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %v", err)
	}

	return &Repo{pool: pool}, nil
}

func (r *Repo) Close() {
	r.pool.Close()
}

func (r *Repo) WithTransaction(ctx context.Context, fn func(ctx context.Context, tx pgx.Tx) error) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	defer func() {
		if err != nil {
			tx.Rollback(ctx)
		}
	}()

	if err := fn(ctx, tx); err != nil {
		return err
	}

	return tx.Commit(ctx)
}

// GetCurrentPartitions returns all existing partitions for a given table
func (r *Repo) GetCurrentPartitions(ctx context.Context, tableName string) ([]PartitionInfo, error) {
	query := `
		SELECT 
			c.relname as table_name,
			p.relname as partition_name,
			pg_get_expr(p.relpartbound, p.oid) as bounds
		FROM pg_class c
		JOIN pg_inherits i ON i.inhparent = c.oid
		JOIN pg_class p ON p.oid = i.inhrelid
		WHERE c.relname = $1
		ORDER BY p.relname ASC`

	rows, err := r.pool.Query(ctx, query, tableName)
	if err != nil {
		return nil, fmt.Errorf("failed to query partitions: %v", err)
	}
	defer rows.Close()

	var partitions []PartitionInfo
	for rows.Next() {
		var p PartitionInfo
		var bounds string
		if err := rows.Scan(&p.TableName, &p.PartitionName, &bounds); err != nil {
			return nil, fmt.Errorf("failed to scan partition info: %v", err)
		}

		// Parse the bounds string to extract start and end slots
		var startSlot, endSlot int64
		_, err := fmt.Sscanf(bounds, "FOR VALUES FROM ('%d') TO ('%d')", &startSlot, &endSlot)
		if err != nil {
			log.Printf("failed to parse partition bounds: %s", bounds)
			return nil, fmt.Errorf("failed to parse partition bounds: %v", err)
		}

		p.StartSlot = startSlot
		p.EndSlot = endSlot
		partitions = append(partitions, p)
	}

	return partitions, nil
}

// CreateNextPartition creates the next partition for a given table based on the current slot
func (r *Repo) CreateNextPartition(ctx context.Context, tableName string, currentSlot uint64) error {
	// Calculate partition number (floor(slot/864000) + 1)
	partitionNum := (currentSlot / 864000) + 1
	startSlot := partitionNum * 864000
	endSlot := startSlot + 864000

	// Check if partition already exists
	partitions, err := r.GetCurrentPartitions(ctx, tableName)
	if err != nil {
		return fmt.Errorf("failed to get current partitions: %v", err)
	}

	// Check if the partition we want to create already exists
	for _, p := range partitions {
		if uint64(p.StartSlot) == startSlot && uint64(p.EndSlot) == endSlot {
			return nil // Partition already exists
		}
	}

	// Create the new partition
	partitionName := fmt.Sprintf("%s_p%d", tableName, partitionNum)
	query := fmt.Sprintf(`
		CREATE TABLE %s PARTITION OF %s FOR
		VALUES FROM (%d) TO (%d)`,
		partitionName, tableName, startSlot, endSlot)

	_, err = r.pool.Exec(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to create partition: %v", err)
	}

	return nil
}

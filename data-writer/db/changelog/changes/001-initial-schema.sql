--liquibase formatted sql
--changeset author:harry:001-1
CREATE TABLE sol_transfers (
    signature BYTEA NOT NULL,
    ix_index SMALLINT NOT NULL,
    slot BIGINT NOT NULL,
    from_wallet BYTEA NOT NULL,
    to_wallet BYTEA NOT NULL,
    amount NUMERIC(20, 0) NOT NULL,
    PRIMARY KEY (signature, ix_index, slot)
) PARTITION BY RANGE (slot);

--changeset author:harry:001-2
CREATE TABLE token_transfers (
    signature BYTEA NOT NULL,
    ix_index SMALLINT NOT NULL,
    slot BIGINT NOT NULL,
    token_mint BYTEA NOT NULL,
    from_wallet BYTEA NOT NULL,
    to_wallet BYTEA NOT NULL,
    amount NUMERIC(20, 0) NOT NULL,
    PRIMARY KEY (signature, ix_index, slot)
) PARTITION BY RANGE (slot);

--changeset author:harry:001-3
-- Create initial partitions for both tables. Partition n includes epoch 2n, 2n+1 (~4 days)
CREATE TABLE sol_transfers_p400 PARTITION OF sol_transfers FOR
VALUES
FROM
    (345600000) TO (345600000 + 864000);

CREATE TABLE sol_transfers_p401 PARTITION OF sol_transfers FOR
VALUES
FROM
    (345600000 + 864000) TO (345600000 + 864000 * 2);

CREATE TABLE token_transfers_p400 PARTITION OF token_transfers FOR
VALUES
FROM
    (345600000) TO (345600000 + 864000);

CREATE TABLE token_transfers_p401 PARTITION OF token_transfers FOR
VALUES
FROM
    (345600000 + 864000) TO (345600000 + 864000 * 2);

--changeset author:harry:001-4
CREATE INDEX idx_sol_transfers_slot ON sol_transfers (slot);

CREATE INDEX idx_sol_transfers_from_wallet_slot ON sol_transfers (from_wallet, slot DESC);

CREATE INDEX idx_sol_transfers_to_wallet_slot ON sol_transfers (to_wallet, slot DESC);

CREATE INDEX idx_token_transfers_slot ON token_transfers (slot);

CREATE INDEX idx_token_transfers_from_wallet_mint_slot ON token_transfers (from_wallet, token_mint, slot DESC);

CREATE INDEX idx_token_transfers_to_wallet_mint_slot ON token_transfers (to_wallet, token_mint, slot DESC);
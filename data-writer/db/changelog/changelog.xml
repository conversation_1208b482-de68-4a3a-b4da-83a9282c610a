<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <include file="changes/001-initial-schema.sql" relativeToChangelogFile="true"/>
    <changeSet id="add-base58-function-xml" author="harry">
        <sql splitStatements="false">
            <![CDATA[
CREATE OR REPLACE FUNCTION from_base58(p_text text)
RETURNS bytea AS $FUNCTIONBODY$
DECLARE
    alphabet text := '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
    base58_val int;
    carry bigint;
    i int;
    j int;
    zeros int := 0;
    -- Solana signatures are 64 bytes, which is a sufficient buffer size.
    buffer_size int := 64;
    -- PL/pgSQL arrays are 1-based by default.
    buffer int[] := array_fill(0, ARRAY[buffer_size]);
    result_bytea bytea;
    text_len int := length(p_text);
BEGIN
    -- 1. Count leading '1's which represent zero bytes in the output.
    i := 1;
    WHILE i <= text_len AND substr(p_text, i, 1) = '1' LOOP
        zeros := zeros + 1;
        i := i + 1;
    END LOOP;

    -- 2. Process the rest of the string, performing base conversion.
    FOR i IN (zeros + 1)..text_len LOOP
        -- Find the decimal value of the Base58 character.
        base58_val := strpos(alphabet, substr(p_text, i, 1)) - 1;

        IF base58_val < 0 THEN
            RAISE EXCEPTION 'from_base58: invalid character found: %', substr(p_text, i, 1);
        END IF;

        carry := base58_val;
        -- This loop performs the core arithmetic: buffer = (buffer * 58) + carry.
        -- It iterates from the least significant byte (right) to the most significant (left).
        FOR j IN REVERSE (buffer_size - 1)..0 LOOP
            -- PostgreSQL arrays are 1-based, so buffer indexes are j + 1.
            carry := carry + (buffer[j + 1] * 58);
            buffer[j + 1] := carry % 256;
            carry := carry / 256;
        END LOOP;
        
        -- For a valid signature, the number should not overflow a 64-byte buffer.
        IF carry > 0 THEN
             RAISE EXCEPTION 'from_base58: input string decodes to a number larger than 64 bytes';
        END IF;
    END LOOP;

    -- 3. Assemble the final bytea result from the buffer using a safer method.
    
    -- Find the first significant (non-zero) byte in our buffer to trim leading zero padding from the number.
    j := 1;
    WHILE j <= buffer_size AND buffer[j] = 0 LOOP
        j := j + 1;
    END LOOP;

    -- Start with the explicit leading zero bytes from the input '1' characters.
    result_bytea := repeat(E'\\x00', zeros)::bytea;

    -- Append the significant bytes from our buffer by building a hex string and decoding once.
    IF j <= buffer_size THEN
        DECLARE
            hex_part text := '';
        BEGIN
            FOR i IN j..buffer_size LOOP
                -- Use lpad to ensure each byte is represented by two hex digits (e.g., 'a' becomes '0a').
                hex_part := hex_part || lpad(to_hex(buffer[i]), 2, '0');
            END LOOP;
            result_bytea := result_bytea || decode(hex_part, 'hex');
        END;
    END IF;
    
    RETURN result_bytea;
END;
$FUNCTIONBODY$ LANGUAGE plpgsql IMMUTABLE;
            ]]>
        </sql>
    </changeSet>

    <include file="changes/002-add-p399-partition.sql" relativeToChangelogFile="true"/>
</databaseChangeLog>
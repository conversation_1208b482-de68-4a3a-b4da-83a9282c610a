package db

import (
	"context"
	"fmt"
	"testing"

	"github.com/kryptogo/kg-solana-data/data-writer/models"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestProcessSolTransfers(t *testing.T) {
	// Setup test database
	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		// Drop test database after test
		cleanupDB(t, config)
	}()

	// Test data
	events := []*pb.SolTransferEvent{
		{
			Metadata: &pb.EventMetadata{
				Signature: []byte("test_sig_1"),
				IxIndex:   0,
				Slot:      345600001, // Use a slot within the first partition
			},
			FromWallet:     "wallet1",
			ToWallet:       "wallet2",
			AmountLamports: 1000000,
		},
		{
			Metadata: &pb.EventMetadata{
				Signature: []byte("test_sig_2"),
				IxIndex:   1,
				Slot:      345600002, // Use a slot within the first partition
			},
			FromWallet:     "wallet3",
			ToWallet:       "wallet4",
			AmountLamports: 2000000,
		},
	}

	// Test inserting transfers
	err = repo.ProcessSolTransfers(ctx, events)
	require.NoError(t, err)

	// Verify the transfers were inserted correctly
	for _, event := range events {
		var transfer models.SolTransfer
		err := repo.pool.QueryRow(ctx, `
			SELECT signature, ix_index, slot, from_wallet, to_wallet, amount 
			FROM sol_transfers 
			WHERE signature = $1 AND ix_index = $2 AND slot = $3`,
			event.Metadata.Signature, event.Metadata.IxIndex, event.Metadata.Slot,
		).Scan(
			&transfer.Signature,
			&transfer.IxIndex,
			&transfer.Slot,
			&transfer.FromWallet,
			&transfer.ToWallet,
			&transfer.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Metadata.Signature, transfer.Signature)
		assert.Equal(t, uint16(event.Metadata.IxIndex), transfer.IxIndex)
		assert.Equal(t, event.Metadata.Slot, transfer.Slot)
		assert.Equal(t, []byte(event.FromWallet), transfer.FromWallet)
		assert.Equal(t, []byte(event.ToWallet), transfer.ToWallet)
		assert.Equal(t, event.AmountLamports, transfer.Amount)
	}

	// Test duplicate handling
	err = repo.ProcessSolTransfers(ctx, events)
	require.NoError(t, err) // Should not error on duplicates

	// Verify no duplicates were inserted
	var count int
	err = repo.pool.QueryRow(ctx, `
		SELECT COUNT(*) 
		FROM sol_transfers 
		WHERE signature = $1 AND ix_index = $2 AND slot = $3`,
		events[0].Metadata.Signature, events[0].Metadata.IxIndex, events[0].Metadata.Slot,
	).Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, 1, count)
}

// cleanupDB drops the test database
func cleanupDB(t *testing.T, c *config) {
	// Connect to postgres database to drop the test database
	c.Database = "postgres"
	ctx := context.Background()
	repo, err := newRepo(ctx, c)
	require.NoError(t, err)
	defer repo.Close()
}

func TestProcessSolTransfers_EmptyBatch(t *testing.T) {
	// Setup test database
	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer repo.Close()

	// Test empty batch
	err = repo.ProcessSolTransfers(ctx, nil)
	require.NoError(t, err)

	err = repo.ProcessSolTransfers(ctx, []*pb.SolTransferEvent{})
	require.NoError(t, err)
}

func TestProcessTokenTransfers(t *testing.T) {
	// Setup test database
	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupDB(t, config)
	}()

	// Test data
	events := []*pb.TokenTransferEvent{
		{
			Metadata: &pb.EventMetadata{
				Signature: []byte("test_sig_1"),
				IxIndex:   0,
				Slot:      345600001, // Use a slot within the first partition
			},
			TokenMint:  "token_mint_1",
			FromWallet: "wallet1",
			ToWallet:   "wallet2",
			Amount:     "1000000",
		},
		{
			Metadata: &pb.EventMetadata{
				Signature: []byte("test_sig_2"),
				IxIndex:   1,
				Slot:      345600002, // Use a slot within the first partition
			},
			TokenMint:  "token_mint_2",
			FromWallet: "wallet3",
			ToWallet:   "wallet4",
			Amount:     "2000000",
		},
	}

	// Test inserting transfers
	err = repo.ProcessTokenTransfers(ctx, events)
	require.NoError(t, err)

	// Verify the transfers were inserted correctly
	for _, event := range events {
		var transfer models.TokenTransfer
		err := repo.pool.QueryRow(ctx, `
			SELECT signature, ix_index, slot, token_mint, from_wallet, to_wallet, amount 
			FROM token_transfers 
			WHERE signature = $1 AND ix_index = $2 AND slot = $3`,
			event.Metadata.Signature, event.Metadata.IxIndex, event.Metadata.Slot,
		).Scan(
			&transfer.Signature,
			&transfer.IxIndex,
			&transfer.Slot,
			&transfer.TokenMint,
			&transfer.FromWallet,
			&transfer.ToWallet,
			&transfer.Amount,
		)
		require.NoError(t, err)

		assert.Equal(t, event.Metadata.Signature, transfer.Signature)
		assert.Equal(t, uint16(event.Metadata.IxIndex), transfer.IxIndex)
		assert.Equal(t, event.Metadata.Slot, transfer.Slot)
		assert.Equal(t, []byte(event.TokenMint), transfer.TokenMint)
		assert.Equal(t, []byte(event.FromWallet), transfer.FromWallet)
		assert.Equal(t, []byte(event.ToWallet), transfer.ToWallet)
		assert.Equal(t, event.Amount, fmt.Sprintf("%d", transfer.Amount))
	}

	// Test duplicate handling
	err = repo.ProcessTokenTransfers(ctx, events)
	require.NoError(t, err) // Should not error on duplicates

	// Verify no duplicates were inserted
	var count int
	err = repo.pool.QueryRow(ctx, `
		SELECT COUNT(*) 
		FROM token_transfers 
		WHERE signature = $1 AND ix_index = $2 AND slot = $3`,
		events[0].Metadata.Signature, events[0].Metadata.IxIndex, events[0].Metadata.Slot,
	).Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, 1, count)
}

func TestProcessTokenTransfers_EmptyBatch(t *testing.T) {
	// Setup test database
	config := defaultConfig()
	config.Host = "localhost"
	config.Port = 5432
	config.User = "postgres"
	config.Password = "postgres"
	config.Database = "solana_data"
	config.SSLMode = "disable"

	ctx := context.Background()
	repo, err := newRepo(ctx, config)
	require.NoError(t, err)
	defer func() {
		repo.Close()
		cleanupDB(t, config)
	}()

	// Test empty batch
	err = repo.ProcessTokenTransfers(ctx, []*pb.TokenTransferEvent{})
	require.NoError(t, err)
}

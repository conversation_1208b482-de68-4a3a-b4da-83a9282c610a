package db

import (
	"context"
	"fmt"
	"sync"

	"github.com/jackc/pgx/v5"
	"github.com/mr-tron/base58"
)

// WalletTransferStats represents transfer statistics between two wallets
type WalletTransferStats struct {
	AssociatedWallet string
	InTransfers      int
	OutTransfers     int
	IsBidirectional  bool
}

// ShouldExcludeWalletResult represents the result of checking if a wallet should be excluded
type ShouldExcludeWalletResult struct {
	WalletAddress    string
	MinSlot          int64
	MaxSlot          int64
	TransactionCount int
	ShouldExclude    bool
}

const (
	// Token mint addresses
	USDC_MINT = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" // USDC
	WSOL_MINT = "So11111111111111111111111111111111111111112"  // Wrapped SOL
)

// GetWalletTransfers retrieves transfer statistics for a given wallet and token mint
func (r *Repo) GetWalletTransfers(ctx context.Context, wallet string, tokenMint string) ([]WalletTransferStats, error) {
	// Get all transfers involving this wallet
	query := `
WITH all_transfers AS (
    -- Each of these subqueries is now simple and can use an index efficiently.
    -- The parentheses are required for UNION ALL with ORDER BY/LIMIT.
    (SELECT from_wallet, to_wallet, slot FROM sol_transfers 
     WHERE from_wallet = from_base58($1) 
     AND amount >= 1000000000 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
    UNION ALL
    (SELECT from_wallet, to_wallet, slot FROM sol_transfers 
     WHERE to_wallet = from_base58($1) 
     AND amount >= 1000000000 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
    UNION ALL
    (SELECT from_wallet, to_wallet, slot FROM token_transfers 
     WHERE from_wallet = from_base58($1) 
     AND token_mint = from_base58($2) 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
    UNION ALL
    (SELECT from_wallet, to_wallet, slot FROM token_transfers 
     WHERE to_wallet = from_base58($1) 
     AND token_mint = from_base58($2) 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
    UNION ALL
    (SELECT from_wallet, to_wallet, slot FROM token_transfers 
     WHERE from_wallet = from_base58($1) 
     AND token_mint = from_base58($3) 
     AND amount >= 1000000 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
    UNION ALL
    (SELECT from_wallet, to_wallet, slot FROM token_transfers 
     WHERE to_wallet = from_base58($1) 
     AND token_mint = from_base58($3) 
     AND amount >= 1000000 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
    UNION ALL
    (SELECT from_wallet, to_wallet, slot FROM token_transfers 
     WHERE from_wallet = from_base58($1) 
     AND token_mint = from_base58($4) 
     AND amount >= 1000000000 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
    UNION ALL
    (SELECT from_wallet, to_wallet, slot FROM token_transfers 
     WHERE to_wallet = from_base58($1) 
     AND token_mint = from_base58($4) 
     AND amount >= 1000000000 
     AND slot >= 344736000 
     ORDER BY slot DESC 
     LIMIT 200)
),
latest_transfers AS (
    SELECT 
        CASE WHEN from_wallet = from_base58($1) THEN to_wallet ELSE from_wallet END as associated_wallet,
        CASE WHEN from_wallet = from_base58($1) THEN 'out' ELSE 'in' END as direction,
        slot
    FROM all_transfers
    ORDER BY slot DESC
    LIMIT 200
)
SELECT 
    associated_wallet,
    direction,
    COUNT(*) as transfer_count
FROM latest_transfers
GROUP BY associated_wallet, direction
ORDER BY transfer_count DESC`

	// Use a single transaction for the entire operation
	var result []WalletTransferStats
	err := r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
		rows, err := tx.Query(ctx, query, wallet, tokenMint, USDC_MINT, WSOL_MINT)
		if err != nil {
			return fmt.Errorf("failed to query transfers: %v", err)
		}
		defer rows.Close()

		// Map to store wallet transfer counts
		walletTransfers := make(map[string]*WalletTransferStats)

		for rows.Next() {
			var associatedWalletBytes []byte
			var direction string
			var count int

			if err := rows.Scan(&associatedWalletBytes, &direction, &count); err != nil {
				return fmt.Errorf("failed to scan transfer row: %v", err)
			}

			associatedWallet := base58.Encode(associatedWalletBytes)
			wallet, exists := walletTransfers[associatedWallet]
			if !exists {
				wallet = &WalletTransferStats{
					AssociatedWallet: associatedWallet,
				}
				walletTransfers[associatedWallet] = wallet
			}

			if direction == "in" {
				wallet.InTransfers = count
			} else {
				wallet.OutTransfers = count
			}

			// Check if transfers are bidirectional
			if wallet.InTransfers > 0 && wallet.OutTransfers > 0 {
				wallet.IsBidirectional = true
			}
		}

		// Convert map to slice
		for _, wallet := range walletTransfers {
			result = append(result, *wallet)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetWalletsTransfers retrieves transfer statistics for multiple wallets in batches
func (r *Repo) GetWalletsTransfers(ctx context.Context, wallets []string, tokenMint string) (map[string][]WalletTransferStats, error) {
	if len(wallets) == 0 {
		return make(map[string][]WalletTransferStats), nil
	}

	const batchSize = 20
	result := make(map[string][]WalletTransferStats)
	resultMu := sync.Mutex{} // Mutex to protect concurrent map access

	// Create work groups
	workGroups := 5
	workChan := make(chan []string, workGroups)
	errChan := make(chan error, workGroups)
	var wg sync.WaitGroup

	// Start worker goroutines
	for i := 0; i < workGroups; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for batch := range workChan {
				// Build the VALUES clause for the query
				valuesClause := "VALUES "
				for j := range batch {
					if j > 0 {
						valuesClause += ","
					}
					valuesClause += fmt.Sprintf("(from_base58($%d))", j+1)
				}

				// Define parameter positions AFTER the wallet parameters
				paramCount := len(batch)
				tokenMintPos := paramCount + 1
				usdcMintPos := paramCount + 2
				wsolMintPos := paramCount + 3

				query := fmt.Sprintf(`
WITH wallets_to_query (wallet_address) AS (
    %s
)
SELECT 
    w.wallet_address,
    t.associated_wallet,
    t.direction,
    t.transfer_count
FROM 
    wallets_to_query w
CROSS JOIN LATERAL (
    WITH all_transfers AS (
        (SELECT from_wallet, to_wallet, slot FROM sol_transfers 
         WHERE from_wallet = w.wallet_address 
         AND amount >= 1000000000 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
        UNION ALL
        (SELECT from_wallet, to_wallet, slot FROM sol_transfers 
         WHERE to_wallet = w.wallet_address 
         AND amount >= 1000000000 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
        UNION ALL
        (SELECT from_wallet, to_wallet, slot FROM token_transfers 
         WHERE from_wallet = w.wallet_address 
         AND token_mint = from_base58($%d) 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
        UNION ALL
        (SELECT from_wallet, to_wallet, slot FROM token_transfers 
         WHERE to_wallet = w.wallet_address 
         AND token_mint = from_base58($%d) 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
        UNION ALL
        (SELECT from_wallet, to_wallet, slot FROM token_transfers 
         WHERE from_wallet = w.wallet_address 
         AND token_mint = from_base58($%d) 
         AND amount >= 1000000 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
        UNION ALL
        (SELECT from_wallet, to_wallet, slot FROM token_transfers 
         WHERE to_wallet = w.wallet_address 
         AND token_mint = from_base58($%d) 
         AND amount >= 1000000 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
        UNION ALL
        (SELECT from_wallet, to_wallet, slot FROM token_transfers 
         WHERE from_wallet = w.wallet_address 
         AND token_mint = from_base58($%d) 
         AND amount >= 1000000000 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
        UNION ALL
        (SELECT from_wallet, to_wallet, slot FROM token_transfers 
         WHERE to_wallet = w.wallet_address 
         AND token_mint = from_base58($%d) 
         AND amount >= 1000000000 
         AND slot >= 344736000 
         ORDER BY slot DESC 
         LIMIT 200)
    ),
    latest_transfers AS (
        SELECT 
            CASE WHEN from_wallet = w.wallet_address THEN to_wallet ELSE from_wallet END as associated_wallet,
            CASE WHEN from_wallet = w.wallet_address THEN 'out' ELSE 'in' END as direction,
            slot
        FROM all_transfers
        ORDER BY slot DESC
        LIMIT 200
    )
    SELECT 
        associated_wallet,
        direction,
        COUNT(*) as transfer_count
    FROM latest_transfers
    GROUP BY associated_wallet, direction
    ORDER BY transfer_count DESC
) t`,
					valuesClause,
					tokenMintPos, tokenMintPos,
					usdcMintPos, usdcMintPos,
					wsolMintPos, wsolMintPos)

				// Prepare query arguments
				args := make([]interface{}, paramCount+3)
				for j, wallet := range batch {
					args[j] = wallet
				}
				args[paramCount] = tokenMint
				args[paramCount+1] = USDC_MINT
				args[paramCount+2] = WSOL_MINT

				// Execute query
				err := r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
					rows, err := tx.Query(ctx, query, args...)
					if err != nil {
						return fmt.Errorf("failed to query transfers: %v", err)
					}
					defer rows.Close()

					// Process results for each wallet
					currentWallet := ""
					walletTransfers := make(map[string]*WalletTransferStats)
					batchResults := make(map[string][]WalletTransferStats)

					for rows.Next() {
						var walletBytes, associatedWalletBytes []byte
						var direction string
						var count int

						if err := rows.Scan(&walletBytes, &associatedWalletBytes, &direction, &count); err != nil {
							return fmt.Errorf("failed to scan transfer row: %v", err)
						}

						wallet := base58.Encode(walletBytes)
						associatedWallet := base58.Encode(associatedWalletBytes)

						if wallet != currentWallet {
							if currentWallet != "" {
								var stats []WalletTransferStats
								for _, w := range walletTransfers {
									stats = append(stats, *w)
								}
								batchResults[currentWallet] = stats
							}
							currentWallet = wallet
							walletTransfers = make(map[string]*WalletTransferStats)
						}

						transfer, exists := walletTransfers[associatedWallet]
						if !exists {
							transfer = &WalletTransferStats{
								AssociatedWallet: associatedWallet,
							}
							walletTransfers[associatedWallet] = transfer
						}

						if direction == "in" {
							transfer.InTransfers = count
						} else {
							transfer.OutTransfers = count
						}

						if transfer.InTransfers > 0 && transfer.OutTransfers > 0 {
							transfer.IsBidirectional = true
						}
					}

					if currentWallet != "" {
						var stats []WalletTransferStats
						for _, w := range walletTransfers {
							stats = append(stats, *w)
						}
						batchResults[currentWallet] = stats
					}

					// Safely merge results into the main result map
					resultMu.Lock()
					for wallet, stats := range batchResults {
						result[wallet] = stats
					}
					resultMu.Unlock()

					return nil
				})

				if err != nil {
					errChan <- err
					return
				}
			}
		}()
	}

	// Send work to workers
	for i := 0; i < len(wallets); i += batchSize {
		end := i + batchSize
		if end > len(wallets) {
			end = len(wallets)
		}
		workChan <- wallets[i:end]
	}
	close(workChan)

	// Wait for all workers to complete
	wg.Wait()
	close(errChan)

	// Check for any errors
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

// CheckWalletsForExclusion checks if a list of wallets should be excluded based on their transaction patterns
func (r *Repo) CheckWalletsForExclusion(ctx context.Context, wallets []string) (map[string]bool, error) {
	if len(wallets) == 0 {
		return make(map[string]bool), nil
	}

	// Build the VALUES clause for the query with from_base58 function
	valuesClause := "VALUES "
	for i := range wallets {
		if i > 0 {
			valuesClause += ","
		}
		valuesClause += fmt.Sprintf("(from_base58($%d))", i+1)
	}

	query := fmt.Sprintf(`
		WITH wallets_to_query (wallet_address) AS (
			%s
		)
		SELECT 
			w.wallet_address, 
			t.*
		FROM 
			wallets_to_query w
		CROSS JOIN LATERAL (
			SELECT 
				COALESCE(min(slot), 0) as min_slot, 
				COALESCE(max(slot), 0) as max_slot, 
				COALESCE(count(*), 0) as transaction_count,
				(COALESCE(count(*), 0) = 100 AND COALESCE(MAX(slot), 0) - COALESCE(MIN(slot), 0) < 27000) AS should_exclude
			FROM (
				SELECT slot FROM (
					-- Native SOL transfers (from)
					(
						SELECT slot FROM public.sol_transfers
						WHERE from_wallet = w.wallet_address
						ORDER BY slot DESC
						LIMIT 100
					)
					UNION ALL
					-- Native SOL transfers (to)
					(
						SELECT slot FROM public.sol_transfers
						WHERE to_wallet = w.wallet_address
						ORDER BY slot DESC
						LIMIT 100
					)
					UNION ALL
					-- USDC transfers (from)
					(
						SELECT slot FROM public.token_transfers
						WHERE from_wallet = w.wallet_address
						AND token_mint = from_base58($%d)
						ORDER BY slot DESC
						LIMIT 100
					)
					UNION ALL
					-- USDC transfers (to)
					(
						SELECT slot FROM public.token_transfers
						WHERE to_wallet = w.wallet_address
						AND token_mint = from_base58($%d)
						ORDER BY slot DESC
						LIMIT 100
					)
					UNION ALL
					-- WSOL transfers (from)
					(
						SELECT slot FROM public.token_transfers
						WHERE from_wallet = w.wallet_address
						AND token_mint = from_base58($%d)
						ORDER BY slot DESC
						LIMIT 100
					)
					UNION ALL
					-- WSOL transfers (to)
					(
						SELECT slot FROM public.token_transfers
						WHERE to_wallet = w.wallet_address
						AND token_mint = from_base58($%d)
						ORDER BY slot DESC
						LIMIT 100
					)
				) AS combined_slots
				ORDER BY slot DESC
				LIMIT 100
			) AS final_aggregation
		) t`, valuesClause, len(wallets)+1, len(wallets)+2, len(wallets)+3, len(wallets)+4)

	// Prepare query arguments
	args := make([]interface{}, len(wallets)+4)
	for i, wallet := range wallets {
		args[i] = wallet // Pass the base58 string directly
	}
	args[len(wallets)] = USDC_MINT
	args[len(wallets)+1] = USDC_MINT
	args[len(wallets)+2] = WSOL_MINT
	args[len(wallets)+3] = WSOL_MINT

	// Execute query
	var result map[string]bool
	err := r.WithTransaction(ctx, func(ctx context.Context, tx pgx.Tx) error {
		rows, err := tx.Query(ctx, query, args...)
		if err != nil {
			return fmt.Errorf("failed to query wallet exclusions: %v", err)
		}
		defer rows.Close()

		result = make(map[string]bool)
		for rows.Next() {
			var walletBytes []byte
			var minSlot, maxSlot int64
			var transactionCount int
			var shouldExclude bool

			if err := rows.Scan(&walletBytes, &minSlot, &maxSlot, &transactionCount, &shouldExclude); err != nil {
				return fmt.Errorf("failed to scan wallet exclusion row: %v", err)
			}

			wallet := base58.Encode(walletBytes)
			result[wallet] = shouldExclude
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

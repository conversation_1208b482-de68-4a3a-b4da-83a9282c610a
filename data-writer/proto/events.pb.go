// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/events.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Signature     []byte                 `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`
	Slot          uint64                 `protobuf:"varint,2,opt,name=slot,proto3" json:"slot,omitempty"`
	IxIndex       uint32                 `protobuf:"varint,3,opt,name=ix_index,json=ixIndex,proto3" json:"ix_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventMetadata) Reset() {
	*x = EventMetadata{}
	mi := &file_proto_events_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventMetadata) ProtoMessage() {}

func (x *EventMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventMetadata.ProtoReflect.Descriptor instead.
func (*EventMetadata) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{0}
}

func (x *EventMetadata) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *EventMetadata) GetSlot() uint64 {
	if x != nil {
		return x.Slot
	}
	return 0
}

func (x *EventMetadata) GetIxIndex() uint32 {
	if x != nil {
		return x.IxIndex
	}
	return 0
}

// Topic: solana-sol-transfers
type SolTransferEvent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *EventMetadata         `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	FromWallet     string                 `protobuf:"bytes,2,opt,name=from_wallet,json=fromWallet,proto3" json:"from_wallet,omitempty"`
	ToWallet       string                 `protobuf:"bytes,3,opt,name=to_wallet,json=toWallet,proto3" json:"to_wallet,omitempty"`
	AmountLamports uint64                 `protobuf:"varint,4,opt,name=amount_lamports,json=amountLamports,proto3" json:"amount_lamports,omitempty"` // in lamports
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SolTransferEvent) Reset() {
	*x = SolTransferEvent{}
	mi := &file_proto_events_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolTransferEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolTransferEvent) ProtoMessage() {}

func (x *SolTransferEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolTransferEvent.ProtoReflect.Descriptor instead.
func (*SolTransferEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{1}
}

func (x *SolTransferEvent) GetMetadata() *EventMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SolTransferEvent) GetFromWallet() string {
	if x != nil {
		return x.FromWallet
	}
	return ""
}

func (x *SolTransferEvent) GetToWallet() string {
	if x != nil {
		return x.ToWallet
	}
	return ""
}

func (x *SolTransferEvent) GetAmountLamports() uint64 {
	if x != nil {
		return x.AmountLamports
	}
	return 0
}

// Topic: solana-token-transfers
type TokenTransferEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *EventMetadata         `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	TokenMint     string                 `protobuf:"bytes,2,opt,name=token_mint,json=tokenMint,proto3" json:"token_mint,omitempty"`
	FromWallet    string                 `protobuf:"bytes,3,opt,name=from_wallet,json=fromWallet,proto3" json:"from_wallet,omitempty"`
	ToWallet      string                 `protobuf:"bytes,4,opt,name=to_wallet,json=toWallet,proto3" json:"to_wallet,omitempty"`
	Amount        string                 `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenTransferEvent) Reset() {
	*x = TokenTransferEvent{}
	mi := &file_proto_events_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenTransferEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenTransferEvent) ProtoMessage() {}

func (x *TokenTransferEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenTransferEvent.ProtoReflect.Descriptor instead.
func (*TokenTransferEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{2}
}

func (x *TokenTransferEvent) GetMetadata() *EventMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *TokenTransferEvent) GetTokenMint() string {
	if x != nil {
		return x.TokenMint
	}
	return ""
}

func (x *TokenTransferEvent) GetFromWallet() string {
	if x != nil {
		return x.FromWallet
	}
	return ""
}

func (x *TokenTransferEvent) GetToWallet() string {
	if x != nil {
		return x.ToWallet
	}
	return ""
}

func (x *TokenTransferEvent) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type SolanaEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Event:
	//
	//	*SolanaEvent_SolTransfer
	//	*SolanaEvent_TokenTransfer
	Event         isSolanaEvent_Event `protobuf_oneof:"event"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SolanaEvent) Reset() {
	*x = SolanaEvent{}
	mi := &file_proto_events_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SolanaEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SolanaEvent) ProtoMessage() {}

func (x *SolanaEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SolanaEvent.ProtoReflect.Descriptor instead.
func (*SolanaEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{3}
}

func (x *SolanaEvent) GetEvent() isSolanaEvent_Event {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *SolanaEvent) GetSolTransfer() *SolTransferEvent {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_SolTransfer); ok {
			return x.SolTransfer
		}
	}
	return nil
}

func (x *SolanaEvent) GetTokenTransfer() *TokenTransferEvent {
	if x != nil {
		if x, ok := x.Event.(*SolanaEvent_TokenTransfer); ok {
			return x.TokenTransfer
		}
	}
	return nil
}

type isSolanaEvent_Event interface {
	isSolanaEvent_Event()
}

type SolanaEvent_SolTransfer struct {
	SolTransfer *SolTransferEvent `protobuf:"bytes,1,opt,name=sol_transfer,json=solTransfer,proto3,oneof"`
}

type SolanaEvent_TokenTransfer struct {
	TokenTransfer *TokenTransferEvent `protobuf:"bytes,2,opt,name=token_transfer,json=tokenTransfer,proto3,oneof"`
}

func (*SolanaEvent_SolTransfer) isSolanaEvent_Event() {}

func (*SolanaEvent_TokenTransfer) isSolanaEvent_Event() {}

// Topic: solana-events
type BatchedSolanaEvent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Events        []*SolanaEvent         `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchedSolanaEvent) Reset() {
	*x = BatchedSolanaEvent{}
	mi := &file_proto_events_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchedSolanaEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchedSolanaEvent) ProtoMessage() {}

func (x *BatchedSolanaEvent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_events_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchedSolanaEvent.ProtoReflect.Descriptor instead.
func (*BatchedSolanaEvent) Descriptor() ([]byte, []int) {
	return file_proto_events_proto_rawDescGZIP(), []int{4}
}

func (x *BatchedSolanaEvent) GetEvents() []*SolanaEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

var File_proto_events_proto protoreflect.FileDescriptor

const file_proto_events_proto_rawDesc = "" +
	"\n" +
	"\x12proto/events.proto\x12\veventstream\"\\\n" +
	"\rEventMetadata\x12\x1c\n" +
	"\tsignature\x18\x01 \x01(\fR\tsignature\x12\x12\n" +
	"\x04slot\x18\x02 \x01(\x04R\x04slot\x12\x19\n" +
	"\bix_index\x18\x03 \x01(\rR\aixIndex\"\xb1\x01\n" +
	"\x10SolTransferEvent\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.eventstream.EventMetadataR\bmetadata\x12\x1f\n" +
	"\vfrom_wallet\x18\x02 \x01(\tR\n" +
	"fromWallet\x12\x1b\n" +
	"\tto_wallet\x18\x03 \x01(\tR\btoWallet\x12'\n" +
	"\x0famount_lamports\x18\x04 \x01(\x04R\x0eamountLamports\"\xc1\x01\n" +
	"\x12TokenTransferEvent\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.eventstream.EventMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"token_mint\x18\x02 \x01(\tR\ttokenMint\x12\x1f\n" +
	"\vfrom_wallet\x18\x03 \x01(\tR\n" +
	"fromWallet\x12\x1b\n" +
	"\tto_wallet\x18\x04 \x01(\tR\btoWallet\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\"\xa4\x01\n" +
	"\vSolanaEvent\x12B\n" +
	"\fsol_transfer\x18\x01 \x01(\v2\x1d.eventstream.SolTransferEventH\x00R\vsolTransfer\x12H\n" +
	"\x0etoken_transfer\x18\x02 \x01(\v2\x1f.eventstream.TokenTransferEventH\x00R\rtokenTransferB\a\n" +
	"\x05event\"F\n" +
	"\x12BatchedSolanaEvent\x120\n" +
	"\x06events\x18\x01 \x03(\v2\x18.eventstream.SolanaEventR\x06eventsB6Z4github.com/kryptogo/kg-solana-data/data-writer/protob\x06proto3"

var (
	file_proto_events_proto_rawDescOnce sync.Once
	file_proto_events_proto_rawDescData []byte
)

func file_proto_events_proto_rawDescGZIP() []byte {
	file_proto_events_proto_rawDescOnce.Do(func() {
		file_proto_events_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_events_proto_rawDesc), len(file_proto_events_proto_rawDesc)))
	})
	return file_proto_events_proto_rawDescData
}

var file_proto_events_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proto_events_proto_goTypes = []any{
	(*EventMetadata)(nil),      // 0: eventstream.EventMetadata
	(*SolTransferEvent)(nil),   // 1: eventstream.SolTransferEvent
	(*TokenTransferEvent)(nil), // 2: eventstream.TokenTransferEvent
	(*SolanaEvent)(nil),        // 3: eventstream.SolanaEvent
	(*BatchedSolanaEvent)(nil), // 4: eventstream.BatchedSolanaEvent
}
var file_proto_events_proto_depIdxs = []int32{
	0, // 0: eventstream.SolTransferEvent.metadata:type_name -> eventstream.EventMetadata
	0, // 1: eventstream.TokenTransferEvent.metadata:type_name -> eventstream.EventMetadata
	1, // 2: eventstream.SolanaEvent.sol_transfer:type_name -> eventstream.SolTransferEvent
	2, // 3: eventstream.SolanaEvent.token_transfer:type_name -> eventstream.TokenTransferEvent
	3, // 4: eventstream.BatchedSolanaEvent.events:type_name -> eventstream.SolanaEvent
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proto_events_proto_init() }
func file_proto_events_proto_init() {
	if File_proto_events_proto != nil {
		return
	}
	file_proto_events_proto_msgTypes[3].OneofWrappers = []any{
		(*SolanaEvent_SolTransfer)(nil),
		(*SolanaEvent_TokenTransfer)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_events_proto_rawDesc), len(file_proto_events_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_events_proto_goTypes,
		DependencyIndexes: file_proto_events_proto_depIdxs,
		MessageInfos:      file_proto_events_proto_msgTypes,
	}.Build()
	File_proto_events_proto = out.File
	file_proto_events_proto_goTypes = nil
	file_proto_events_proto_depIdxs = nil
}

package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"

	"github.com/kryptogo/kg-solana-data/data-writer/analyzer"
	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

func readWalletsFromFile(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open wallet file: %v", err)
	}
	defer file.Close()

	var wallets []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		wallet := strings.TrimSpace(scanner.Text())
		if wallet != "" {
			wallets = append(wallets, wallet)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading wallet file: %v", err)
	}

	return wallets, nil
}

func main() {
	// Parse command line flags
	wallets := flag.String("wallets", "", "Comma-separated list of wallet addresses to analyze")
	walletFile := flag.String("wallet-file", "", "Path to file containing wallet addresses (one per line)")
	tokenMint := flag.String("token", "", "Token mint address to analyze")
	minTransfers := flag.Int("min-transfers", 1, "Minimum number of transfers to consider a wallet associated")
	includeOneWay := flag.Bool("include-one-way", true, "Include one-way transfer relationships")
	minOneWayTransfers := flag.Int("min-one-way", 1, "Minimum number of transfers for one-way relationships")
	flag.Parse()

	// Validate required flags
	if (*wallets == "" && *walletFile == "") || *tokenMint == "" {
		fmt.Println("Error: either wallets or wallet-file, and token flags are required")
		flag.Usage()
		os.Exit(1)
	}

	// Get wallet list
	var walletList []string
	var err error
	if *walletFile != "" {
		walletList, err = readWalletsFromFile(*walletFile)
		if err != nil {
			fmt.Printf("Error reading wallet file: %v\n", err)
			os.Exit(1)
		}
	} else {
		walletList = strings.Split(*wallets, ",")
		for i, w := range walletList {
			walletList[i] = strings.TrimSpace(w)
		}
	}

	// Remove empty wallets
	var validWallets []string
	for _, w := range walletList {
		if w != "" {
			validWallets = append(validWallets, w)
		}
	}
	walletList = validWallets

	if len(walletList) == 0 {
		fmt.Println("Error: no valid wallet addresses provided")
		os.Exit(1)
	}

	// Initialize logger
	ctx := context.Background()
	if err := logger.Initialize(ctx, "kryptogo-wallet-analyzer"); err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Close()

	// Initialize database
	if err := db.Initialize(); err != nil {
		fmt.Printf("Failed to initialize database: %v\n", err)
		os.Exit(1)
	}
	defer db.Close()

	// Create analyzer with config
	config := analyzer.WalletAnalyzerConfig{
		MinTransfers:       *minTransfers,
		IncludeOneWay:      *includeOneWay,
		MinOneWayTransfers: *minOneWayTransfers,
	}
	analyzer := analyzer.NewWalletAnalyzer(config)

	// Analyze wallet clusters
	clusters, err := analyzer.AnalyzeWalletsCluster(ctx, walletList, *tokenMint)
	if err != nil {
		fmt.Printf("Error analyzing wallet clusters: %v\n", err)
		os.Exit(1)
	}

	// Print results
	output := struct {
		TokenMint string     `json:"token_mint"`
		Clusters  [][]string `json:"clusters"`
	}{
		TokenMint: *tokenMint,
		Clusters:  clusters,
	}

	outputFile := "wallet_clusters.json"
	file, err := os.Create(outputFile)
	if err != nil {
		fmt.Printf("Error creating output file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(output); err != nil {
		fmt.Printf("Error encoding results: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Results written to %s\n", outputFile)
}

package main

import (
	"context"
	"flag"
	"log"

	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-solana-data/data-writer/db"
)

func main() {
	// Parse command line flags
	rpcEndpoint := flag.String("rpc", "https://api.mainnet-beta.solana.com", "Solana RPC endpoint")
	flag.Parse()

	// Initialize database connection
	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	client := rpc.New(*rpcEndpoint)
	if err := createNextPartitions(context.Background(), client); err != nil {
		log.Printf("Error creating partitions: %v", err)
	}
}

func createNextPartitions(ctx context.Context, client *rpc.Client) error {
	// Get current slot
	slot, err := client.GetSlot(ctx, rpc.CommitmentFinalized)
	if err != nil {
		return err
	}

	repo := db.Get()

	// Create next partition for sol_transfers
	if err := repo.CreateNextPartition(ctx, "sol_transfers", slot); err != nil {
		return err
	}

	// Create next partition for token_transfers
	if err := repo.CreateNextPartition(ctx, "token_transfers", slot); err != nil {
		return err
	}

	log.Printf("Successfully created next partitions for slot %d", slot)
	return nil
}

package main

import (
	"context"
	"fmt"
	"log"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
)

// ExampleUsage demonstrates how to use the CalculateTokenBalanceHistory function
// programmatically from other Go code.
func ExampleUsage() {
	// Example parameters
	tokenMint := "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" // USDC token mint
	wallets := []string{
		"9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
		"2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S",
	}
	config := TokenBalanceConfig{
		FromSlot: 250000000, // Starting from slot 250M
		RPCURL:   "",        // Will use environment variable or default
	}

	// Initialize dependencies (normally done in main or init functions)
	ctx := context.Background()

	// Note: In a real application, you would initialize these once at startup
	if err := logger.Initialize(ctx, "example-app"); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Call the reusable function
	result, err := CalculateTokenBalanceHistory(ctx, tokenMint, wallets, config)
	if err != nil {
		log.Fatalf("Failed to calculate token balance history: %v", err)
	}

	// Use the results
	fmt.Printf("Total current balance: %d\n", result.TotalCurrentBalance)
	fmt.Printf("Number of intervals: %d\n", result.NumIntervals)
	fmt.Printf("Number of balance points: %d\n", len(result.BalancePoints))

	// Print first few balance points as example
	for i, point := range result.BalancePoints {
		if i >= 5 { // Only show first 5 points
			break
		}
		fmt.Printf("Point %d: Timestamp=%d, Balance=%d\n", i, point.Timestamp, point.Balance)
	}
}

// AnotherExampleWithCustomRPC shows how to use a custom RPC endpoint
func AnotherExampleWithCustomRPC() {
	ctx := context.Background()

	// Assume logger and db are already initialized

	tokenMint := "So11111111111111111111111111111111111111112" // Wrapped SOL
	wallets := []string{"9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"}

	config := TokenBalanceConfig{
		FromSlot: 250000000,
		RPCURL:   "https://api.mainnet-beta.solana.com", // Custom RPC endpoint
	}

	result, err := CalculateTokenBalanceHistory(ctx, tokenMint, wallets, config)
	if err != nil {
		log.Printf("Error: %v", err)
		return
	}

	fmt.Printf("Successfully calculated balance history with %d points\n", len(result.BalancePoints))
}

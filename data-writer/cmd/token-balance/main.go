package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/mr-tron/base58"
)

type BalancePoint struct {
	Timestamp int64 `json:"timestamp"`
	Balance   int64 `json:"balance"`
}

func getCurrentTokenBalance(ctx context.Context, client *rpc.Client, wallet solana.PublicKey, tokenMint solana.PublicKey) (uint64, error) {
	// Find the token account for this wallet and mint
	tokenAccounts, err := client.GetTokenAccountsByOwner(
		ctx,
		wallet,
		&rpc.GetTokenAccountsConfig{
			Mint: &tokenMint,
		},
		&rpc.GetTokenAccountsOpts{
			Encoding: solana.EncodingBase64,
		},
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token accounts: %v", err)
	}

	if len(tokenAccounts.Value) == 0 {
		return 0, nil
	}

	// Get the token account data
	accountInfo, err := client.GetTokenAccountBalance(
		ctx,
		tokenAccounts.Value[0].Pubkey,
		rpc.CommitmentConfirmed,
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token account balance: %v", err)
	}

	return strconv.ParseUint(accountInfo.Value.Amount, 10, 64)
}

func readWalletsFromFile(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open wallet file: %v", err)
	}
	defer file.Close()

	var wallets []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		wallet := strings.TrimSpace(scanner.Text())
		if wallet != "" {
			wallets = append(wallets, wallet)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading wallet file: %v", err)
	}

	return wallets, nil
}

func main() {
	// Parse command line arguments
	walletsStr := flag.String("wallets", "", "Comma-separated list of wallet addresses")
	walletsFile := flag.String("wallets-file", "", "Path to file containing wallet addresses (one per line)")
	tokenMintStr := flag.String("token-mint", "", "Token mint address")
	fromSlot := flag.Uint64("from-slot", 0, "Starting slot number")
	flag.Parse()

	if (*walletsStr == "" && *walletsFile == "") || *tokenMintStr == "" || *fromSlot == 0 {
		log.Fatal("either wallets or wallets-file, token-mint, and from-slot are required")
	}

	// Initialize logger
	ctx := context.Background()
	if err := logger.Initialize(ctx, "token-balance"); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Close()

	// Initialize database
	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Get wallet addresses
	var walletStrs []string
	var err error
	if *walletsFile != "" {
		walletStrs, err = readWalletsFromFile(*walletsFile)
		if err != nil {
			log.Fatalf("Failed to read wallets from file: %v", err)
		}
	} else {
		walletStrs = strings.Split(*walletsStr, ",")
	}

	wallets := make([][]byte, len(walletStrs))
	solanaWallets := make([]solana.PublicKey, len(walletStrs))
	for i, w := range walletStrs {
		decoded, err := base58.Decode(w)
		if err != nil {
			log.Fatalf("Failed to decode wallet address %s: %v", w, err)
		}
		wallets[i] = decoded
		solanaWallets[i] = solana.PublicKeyFromBytes(decoded)
	}

	// Parse token mint
	tokenMint, err := base58.Decode(*tokenMintStr)
	if err != nil {
		log.Fatalf("Failed to decode token mint address: %v", err)
	}
	solanaTokenMint := solana.PublicKeyFromBytes(tokenMint)

	// Get RPC URL from environment or use default
	rpcURL := os.Getenv("RPC_URL")
	if rpcURL == "" {
		rpcURL = "https://api.mainnet-beta.solana.com"
	}

	// Initialize RPC client
	client := rpc.New(rpcURL)

	// Get current slot from RPC
	currentSlot, err := client.GetSlot(ctx, rpc.CommitmentFinalized)
	if err != nil {
		log.Fatalf("Failed to get current slot: %v", err)
	}

	// Calculate number of intervals
	slotRange := currentSlot - *fromSlot
	numIntervals := slotRange / 1500
	if slotRange%1500 != 0 {
		numIntervals++
	}

	// Get current balances for all wallets
	var totalCurrentBalance int64
	for _, wallet := range solanaWallets {
		balance, err := getCurrentTokenBalance(ctx, client, wallet, solanaTokenMint)
		if err != nil {
			log.Printf("Warning: Failed to get current balance for wallet %s: %v", wallet.String(), err)
			continue
		}
		totalCurrentBalance += int64(balance)
	}
	log.Printf("totalCurrentBalance: %d, numIntervals: %d", totalCurrentBalance, numIntervals)

	// Generate balance points
	balancePoints := make([]BalancePoint, numIntervals)
	currentBalance := totalCurrentBalance

	for i := uint64(0); i < numIntervals; i++ {
		endSlot := currentSlot - (i * 1500)
		startSlot := endSlot - 1500 + 1
		if startSlot < *fromSlot {
			startSlot = *fromSlot
		}
		log.Printf("startSlot: %d, endSlot: %d", startSlot, endSlot)

		// Measure time for getting balance changes
		startTime := time.Now()
		changes, err := db.Get().GetTokenBalanceChanges(ctx, wallets, tokenMint, startSlot, endSlot)
		if err != nil {
			log.Fatalf("Failed to get balance changes for interval %d: %v", i, err)
		}
		log.Printf("Time to get balance changes: %v", time.Since(startTime))

		// Calculate balance at this point
		currentBalance -= changes

		// Measure time for getting block time
		startTime = time.Now()
		blockTime, err := client.GetBlockTime(ctx, endSlot)
		if err != nil {
			log.Printf("Warning: Failed to get block time for slot %d: %v", endSlot, err)
			continue
		}
		log.Printf("Time to get block time: %v", time.Since(startTime))

		balancePoints[i] = BalancePoint{
			Timestamp: int64(*blockTime),
			Balance:   currentBalance,
		}
	}

	// Reverse the array to get chronological order
	for i, j := 0, len(balancePoints)-1; i < j; i, j = i+1, j-1 {
		balancePoints[i], balancePoints[j] = balancePoints[j], balancePoints[i]
	}

	// Write to JSON file
	outputFile := "balance_points.json"
	file, err := os.Create(outputFile)
	if err != nil {
		log.Fatalf("Failed to create output file: %v", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(balancePoints); err != nil {
		log.Fatalf("Failed to encode balance points: %v", err)
	}
	log.Printf("Successfully wrote balance points to %s", outputFile)
}

#!/bin/bash

# Build script for creating the Go FFI shared library

set -e

echo "🔨 Building Go FFI Library for Token Balance"
echo "============================================"

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Determine the platform and set appropriate file extensions
OS=$(uname -s)
case "$OS" in
Darwin)
    LIB_EXT="dylib"
    ;;
Linux)
    LIB_EXT="so"
    ;;
CYGWIN* | MINGW* | MSYS*)
    LIB_EXT="dll"
    ;;
*)
    echo "Unsupported operating system: $OS"
    exit 1
    ;;
esac

# Output files
HEADER_FILE="libtoken_balance.h"
LIBRARY_FILE="libtoken_balance.$LIB_EXT"

echo "Building for platform: $OS"
echo "Library file: $LIBRARY_FILE"
echo "Header file: $HEADER_FILE"

# Clean previous builds
echo "Cleaning previous builds..."
rm -f "$HEADER_FILE" "$LIBRARY_FILE"

# Build the shared library
echo "Building shared library..."
go build -buildmode=c-shared -o "$LIBRARY_FILE" ffi_lib.go

# Check if build was successful
if [ -f "$LIBRARY_FILE" ] && [ -f "$HEADER_FILE" ]; then
    echo "✅ Build successful!"
    echo "Generated files:"
    echo "  - $LIBRARY_FILE ($(du -h "$LIBRARY_FILE" | cut -f1))"
    echo "  - $HEADER_FILE"

    # Show the header file contents for reference
    echo ""
    echo "📄 Generated header file contents:"
    echo "=================================="
    cat "$HEADER_FILE"

    echo ""
    echo "🚀 Library is ready for FFI integration!"
    echo ""
    echo "To use in Rust:"
    echo "1. Copy $LIBRARY_FILE to your Rust project's lib directory"
    echo "2. Add FFI bindings based on the generated header file"
    echo "3. Link against the library in your build.rs or Cargo.toml"

else
    echo "❌ Build failed!"
    exit 1
fi

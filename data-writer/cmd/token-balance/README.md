# Token Balance Line Chart Generator

This command generates token balance line chart data for a list of wallets over time. It calculates the token balance at regular intervals (every 750 slots) and outputs the data points with timestamps.

## Usage

```bash
# Optional: Set custom RPC URL
export RPC_URL="https://your-custom-rpc-url.com"

# Run the command
./token_balance \
  --wallets="wallet1,wallet2,wallet3" \
  --token-mint="token_mint_address" \
  --from-slot=starting_slot_number
```

### Arguments

- `--wallets`: Comma-separated list of wallet addresses to track
- `--token-mint`: The token mint address to track balances for
- `--from-slot`: The starting slot number to begin tracking from
- `--wallets-file`: Path to a file containing wallet addresses (one per line). Alternative to --wallets flag

### Environment Variables

- `RPC_URL`: (Optional) Custom Solana RPC URL. If not set, defaults to "<https://api.mainnet-beta.solana.com>"

### Output

The command outputs a JSON array of balance points, where each point contains:

- `timestamp`: Unix timestamp of the balance point
- `balance`: Total token balance across all specified wallets at that time

Example output:

```json
[
  {
    "timestamp": 1678901234,
    "balance": 1000000
  },
  {
    "timestamp": 1678902345,
    "balance": 1500000
  }
]
```

## Notes

- The command uses the Solana RPC to get current slot, block timestamps, and current token balances
- Balance points are generated every 750 slots
- The output is ordered chronologically from past to present
- Current balances are fetched directly from the Solana blockchain
- Historical balances are calculated by working backwards from the current balance using transfer data from the database

# Token Balance Calculator

This package provides both a command-line tool and a reusable Go function for calculating historical token balance points for Solana wallets. It generates token balance line chart data for a list of wallets over time, calculating the token balance at regular intervals (every 1500 slots) and outputting the data points with timestamps.

## Command-Line Usage

```bash
# Optional: Set custom RPC URL
export RPC_URL="https://your-custom-rpc-url.com"

# Run the command
go run main.go \
  --wallets="wallet1,wallet2,wallet3" \
  --token-mint="token_mint_address" \
  --from-slot=starting_slot_number \
  --output="balance_points.json"
```

### Command-Line Arguments

- `--wallets`: Comma-separated list of wallet addresses to track
- `--token-mint`: The token mint address to track balances for
- `--from-slot`: The starting slot number to begin tracking from
- `--wallets-file`: Path to a file containing wallet addresses (one per line). Alternative to --wallets flag
- `--output`: Output JSON file path (default: "balance_points.json")

### Environment Variables

- `RPC_URL`: (Optional) Custom Solana RPC URL. If not set, defaults to "<https://api.mainnet-beta.solana.com>"

### Output

The command outputs a JSON array of balance points, where each point contains:

- `timestamp`: Unix timestamp of the balance point
- `balance`: Total token balance across all specified wallets at that time

Example output:

```json
[
  {
    "timestamp": 1678901234,
    "balance": 1000000
  },
  {
    "timestamp": 1678902345,
    "balance": 1500000
  }
]
```

## Programmatic Usage

The package now exports a reusable function `CalculateTokenBalanceHistory` that can be called from other Go code.

### Function Signature

```go
func CalculateTokenBalanceHistory(
    ctx context.Context,
    tokenMint string,
    wallets []string,
    config TokenBalanceConfig
) (*TokenBalanceResult, error)
```

### Parameters

- `ctx`: Context for the operation
- `tokenMint`: The token mint address as a string
- `wallets`: Slice of wallet addresses as strings
- `config`: Configuration struct with:
  - `FromSlot uint64`: Starting slot number (required, must be > 0)
  - `RPCURL string`: RPC endpoint URL (optional, defaults to environment variable or mainnet)

### Return Values

Returns a `TokenBalanceResult` struct containing:

- `BalancePoints []BalancePoint`: Array of historical balance points in chronological order
- `TotalCurrentBalance int64`: Current total balance across all wallets
- `NumIntervals uint64`: Number of intervals calculated

### Prerequisites

Before calling the function, you must initialize the required dependencies:

```go
// Initialize logger
ctx := context.Background()
if err := logger.Initialize(ctx, "your-app-name"); err != nil {
    log.Fatalf("Failed to initialize logger: %v", err)
}
defer logger.Close()

// Initialize database
if err := db.Initialize(); err != nil {
    log.Fatalf("Failed to initialize database: %v", err)
}
defer db.Close()
```

### Example Usage

```go
tokenMint := "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" // USDC
wallets := []string{"9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"}
config := TokenBalanceConfig{
    FromSlot: 250000000,
    RPCURL:   "", // Will use environment variable or default
}

result, err := CalculateTokenBalanceHistory(ctx, tokenMint, wallets, config)
if err != nil {
    log.Fatalf("Failed to calculate balance history: %v", err)
}

fmt.Printf("Current total balance: %d\n", result.TotalCurrentBalance)
```

## Notes

- The command uses the Solana RPC to get current slot, block timestamps, and current token balances
- Balance points are generated every 1500 slots
- The output is ordered chronologically from past to present
- Current balances are fetched directly from the Solana blockchain
- Historical balances are calculated by working backwards from the current balance using transfer data from the database
- The reusable function requires proper initialization of logger and database dependencies

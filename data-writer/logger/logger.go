package logger

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"cloud.google.com/go/logging"
)

var (
	client *logging.Client
	mu     sync.Mutex
)

// LogEntry represents a structured log entry in GCP format
type LogEntry struct {
	Severity       string                 `json:"severity"`
	Timestamp      string                 `json:"timestamp"`
	Message        string                 `json:"message"`
	JSONPayload    map[string]interface{} `json:"jsonPayload,omitempty"`
	Labels         map[string]string      `json:"labels,omitempty"`
	SourceLocation *SourceLocation        `json:"sourceLocation,omitempty"`
}

type SourceLocation struct {
	File string `json:"file"`
	Line string `json:"line"`
}

// Initialize sets up the logging client
func Initialize(ctx context.Context, projectID string) error {
	var err error
	client, err = logging.NewClient(ctx, projectID)
	if err != nil {
		return fmt.Errorf("failed to create logging client: %w", err)
	}
	return nil
}

// Close closes the logging client
func Close() error {
	if client != nil {
		return client.Close()
	}
	return nil
}

// <PERSON><PERSON> wraps the GCP Cloud Logging logger
type Logger struct {
	logger *logging.Logger
	name   string
}

// NewLogger creates a new logger instance
func NewLogger(name string) *Logger {
	return &Logger{
		name: name,
	}
}

// logEntry creates a logging entry with proper context and severity
func (l *Logger) logEntry(ctx context.Context, severity string, format string, args ...interface{}) {
	entry := LogEntry{
		Severity:  severity,
		Timestamp: time.Now().UTC().Format(time.RFC3339Nano),
		Message:   fmt.Sprintf(format, args...),
		JSONPayload: map[string]interface{}{
			"message": fmt.Sprintf(format, args...),
		},
		Labels: map[string]string{
			"service": l.name,
		},
	}

	// Add trace context if available
	if traceID := ctx.Value("traceID"); traceID != nil {
		entry.JSONPayload["traceId"] = fmt.Sprintf("%v", traceID)
	}

	// Marshal to JSON and print to stdout
	jsonData, err := json.Marshal(entry)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error marshaling log entry: %v\n", err)
		return
	}
	fmt.Println(string(jsonData))
}

// Info logs an info message
func (l *Logger) Info(ctx context.Context, format string, args ...interface{}) {
	l.logEntry(ctx, "INFO", format, args...)
}

// Error logs an error message
func (l *Logger) Error(ctx context.Context, format string, args ...interface{}) {
	l.logEntry(ctx, "ERROR", format, args...)
}

// Debug logs a debug message
func (l *Logger) Debug(ctx context.Context, format string, args ...interface{}) {
	l.logEntry(ctx, "DEBUG", format, args...)
}

// Warning logs a warning message
func (l *Logger) Warning(ctx context.Context, format string, args ...interface{}) {
	l.logEntry(ctx, "WARNING", format, args...)
}

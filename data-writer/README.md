# Solana Token Transfer Data Writer

This service is responsible for ingesting Solana token transfer events from a Pub/Sub topic and storing them in a PostgreSQL database. It implements the design specified in the [design document](../docs/design-db.md).

## Features

- High-throughput token transfer event ingestion
- Efficient batch processing with PostgreSQL COPY
- Automatic table partitioning by week
- Idempotent writes to prevent duplicates
- Graceful shutdown handling
- Health check endpoint

## Prerequisites

- Go 1.21 or later
- Docker and Docker Compose
- Google Cloud SDK (for Pub/Sub access)

## Local Development Setup

1. Start the PostgreSQL database and run Liquibase migrations:

```bash
docker-compose up -d
```

2. Set up environment variables:

```bash
export DATABASE_URL="postgres://postgres:postgres@localhost:5432/solana_data?sslmode=disable"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"
```

3. Build and run the service:

```bash
go build
./data-writer
```

## Database Migrations

The database schema is managed using Liquibase. To apply migrations:

```bash
docker-compose run --rm liquibase update
```

To rollback changes:

```bash
docker-compose run --rm liquibase rollback <tag>
```

## Configuration

The service can be configured using the following environment variables:

- `DATABASE_URL`: PostgreSQL connection string (required)
- `HEALTH_CHECK_PORT`: Port for health check endpoint (default: 8080)
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to Google Cloud credentials file (required for Pub/Sub)

## Monitoring

The service exposes a health check endpoint at `/health` that returns a 200 OK response when the service is running properly.

## Production Deployment

For production deployment:

1. Ensure the PostgreSQL instance is configured according to the design document specifications
2. Set up appropriate connection pooling and resource limits
3. Configure monitoring and alerting
4. Use a managed Kubernetes cluster for running the service

## Database Maintenance

### Creating New Partitions

Partitions are created automatically by a trigger when new data is inserted. However, you can manually create partitions for future weeks:

```sql
CREATE TABLE token_transfers_y2024w25 PARTITION OF token_transfers
    FOR VALUES FROM ('2024-06-17 00:00:00') TO ('2024-06-24 00:00:00');
```

### Archiving Old Data

To archive old data:

1. Create a new table for the archived data
2. Move the partition to the archive table
3. Detach the partition from the main table

```sql
-- Create archive table
CREATE TABLE token_transfers_archive (LIKE token_transfers);

-- Move partition to archive
ALTER TABLE token_transfers_archive ATTACH PARTITION token_transfers_y2024w24
    FOR VALUES FROM ('2024-06-10 00:00:00') TO ('2024-06-17 00:00:00');

-- Detach from main table
ALTER TABLE token_transfers DETACH PARTITION token_transfers_y2024w24;
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check the `DATABASE_URL` environment variable
   - Verify network connectivity to the database
   - Check PostgreSQL logs for connection errors

2. **Pub/Sub Issues**
   - Verify Google Cloud credentials
   - Check Pub/Sub topic and subscription exist
   - Monitor Pub/Sub metrics for message backlog

3. **Performance Issues**
   - Monitor database connection pool usage
   - Check for long-running transactions
   - Verify partition sizes and index usage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

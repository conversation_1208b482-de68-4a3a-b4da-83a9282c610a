package models

// BaseTransfer contains common fields for both SOL and token transfers
type BaseTransfer struct {
	Signature  []byte
	IxIndex    uint16
	Slot       uint64
	FromWallet []byte
	ToWallet   []byte
	Amount     uint64
}

// SolTransfer represents a SOL transfer event
type SolTransfer struct {
	BaseTransfer
}

// TokenTransfer represents a token transfer event
type TokenTransfer struct {
	BaseTransfer
	TokenMint []byte
}

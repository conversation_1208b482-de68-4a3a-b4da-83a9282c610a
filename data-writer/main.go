//go:generate protoc --go_out=. --go_opt=paths=source_relative proto/events.proto

package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	pb "github.com/kryptogo/kg-solana-data/data-writer/proto"
)

var log *logger.Logger

const (
	numWorkers = 10
)

type dbOperation struct {
	eventType string
	data      interface{}
}

func main() {
	ctx := context.Background()

	// Initialize logger
	if err := logger.Initialize(ctx, "kryptogo-wallet-data"); err != nil {
		panic(fmt.Sprintf("Failed to initialize logger: %v", err))
	}
	defer func() {
		if err := logger.Close(); err != nil {
			fmt.Printf("Error closing logger: %v\n", err)
		}
	}()
	log = logger.NewLogger("data-writer")

	// Initialize database
	if err := db.Initialize(); err != nil {
		log.Error(ctx, "Failed to initialize database: %v", err)
		os.Exit(1)
	}
	defer db.Close()

	// Create PubSub client
	client, err := pubsub.NewClient(ctx, "kryptogo-wallet-data")
	if err != nil {
		log.Error(ctx, "Failed to create pubsub client: %v", err)
		os.Exit(1)
	}
	defer client.Close()

	// Create subscription
	eventsSub := client.Subscription("solana-events-sub")

	// Create a cancellable context for graceful shutdown
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Start message processing
	msgProcessDone := make(chan struct{})
	go func() {
		processMessages(ctx, eventsSub)
		close(msgProcessDone)
	}()

	// Start health check server
	port := os.Getenv("HEALTH_CHECK_PORT")
	if port == "" {
		port = "8080"
	}

	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		if db.Get() == nil {
			w.WriteHeader(http.StatusServiceUnavailable)
			w.Write([]byte("database not initialized"))
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("ok"))
	})

	server := &http.Server{
		Addr: ":" + port,
	}

	go func() {
		log.Info(ctx, "Starting health check server on port %s", port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error(ctx, "Failed to start health check server: %v", err)
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh
	log.Info(ctx, "Shutting down...")

	// Start graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Cancel the main context to stop receiving new messages
	cancel()

	// Shutdown HTTP server
	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Error(ctx, "HTTP server shutdown error: %v", err)
	}

	// Wait for message processing to complete
	select {
	case <-msgProcessDone:
		log.Info(ctx, "Message processing completed")
	case <-shutdownCtx.Done():
		log.Error(ctx, "Shutdown timeout waiting for message processing")
	}

	log.Info(ctx, "Shutdown complete")
}

func processMessages(ctx context.Context, sub *pubsub.Subscription) {
	// Create channels for worker pool
	dbOps := make(chan dbOperation, 100) // Buffer size of 100 for operations
	workerDone := make(chan struct{})
	shutdown := make(chan struct{})

	// Start worker pool
	for i := 0; i < numWorkers; i++ {
		go func(workerID int) {
			log.Info(ctx, "Starting database worker %d", workerID)
			for op := range dbOps {
				database := db.Get()
				// Create a new background context for database operations
				// This ensures operations complete even if the parent context is cancelled
				dbCtx := context.Background()
				switch op.eventType {
				case "sol_transfer":
					if err := database.ProcessSolTransfers(dbCtx, op.data.([]*pb.SolTransferEvent)); err != nil {
						log.Error(ctx, "Worker %d: Error processing SOL transfers: %v", workerID, err)
					} else {
						log.Info(ctx, "Processed %d SOL transfers", len(op.data.([]*pb.SolTransferEvent)))
					}
				case "token_transfer":
					if err := database.ProcessTokenTransfers(dbCtx, op.data.([]*pb.TokenTransferEvent)); err != nil {
						log.Error(ctx, "Worker %d: Error processing token transfers: %v", workerID, err)
					} else {
						log.Info(ctx, "Processed %d token transfers", len(op.data.([]*pb.TokenTransferEvent)))
					}
				}
			}
			log.Info(ctx, "Worker %d completed", workerID)
			workerDone <- struct{}{}
		}(i)
	}

	// Start graceful shutdown handler
	go func() {
		<-ctx.Done()
		log.Info(ctx, "Initiating graceful shutdown...")

		// Wait for a short time to allow any in-flight messages to be processed
		time.Sleep(2 * time.Second)

		// Close the operations channel
		close(dbOps)

		// Wait for all workers to finish
		for i := 0; i < numWorkers; i++ {
			<-workerDone
		}

		log.Info(ctx, "All workers completed, shutdown complete")
		close(shutdown)
	}()

	for {
		select {
		case <-shutdown:
			return
		case <-ctx.Done():
			log.Info(ctx, "Context cancelled, waiting for graceful shutdown...")
			// The shutdown handler will take care of the rest
			<-shutdown
			return
		default:
			err := sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
				// Process message
				if err := processBatchedEvents(ctx, msg.Data, dbOps); err != nil {
					if !errors.Is(err, context.Canceled) {
						log.Error(ctx, "Error processing batched events: %v", err)
					}
					msg.Nack()
					return
				}
				msg.Ack()
			})
			if err != nil {
				if err == context.Canceled {
					log.Info(ctx, "Message processing stopped due to context cancellation")
					return
				}
				log.Error(ctx, "Error receiving messages: %v", err)
				time.Sleep(time.Second)
			}
		}
	}
}

func processBatchedEvents(ctx context.Context, data []byte, dbOps chan<- dbOperation) error {
	var batchedEvent pb.BatchedSolanaEvent
	if err := proto.Unmarshal(data, &batchedEvent); err != nil {
		return fmt.Errorf("failed to unmarshal BatchedSolanaEvent: %v", err)
	}

	// Collect events by type for batch processing
	solTransfers := make([]*pb.SolTransferEvent, 0)
	tokenTransfers := make([]*pb.TokenTransferEvent, 0)

	// Process each event in the batch
	for _, event := range batchedEvent.Events {
		switch e := event.Event.(type) {
		case *pb.SolanaEvent_SolTransfer:
			solTransfers = append(solTransfers, e.SolTransfer)
		case *pb.SolanaEvent_TokenTransfer:
			tokenTransfers = append(tokenTransfers, e.TokenTransfer)
		}
	}

	// Send operations to worker pool
	if len(solTransfers) > 0 {
		select {
		case dbOps <- dbOperation{eventType: "sol_transfer", data: solTransfers}:
		case <-ctx.Done():
			return ctx.Err()
		}
	}

	if len(tokenTransfers) > 0 {
		select {
		case dbOps <- dbOperation{eventType: "token_transfer", data: tokenTransfers}:
		case <-ctx.Done():
			return ctx.Err()
		}
	}

	return nil
}

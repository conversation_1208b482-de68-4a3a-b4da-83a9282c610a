apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: data-writer-hpa
  namespace: solana-data
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: data-writer
  minReplicas: 1
  maxReplicas: 5
  metrics:
    - type: External
      external:
        metric:
          name: pubsub.googleapis.com|subscription|num_undelivered_messages
          selector:
            matchLabels:
              resource.labels.subscription_id: solana-events-sub
        target:
          type: AverageValue
          averageValue: "10"
  behavior:
    scaleUp:
      # Allows for rapid scaling to handle sudden message spikes.
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 100 # Double the pod count every 60 seconds if needed.
          periodSeconds: 60
        - type: Pods
          value: 2 # Never scale up by more than 2 pods at a time
          periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 60 # Wait 1 minute before initiating a scale-down
      policies:
        - type: Percent
          value: 25 # Reduce pod count by 25% at most every 60 seconds
          periodSeconds: 60
        - type: Pods
          value: 2 # Never scale down by more than 2 pods at a time
          periodSeconds: 60

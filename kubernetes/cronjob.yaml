apiVersion: batch/v1
kind: CronJob
metadata:
  name: create-partition
  namespace: solana-data
spec:
  schedule: "0 0 */4 * *" # Run at midnight every 4 days
  concurrencyPolicy: Forbid # Don't run if previous job is still running
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: default-ksa
          containers:
            - name: create-partition
              image: asia-northeast1-docker.pkg.dev/kryptogo-wallet-data/kg-solana-data/data-writer:latest
              imagePullPolicy: Always
              workingDir: /app
              command: ["/app/create-partition"]
              resources:
                requests:
                  cpu: "500m"
                  memory: "512Mi"
                limits:
                  cpu: "1"
                  memory: "1Gi"
              env:
                - name: DB_HOST
                  value: "**********"
                - name: DB_PORT
                  value: "5432"
                - name: DB_USER
                  value: "solana_data"
                - name: DB_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: db-credentials
                      key: password
                - name: DB_NAME
                  value: "solana_data"
                - name: DB_SSL_MODE
                  value: "require"
          restartPolicy: OnFailure
